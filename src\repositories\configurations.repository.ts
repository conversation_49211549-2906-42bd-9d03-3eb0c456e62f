import { Inject, Injectable } from '@nestjs/common';
import { CONFIGURATION_REPOSITORY } from 'src/constants/entities';
import { k500Error } from 'src/constants/misc';
import { AppConfigurations } from 'src/entities/configuration.entity';
import { RepositoryManager } from './repository.manager';

@Injectable()
export class ConfigurationRepository {
  constructor(
    @Inject(CONFIGURATION_REPOSITORY)
    private readonly repository: typeof AppConfigurations,
    private readonly repoManager: RepositoryManager,
  ) {}

  async getRowWhereData(attributes: string[], options: any) {
    try {
      return await this.repoManager.getRowWhereData(
        this.repository,
        attributes,
        options,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async updateRowData(updatedData: any, id: number) {
    try {
      return this.repoManager.updateRowData(this.repository, updatedData, id);
    } catch (error) {
      return k500Error;
    }
  }
}

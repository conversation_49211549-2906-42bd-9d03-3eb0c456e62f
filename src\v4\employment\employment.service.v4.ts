// Imports
import * as fs from 'fs';
import * as FormData from 'form-data';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  GLOBAL_RANGES,
  REKYCDAYS,
  SYSTEM_ADMIN_ID,
  GLOBAL_FLOW,
  validAppTypeValues,
  GlobalServices,
  NAME_MISS_MATCH_PER,
  gIsPROD,
  PRE_APPROVAL_COMPANY_NAME,
  MAX_LENGTH_SALARY_AMOUNT,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  PreApproveStatus,
  SALARY_EXPRESSION,
  UATNumberSeries,
  UserStage,
  kDummyUserIds,
  kEmploymentFields,
  kExceptionsDomains,
  kFirstTimeEmploymentFields,
  kSameEmploymentFields,
  kVerificationAccessStatus,
} from 'src/constants/objects';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import {
  kGlobalTrail,
  kNoDataFound,
  kSubmissionOfEmploymentNotCom,
  kTEmailOtp,
  kVerificationsMail,
  COMPANY_STATUS_IS_NOT_ACTIVE,
  kLoanNotProgress,
  kCanNotSelectThisCompany,
  ENTERED_SALARY_IS_BELOW,
  kEmploymentRoute,
  kNetBankingRoute,
  kErrorMsgs,
  BANKINGADMINS,
  kMaxAgeCriteria,
  kMinAgeCriteria,
  kDisbursementInProcess,
  kEPFOService,
  BAD_CIBIL_SCORE_MSG,
} from 'src/constants/strings';
import { employmentDetails } from 'src/entities/employment.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { BankingRepository } from 'src/repositories/banking.repository';
import { EmployementDegignationRepository } from 'src/repositories/degignation.repository';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { EmploymentHistoryRepository } from 'src/repositories/employmentHistory.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { EmployementSectoreRepository } from 'src/repositories/sector.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { LogsSharedService } from 'src/shared/logs.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { UserSharedService } from 'src/shared/user.share.service';
import { GoogleService } from 'src/thirdParty/google/google.service';
import { InstaFinancialService } from 'src/thirdParty/instafinancial/instafinancial.service';
import { CommonService } from 'src/utils/common.service';
import { TypeService } from 'src/utils/type.service';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { APIService } from 'src/utils/api.service';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { AdminService } from 'src/admin/admin/admin.service';
import { nValidateOfferLetter } from 'src/constants/network';
import { employmentDesignation } from 'src/entities/designation.entity';
import { employmentSector } from 'src/entities/sector.entity';
import { BankingSharedService } from 'src/shared/banking.service';
import { UserServiceV4 } from '../user/user.service.v4';
import { PredictionService } from 'src/admin/eligibility/prediction.service';
import { KYCEntity } from 'src/entities/kyc.entity';
import { BankingServiceV4 } from '../banking/banking.service.v4';
import { FileService } from 'src/utils/file.service';
import { RedisService } from 'src/redis/redis.service';
import { MandateSharedService } from 'src/shared/mandate.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { Experiment } from 'src/entities/experiment_schema';
import { CibilService } from 'src/shared/cibil.service';
import { DynamicEntity } from 'src/entities/dynamic.entity';
import { ProteanService } from 'src/thirdParty/protean/protean.servics';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { CryptService } from 'src/utils/crypt.service';
import { ValidationService } from 'src/utils/validation.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { EmploymentSharedService } from 'src/shared/employment.shared.service';
import { BlockUserHistoryRepository } from 'src/repositories/user.blockHistory.repository';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';
@Injectable()
export class EmploymentServiceV4 {
  constructor(
    @Inject(forwardRef(() => AdminService))
    private readonly adminService: AdminService,
    private readonly api: APIService,
    private readonly CompanyRepo: CompanyRepository,
    private readonly bankingRepo: BankingRepository,
    private readonly commonService: CommonService,
    private readonly designationRepo: EmployementDegignationRepository,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly sharedEligibility: EligibilitySharedService,
    private readonly empHistoryRepo: EmploymentHistoryRepository,
    private readonly googleService: GoogleService,
    private readonly instaFinancial: InstaFinancialService,
    private readonly proteanService: ProteanService,
    private readonly loanRepo: LoanRepository,
    private readonly masterRepo: MasterRepository,
    private readonly repository: EmploymentRepository,
    private readonly salarySlipRepo: SalarySlipRepository,
    private readonly sectorRepo: EmployementSectoreRepository,
    private readonly userBlockHistoryRepo: BlockUserHistoryRepository,

    private readonly typeService: TypeService,
    private readonly cryptService: CryptService,
    private readonly userRepo: UserRepository,
    private readonly workEmailRepo: WorkMailRepository,
    private readonly notificationService: SharedNotificationService,
    private readonly userSharedService: UserSharedService,
    private readonly logTracker: LogsSharedService,
    private readonly commonSharedService: CommonSharedService,
    private readonly kycRepo: KYCRepository,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly eligiblityService: EligibilitySharedService,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly sharedBankingService: BankingSharedService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly predictionService: PredictionService,
    private readonly bankinService: BankingServiceV4,
    private readonly sharedMandate: MandateSharedService,
    private readonly cibilService: CibilService,
    private readonly validation: ValidationService,
    private readonly caAssignmentService: CAAssignmentService,
    // Utils
    private readonly fileService: FileService,
    private readonly redisService: RedisService,
    private readonly employmentSharedService: EmploymentSharedService,
    private readonly repoManager: RepositoryManager,
    private readonly errorContextService: ErrorContextService,
  ) {}

  async searchCompany(reqData) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    let searchStr = reqData?.searchStr?.toLowerCase()?.trim();
    if (!searchStr) return kParamMissing('searchStr');
    const appType = reqData.appType;

    if (!validAppTypeValues.includes(appType)) {
      const userData = await this.userRepo.getRowWhereData(['appType'], {
        where: { id: userId },
      });
      if (!userData || userData?.message) return kInternalError;
      reqData.appType = userData.appType;
    }

    let list: any = [];
    list = await this.instaFinancial.searchCompany(searchStr);
    // if (!list?.length || list?.message)
    //   list = await this.proteanService.searchCompany(reqData);
    //filter pre approval company
    list = list.filter((company) => company != PRE_APPROVAL_COMPANY_NAME);

    if (list.message) return kInternalError;
    if (list?.length == 0) {
      // Store Other attempt
      new Promise(async (resolve, _) => {
        const creationData = {
          type: 'COMPANY_SEARCH_DEFAULT_OTHERS',
          userId,
          value: searchStr,
        };
        await this.repoManager.createRowData(Experiment, creationData);
        resolve({});
      }).catch((err) => {});

      return [searchStr, 'OTHERS'];
    }

    return [...new Set(list)];
  }

  async getNecessaryList() {
    // Get sectors
    const sectors = await this.getSectorList();
    if (sectors.message) return sectors;

    // Get designations
    const designations = await this.getDesignationList();
    if (designations.message) return designations;

    const topCompanies = await this.getTop10VerifiedCompanies();

    return { designations, sectors, topCompanies };
  }

  private async getSectorList() {
    try {
      const attributes = ['id', 'sectorName'];
      const options = { where: { sectorStatusVerified: '1' } };
      const sectorList: any = this.sectorRepo.getTableWhereData(
        attributes,
        options,
      );
      if (sectorList == k500Error) return kInternalError;
      return sectorList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getDesignationList() {
    try {
      const attributes = ['id', 'designationName'];
      const options = { where: { designationStatusVerified: '1' } };
      const designationList = await this.designationRepo.getTableWhereData(
        attributes,
        options,
      );
      if (designationList == k500Error) return kInternalError;
      return designationList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getTop10VerifiedCompanies() {
    // Currently no need to show the top companies
    return [];
    const rawQuery = `SELECT lower("companyName") AS "companyName", COUNT("companyName") AS "count" 
    FROM "employmentDetails"
    WHERE ("employmentDetails"."companyVerification" = '1' OR "employmentDetails"."companyVerification" = '3') 
    GROUP BY lower("companyName") 
    ORDER BY "count" DESC LIMIT 10`;
    const outputList = await this.repoManager.injectRawQuery(
      employmentDetails,
      rawQuery,
      { source: 'REPLICA' },
    );
    if (outputList == k500Error) throw new Error();

    const companyList = outputList.map(
      (el) =>
        el?.companyName.charAt(0).toUpperCase() + el?.companyName.slice(1),
    );

    return companyList.sort();
  }

  async submitDetails(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      const update = reqData?.updateEmployment ?? false;
      const typeOfDevice = reqData?.typeOfdevice;
      const isEmploymentSkipForFirstTime =
        reqData?.isEmploymentSkipForFirstTime ?? false;
      const isInstantCashFlow = reqData?.isInstantCashFlow ?? false;
      let companyName = reqData.companyName?.trim()?.toUpperCase();
      if (!companyName && !update) return kParamMissing('companyName');

      if (companyName) {
        // Prevention -> User stuck (Lead generation)
        const companyAttr = ['id'];
        const companyOptions = { where: { companyName: companyName } };
        const companyData = await this.repoManager.getRowWhereData(
          GoogleCompanyResultEntity,
          companyAttr,
          companyOptions,
        );
        if (companyData == k500Error) throw new Error();
        if (!companyData) {
          const userCompany = companyName;
          companyName = 'OTHERS';
          reqData.companyName = companyName;
          // Store Manual company name
          new Promise(async (resolve, _) => {
            const creationData = {
              type: 'COMPANY_MANUAL_SEARCH',
              userId,
              value: userCompany,
            };
            await this.repoManager.createRowData(Experiment, creationData);
            resolve({});
          }).catch((err) => {});
        }
      }
      const sectorId = reqData?.sectorId ?? null;
      if (!sectorId && !update && !isEmploymentSkipForFirstTime)
        return kParamMissing('sectorId');
      const designationId = reqData?.designationId ?? null;
      const expressReapply = reqData?.expressReapply;
      if (!designationId && !update && !isEmploymentSkipForFirstTime)
        return kParamMissing('designationId');

      let empStartDate = reqData.empStartDate ?? null;
      if (!empStartDate && !update && !isEmploymentSkipForFirstTime)
        return kParamMissing('empStartDate');
      empStartDate = empStartDate
        ? new Date(empStartDate + kGlobalTrail)
        : null;
      if (isNaN(empStartDate) && !update && !isEmploymentSkipForFirstTime)
        return kInvalidParamValue('empStartDate');
      const currDateTime = new Date();
      let lastPayDate = reqData?.lastPayDate ?? null;
      let nextPayDate = reqData?.nextPayDate ?? null;
      if (!isEmploymentSkipForFirstTime) {
        if (isInstantCashFlow) {
          await this.userService.submitBasicDetails({
            userId,
            purposeId: reqData?.purposeId,
          });

          const userInclude: any = {
            model: registeredUsers,
            attributes: ['monthlySalaryDetails'],
          };
          const empAttr = [
            'id',
            'otherInfo',
            'companyName',
            'updatedCompanyName',
          ];
          const empOptions = { where: { userId }, include: [userInclude] };
          const existingData = await this.repository.getRowWhereData(
            empAttr,
            empOptions,
          );
          if (existingData == k500Error) return kInternalError;
          companyName = (
            companyName ??
            existingData?.updatedCompanyName ??
            existingData?.companyName
          )
            ?.trim()
            ?.toUpperCase();

          const salaryData =
            existingData?.user?.monthlySalaryDetails?.salaryData ?? [];
          let lastSalaryDate = salaryData.sort((a, b) => {
            const dateA: any = new Date(
              a.monthYear.split('/').reverse().join('-'),
            );
            const dateB: any = new Date(
              b.monthYear.split('/').reverse().join('-'),
            );
            return dateB - dateA;
          })[0].monthYear;

          lastSalaryDate = lastSalaryDate.split('/').reverse().join('-');
          lastPayDate = new Date(lastSalaryDate);
          nextPayDate = new Date(lastSalaryDate);
          nextPayDate.setMonth(nextPayDate.getMonth() + 1);
        }

        if (!lastPayDate) return kParamMissing('lastPayDate');
        lastPayDate = new Date(lastPayDate);
        currDateTime.setHours(23, 59, 59, 999);

        // Temporary changes
        if (
          lastPayDate.getTime() > currDateTime.getTime() ||
          isNaN(lastPayDate)
        ) {
          lastPayDate = new Date(reqData?.nextPayDate ?? '');
          lastPayDate.setFullYear(lastPayDate.getFullYear() - 1);
        }
        if (
          lastPayDate.getTime() > currDateTime.getTime() ||
          isNaN(lastPayDate)
        ) {
          return k422ErrorMessage('Last pay date must be past date.');
        }

        if (!nextPayDate) return kParamMissing('nextPayDate');
        nextPayDate = new Date(nextPayDate);
        currDateTime.setHours(0, 0, 0, 1);
        if (
          nextPayDate.getTime() < currDateTime.getTime() ||
          isNaN(nextPayDate)
        ) {
          // Temporary changes
          nextPayDate.setMonth(nextPayDate.getMonth() + 1);
          if (nextPayDate.getTime() < currDateTime.getTime())
            nextPayDate.setMonth(nextPayDate.getMonth() + 1);
          // return k422ErrorMessage('Next pay date must be future date.');
        }

        // Temporary disabled due to LSP APP side issue
        // if (lastPayDate?.toString() == nextPayDate?.toString())
        //   return k422ErrorMessage(
        //     'Last pay date and next pay date cannot be same.',
        //   );
      }
      const reApplyFlow = reqData.reApply ?? false;
      let netPaySalary = +(
        this.typeService.checkAndRemoveComma(reqData?.netPaySalary ?? '') ?? 0
      );

      if (isEmploymentSkipForFirstTime || !netPaySalary) {
        const msOps = { where: { userId }, order: [['id', 'DESC']] };
        const masterData = await this.masterRepo.getRowWhereData(
          ['id', 'otherInfo'],
          msOps,
        );
        if (!masterData) return k422ErrorMessage(kNoDataFound);
        netPaySalary = +(masterData?.otherInfo?.salaryInfo ?? 0);
      }

      let salaryInfo = reqData?.salaryInfo ?? netPaySalary;
      reqData.salaryInfo = salaryInfo;

      // Cool-off to user for specific sectors
      if (sectorId) {
        const options = {
          where: {
            id: sectorId,
          },
        };
        const sector = await this.repoManager.getRowWhereData(
          employmentSector,
          ['sectorName'],
          options,
        );
        const sectorName = sector?.sectorName;
        let reason = `User is ${sectorName}`;
        if (sectorName == 'Unemployed' || sectorName == 'Homemaker') {
          let nextApplyDate = new Date();
          nextApplyDate.setDate(nextApplyDate.getDate() + 30);
          let coolOffData: any = {
            userId,
            type: '2',
            nextApplyDate,
            adminId: SYSTEM_ADMIN_ID,
            status: '0',
            reason,
          };
          const blackListRes = await this.adminService.changeBlacklistUser(
            coolOffData,
          );
          if (blackListRes?.message == kDisbursementInProcess)
            return blackListRes;

          return { needUserInfo: true };
        } else if (sectorName == 'Retired') {
          const checkBlackListData: any =
            await this.adminService.changeBlacklistUser({
              userId,
              adminId: SYSTEM_ADMIN_ID,
              type: '1',
              reason,
              status: '1',
              nextApplyDate: null,
            });

          if (checkBlackListData?.message == kDisbursementInProcess)
            return checkBlackListData;

          return { needUserInfo: true };
        }
      }

      const updateLoanData = {
        lastPayDate: lastPayDate ? lastPayDate.toJSON() : null,
        nextPayDate: nextPayDate ? nextPayDate.toJSON() : null,
        ...(netPaySalary ? { netPaySalary } : {}),
      };
      // Prepare data
      const todayDate = this.typeService.getGlobalDate(new Date());
      const salaryDate = lastPayDate ? lastPayDate.getDate() : null;
      let companyVerification = '1';
      const empData: any = {
        companyAddress: '',
        companyUrl: '',
        companyPhone: '',
        companyVerification,
        companyName,
        employmentTypeId: 1,
        sectorId,
        designationId,
        masterId: null,
        salary: '',
        salaryDate,
        userId,
        verifiedDate: todayDate.toJSON(),
        otherInfo: {
          ...updateLoanData,
        },
        companyNameChangeBy: null,
        updatedCompanyName: null,
      };
      if (
        isEmploymentSkipForFirstTime &&
        companyName != PRE_APPROVAL_COMPANY_NAME
      ) {
        let epfoService = await this.redisService.get(kEPFOService);
        if (typeof epfoService != 'string')
          epfoService = GlobalServices.EPFO_SERVICE;
        empData.otherInfo.thirdPartyVerified = epfoService;
      }

      let checkCompany: any = true;
      // Get company details from google apis
      if (!update) {
        const queryData = { needDetails: true, searchText: companyName };
        const companyData = await this.googleService.searchOrganisation(
          queryData,
        );
        // Not handling errors because user should not get affected for this error
        if (companyData && !companyData.message) {
          empData.companyAddress = companyData.formatted_address ?? '';
          empData.companyUrl = companyData.website ?? '';
          empData.companyPhone = companyData.international_phone_number ?? '';
        }
      }

      checkCompany = await this.employmentSharedService.verifyAndStoreInfo(
        companyName,
        userId,
      );
      if (checkCompany?.message || checkCompany == k500Error) {
        companyName = 'OTHERS';
        reqData.companyName = companyName;
      }

      // Skip insta financial in case of error
      if (checkCompany?.message) checkCompany = true;
      if (checkCompany?.message) {
        return checkCompany;
      }
      if (checkCompany === false && companyName != 'OTHERS')
        companyVerification = '2';

      // Get user data
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = [
        'coolOffData',
        'otherInfo',
        'status',
        'dates',
        'miscData',
        'empId',
        'workMailId',
        'salarySlipId',
        'loanId',
      ];
      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = [
        'aadhaarDOB',
        'aadhaarState',
        'aadhaarAddress',
        'aadhaarAddressResponse',
      ];
      const include = [masterInclude, kycInclude];
      const attributes = [
        'id',
        'gender',
        'masterId',
        'recentDeviceId',
        'completedLoans',
        'leadId',
        'state',
        'categoryScore',
        'appType',
        'isCibilConsent',
        'typeOfDevice',
        'fullName',
        'email',
        'phone',
        'hashPhone',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const statusData = userData.masterData?.status ?? {};
      if (!salaryInfo)
        salaryInfo = userData.masterData?.otherInfo?.salaryInfo ?? 0;
      const masterId = userData.masterId;

      //first time user
      if (
        userData?.completedLoans == 0 &&
        netPaySalary < GLOBAL_RANGES.MIN_EMPLOYMENT_SALARY
      )
        return k422ErrorMessage('Please enter valid salary');
      //repeat user
      else if (
        userData?.completedLoans > 0 &&
        netPaySalary < GLOBAL_RANGES.MIN_SALARY_AMOUNT
      )
        return k422ErrorMessage('Please enter valid salary');

      if (!salaryInfo) {
        // code comment due to missing repeater proffetional salaryInfo
        // statusData.professional = -1;
        // const updateResult = await this.masterRepo.updateRowData(
        //   { status: statusData },
        //   masterId,
        // );
        // if (updateResult == k500Error) return kInternalError;
        // return { needUserInfo: true };
      }
      empData.salary = salaryInfo;
      empData.masterId = masterId;

      // Double check -> Minimum salary criteria
      const userEnteredSalary =
        (reqData.salaryInfo ?? 0) > 0 ? reqData.salaryInfo : +salaryInfo;

      const checkResult = await this.doubleCheckMinSalary({
        masterId,
        userData,
        userEnteredSalary,
        userId,
      });
      if (checkResult?.needUserInfo) return checkResult;

      const miscData = userData.masterData?.miscData ?? {};

      if (
        statusData.loan != 2 &&
        statusData.loan != 7 &&
        statusData.loan != -2 &&
        statusData.loan != -1 &&
        !kDummyUserIds.includes(userId)
      )
        return k422ErrorMessage(kSubmissionOfEmploymentNotCom);
      let empId;
      // Existing data
      const empOptions = { where: { userId } };
      const existingData = await this.repository.getRowWhereData(
        null,
        empOptions,
      );
      if (existingData == k500Error) return kInternalError;

      // if user select existing company on reapply route
      if (statusData?.company == 2) {
        if (
          (companyName ?? '').toLowerCase().trim() ==
            (existingData?.companyName ?? '').toLowerCase() &&
          companyName !== 'OTHERS' &&
          existingData?.companyName !== 'OTHERS'
        ) {
          let nextApplyDate = new Date();
          let reason = COMPANY_STATUS_IS_NOT_ACTIVE;
          nextApplyDate.setDate(nextApplyDate.getDate() + 60);
          let coolOffData: any = {
            userId,
            type: '2',
            nextApplyDate,
            adminId: SYSTEM_ADMIN_ID,
            status: '0',
            reason,
            reasonId: 55,
          };
          const blackListRes = await this.adminService.changeBlacklistUser(
            coolOffData,
          );
          if (blackListRes?.message == kDisbursementInProcess)
            return blackListRes;
        }
      }

      // Move existing record into history and update new record
      if (existingData) {
        empId = existingData.id;
        // add previous company data if selected old company
        if (update) {
          empData.companyAddress = existingData.companyAddress;
          empData.companyUrl = existingData.companyUrl;
          empData.companyPhone = existingData.companyPhone;
          empData.companyVerification = existingData.companyVerification;
          empData.companyName = existingData.companyName;
          empData.employmentTypeId = existingData.employmentTypeId;
          empData.sectorId = reqData?.sectorId || existingData.sectorId;
          empData.designationId =
            reqData?.designationId || existingData.designationId;
          empData.startDate = existingData.startDate;
          empData.salary = netPaySalary;
          reqData.empInfo = userData?.masterData?.otherInfo?.employmentInfo;
          reqData.salaryInfo =
            salaryInfo ?? userData?.masterData?.otherInfo?.salaryInfo;
          empData.otherInfo = {
            ...existingData.otherInfo,
            ...updateLoanData,
          };
          empData.companyNameChangeBy = existingData?.companyNameChangeBy;
          empData.updatedCompanyName = existingData?.updatedCompanyName;
        }
        existingData.id = undefined;

        const createdData = await this.empHistoryRepo.createRowData(
          existingData,
        );
        if (createdData == k500Error) return kInternalError;
        if (!update) {
          empData.workMailId = null;
          empData.salarySlipId = null;
        }
        const updateResult = await this.repository.updateRowData(
          empData,
          empId,
        );
        if (updateResult == k500Error) return kInternalError;
        const empDetailsKey = 'EMP_DETAILS' + userId;
        await this.redisService.del(empDetailsKey);
      }
      // Create new record
      else {
        empData.companyVerification = companyVerification;
        const createdData = await this.repository.createRowData(empData);
        if (createdData == k500Error) return kInternalError;
        empId = createdData.id;
      }

      // Update master data
      // Need to reset the below status in case company added again
      statusData.company = +companyVerification;
      statusData.salarySlip = GLOBAL_FLOW.WORK_MAIL_SALARYSLIP_SKIP ? 4 : -1;
      statusData.workMail = GLOBAL_FLOW.WORK_MAIL_SALARYSLIP_SKIP ? 4 : -1;
      miscData.workMailSalarySlipSkip = GLOBAL_FLOW.WORK_MAIL_SALARYSLIP_SKIP
        ? true
        : false;
      miscData.needSalarySlip = true;
      const otherInfo = userData?.masterData?.otherInfo;
      if (!isEmploymentSkipForFirstTime)
        otherInfo.userEnterCompanyName = companyName;
      if (netPaySalary) {
        otherInfo.netPaySalary = netPaySalary;
        otherInfo.salaryInfo = netPaySalary;
      }

      const updatedData: any = {
        empId,
        status: statusData,
        otherInfo,
        salarySlipId: null,
        workMailId: null,
        miscData,
      };
      if (typeof expressReapply === 'boolean')
        updatedData.expressReapply = expressReapply;
      const updateResult = await this.masterRepo.updateRowData(
        updatedData,
        masterId,
      );
      if (updateResult === k500Error) return kInternalError;
      // Validate blacklisted companies
      const isEligibleCompany: any =
        await this.sharedEligibility.isEligibleCompany(empData);

      if (isEligibleCompany.message) {
        return isEligibleCompany;
      }
      if (!isEligibleCompany) return { needUserInfo: true };
      if (checkCompany === false && companyName != 'OTHERS')
        return { needUserInfo: true };

      let purposeId = miscData.purposeId;
      if (purposeId == 0) purposeId = miscData.nextLoanPurposeId;
      if (typeof purposeId == 'string') purposeId = +purposeId;
      const data = { userId, purposeId, reApply: true };
      if (typeOfDevice) userData.typeOfDevice = typeOfDevice;

      const result = await this.handleReApplyFlow(
        statusData,
        data,
        userData,
        updateLoanData,
        companyName,
        expressReapply,
      );

      if (result?.message) return result;

      const isUnderAge = result?.isUnderAge ?? false;
      if (reqData?.empInfo && isUnderAge) {
        const submitData =
          await this.userSharedService.submitProfessionalDetails(reqData);
        if (submitData?.message) {
          return submitData;
        }
      }

      // check name validations
      await this.sharedEligibility.nameValidation(userId);

      let aadhaarState = userData?.kycData?.aadhaarState;
      if (!aadhaarState) {
        const aadhaarAddress = await this.typeService.getAadhaarAddress(
          userData.kycData,
        );
        aadhaarState = aadhaarAddress?.state;
      }

      // store category score if not there
      if (!userData.categoryScore) {
        await this.commonSharedService.getRiskCategoryByScoreData({
          userId,
          masterData: {
            otherInfo: result.otherInfo ?? userData.masterData.otherInfo,
          },
          aadhaarState,
          aadhaarDOB: userData.kycData.aadhaarDOB,
          liveState: userData.state,
        });
      }

      //if user applying again in 100 days then skip workmail and salarySlip
      if (reApplyFlow) {
        const salarySlipAtr = ['id', 'salarySlipDate', 'status', 'companyName'];
        const salarySlipOptions = {
          where: {
            userId: userId,
          },
          order: [['id', 'DESC']],
        };
        const salarySlipData = await this.salarySlipRepo.getRowWhereData(
          salarySlipAtr,
          salarySlipOptions,
        );
        if (salarySlipData === k500Error) return kInternalError;
        let salarySlipDate =
          salarySlipData?.salarySlipDate ?? '1970-01-01' + kGlobalTrail;
        salarySlipDate = new Date(salarySlipDate);
        const slarySlipStatus = salarySlipData?.status;
        const today = this.typeService.getGlobalDate(new Date());
        const diffInDays = this.typeService.dateDifference(
          today,
          salarySlipDate,
        );
        if (diffInDays > GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS)
          miscData.needSalarySlip = true;
        else if (
          diffInDays < GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS &&
          (slarySlipStatus === '1' || slarySlipStatus === '3') &&
          companyName == salarySlipData?.companyName
        ) {
          //for skiping salary slip  and work mail if user reapply again in less than 100 days and salaryslip verified previously.
          const newMasterData = await this.masterRepo.getRowWhereData(
            ['id', 'empId', 'status', 'miscData'],
            { where: { userId }, order: [['id', 'DESC']] },
          );
          if (newMasterData == k500Error) return kInternalError;

          const workMailData = {
            email: '',
            masterId: newMasterData?.id,
            status: '4',
            userId,
            approveById: SYSTEM_ADMIN_ID,
          };
          const workMailCreatedData = await this.workEmailRepo.createRowData(
            workMailData,
          );
          if (workMailCreatedData == k500Error) return kInternalError;
          const workMailId = workMailCreatedData.id;

          // Update employment data
          const empData: any = { workMailId };
          const updateEmpResult = await this.repository.updateRowData(
            empData,
            newMasterData?.empId,
          );
          if (updateEmpResult == k500Error) return kInternalError;
          const newStatusData = newMasterData?.status;
          newStatusData.salarySlip = 4;
          newStatusData.workMail = 4;
          const newMiscData = newMasterData?.miscData;
          newMiscData.needSalarySlip = false;

          const updatedMasterData = {
            miscData: newMiscData,
            status: newStatusData,
            workMailId,
            workMailAdminId: SYSTEM_ADMIN_ID,
          };

          const masterRes = await this.masterRepo.updateRowData(
            updatedMasterData,
            newMasterData?.id,
          );
          if (masterRes === k500Error) return kInternalError;
        }
      }

      if (GLOBAL_FLOW.BANKING_AT_FIRST && statusData.dynamicBank == 1) {
        statusData.dynamicBank = 4;
        await this.repoManager.updateRowData(
          MasterEntity,
          { status: statusData },
          masterId,
        );
        const dataAttr = ['data'];
        const dataOptions = { order: [['id', 'DESC']], where: { userId } };
        const dynamicData = await this.repoManager.getRowWhereData(
          DynamicEntity,
          dataAttr,
          dataOptions,
        );
        if (dynamicData?.data && dynamicData != k500Error) {
          const reqData = dynamicData.data;
          return await this.sharedBankingService.validateEligibility(reqData);
        }
      }

      if (isInstantCashFlow) {
        const bankAttr = [
          'id',
          'accountDetails',
          'bankStatement',
          'consentId',
          'consentHandleId',
          'consentURL',
          'consentStatus',
          'consentResponse',
          'consentPhone',
          'consentMode',
          'accountID',
          'aaDataStatus',
        ];
        const bankOptions = {
          where: {
            userId,
          },
          order: [['id', 'DESC']],
        };
        const bankData = await this.bankingRepo.getRowWhereData(
          bankAttr,
          bankOptions,
        );
        if (bankData == k500Error) return kInternalError;

        const sessionData = await this.repoManager.getRowWhereData(
          PeriodicEntity,
          ['sessionId', 'data'],
          { where: { userId }, order: [['createdAt', 'DESC']] },
        );
        if (sessionData == k500Error) throw new Error();
        const accountDetails = bankData?.accountDetails
          ? JSON.parse(bankData.accountDetails)
          : {};
        const data = {
          accountDetails: { ...accountDetails, aaService: true },
          filePath: sessionData?.data?.fileUrl,
          userId,
          instantCashFlow: {
            sessionId: sessionData?.sessionId,
            consentId: bankData?.consentId,
            consentHandleId: bankData?.consentHandleId,
            consentURL: bankData?.consentURL,
            consentStatus: bankData?.consentStatus,
            consentResponse: bankData?.consentResponse,
            consentPhone: bankData?.consentPhone,
            consentMode: bankData?.consentMode,
            accountID: bankData?.accountID,
            aaDataStatus: bankData?.aaDataStatus,
          },
        };
        await this.sharedBankingService.validateEligibility(data);
      }

      if (kDummyUserIds.includes(userId) && reApplyFlow) {
        const bankAttr = ['id', 'accountDetails', 'bankStatement'];
        const bankOptions = {
          where: {
            userId,
          },
          order: [['id', 'DESC']],
        };
        const bankData = await this.bankingRepo.getRowWhereData(
          bankAttr,
          bankOptions,
        );
        if (bankData == k500Error) return kInternalError;
        const data = {
          accountDetails: bankData?.accountDetails,
          filePath: bankData?.bankStatement,
          userId,
          forceContinue: true,
        };
        await this.sharedBankingService.validateEligibility(data);
      }

      return { needUserInfo: true };
    } catch (error) {
      return kInternalError;
    }
  }

  private async doubleCheckMinSalary({
    userEnteredSalary,
    userData,
    masterId,
    userId,
  }) {
    if (!masterId || !userId || !userEnteredSalary) return {};

    if (userEnteredSalary && !isNaN(userEnteredSalary)) {
      //if new user and salary less 25000 the user cooloff till 3 month
      let userMinSalary = GLOBAL_RANGES.MIN_SALARY_AMOUNT;
      if (userData?.completedLoans == 0 && userEnteredSalary < 25000)
        userMinSalary = 25000;

      if (userEnteredSalary < userMinSalary) {
        if (
          !GLOBAL_FLOW.SALARY_COOLOFF_BEFORE_STATEMENT &&
          userData?.appType == 0 &&
          userEnteredSalary >= GLOBAL_RANGES.MIN_EMPLOYMENT_SALARY
        ) {
          const otherInfornmation = userData?.masterData?.otherInfo ?? {};
          otherInfornmation.salaryInfo = +userEnteredSalary;
          const updateMasterData = { otherInfornmation };
          const updateMasterResult = await this.masterRepo.updateRowData(
            updateMasterData,
            masterId,
          );
          if (updateMasterResult === k500Error) throw new Error();
          return {};
        }
        // Update master data
        const coolOffData = userData.masterData?.coolOffData ?? {};
        coolOffData.count = (coolOffData.count ?? 0) + 1;
        coolOffData.reason = ENTERED_SALARY_IS_BELOW;
        const toDay = this.typeService.getGlobalDate(new Date());
        coolOffData.coolOffStartedOn = toDay.toJSON();
        toDay.setDate(toDay.getDate() + 90);
        coolOffData.coolOffEndsOn = toDay.toJSON();
        coolOffData.lastStep = UserStage.EMPLOYMENT;
        const otherInfo = userData?.masterData?.otherInfo ?? {};
        otherInfo.salaryInfo = +userEnteredSalary;
        const masterUpdateData = { coolOffData, otherInfo };
        const masterUpdateResult = await this.masterRepo.updateRowData(
          masterUpdateData,
          masterId,
        );
        if (masterUpdateResult === k500Error) throw new Error();

        // Update user data
        const userUpdateData = { NextDateForApply: toDay };
        const userUpdateResult = await this.userRepo.updateRowData(
          userUpdateData,
          userId,
        );
        if (userUpdateResult == k500Error) throw new Error();

        const historyData = {
          userId,
          reason: ENTERED_SALARY_IS_BELOW,
          isBlacklist: '0',
          coolOfDate: toDay?.toJSON(),
          blockedBy: SYSTEM_ADMIN_ID,
        };
        await this.userBlockHistoryRepo.createRowData(historyData);

        return { needUserInfo: true };
      }

      return {};
    }

    return {};
  }

  async updateWorkEmail(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      let email = reqData?.email;
      if (!email) return kParamMissing('workMail');
      email = email.toLowerCase();
      const reApplyFlow = reqData.reApply ?? false;
      if (reApplyFlow) await this.verifiedCompanyDate({ userId });

      // Master join
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['empId', 'status', 'workMailId', 'miscData'];

      const include = [masterInclude];
      const attributes = ['id', 'masterId', 'fullName', 'appType'];
      const options = { where: { id: userId }, include };
      // get userData
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const masterId = userData.masterId;
      const statusData = userData.masterData?.status ?? {};
      if (email) {
        const spans = email.split('@');
        if (spans.length > 1) {
          const domain = spans[1];
          if (kExceptionsDomains.includes(domain))
            return k422ErrorMessage('Please enter valid official work mail');
        }
        // Checks whether Email address exists in another user or not
        const existing: any = await this.isEmailExist(email, userId);
        if (existing?.message) return existing;
      }
      // Salary slip check for re-apply flow
      const miscData = userData.masterData?.miscData ?? {};
      if (reApplyFlow) {
        const salarySlipAtr = ['id', 'salarySlipDate', 'status'];
        const salarySlipOptions = {
          where: {
            userId: userId,
          },
          order: [['id', 'DESC']],
        };
        const salarySlipData = await this.salarySlipRepo.getRowWhereData(
          salarySlipAtr,
          salarySlipOptions,
        );
        if (salarySlipData === k500Error) return kInternalError;
        let salarySlipDate =
          salarySlipData?.salarySlipDate ?? '1970-01-01' + kGlobalTrail;
        salarySlipDate = new Date(salarySlipDate);
        const SlarySlipStatus = salarySlipData?.status;
        const today = this.typeService.getGlobalDate(new Date());
        const diffInDays = this.typeService.dateDifference(
          today,
          salarySlipDate,
        );
        if (diffInDays > GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS)
          miscData.needSalarySlip = true;
        else if (
          diffInDays < GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS &&
          (SlarySlipStatus === '1' || SlarySlipStatus === '3')
        ) {
          //for skiping salary slip if user reapply again in less than 100 days.
          statusData.salarySlip = 4;
          miscData.needSalarySlip = false;
        }
      }

      const otp = this.commonService.generateOTP();
      let status = email ? '5' : '4';
      if (email) {
        const mailData = await this.workEmailRepo.getRowWhereData(
          ['id', 'status', 'userId', 'email'],
          { where: { email: email, userId: userId } },
        );
        if (mailData === k500Error) return kInternalError;
        const data = {
          name: userData.fullName,
          code: otp,
          userId: userId,
          workMail: 'true',
          appType: userData?.appType,
        };
        await this.notificationService.sendEmail(kTEmailOtp, email, data);
      }
      statusData.workMail = +status;
      const creationData = { email, otp, masterId, status, userId };
      const createdData = await this.workEmailRepo.createRowData(creationData);
      if (createdData == k500Error) return kInternalError;
      const workMailId = createdData.id;

      // Update employment data
      let updatedData: any = { workMailId };
      // New loan, New master data
      let updateResult = await this.repository.updateRowData(
        updatedData,
        userData.masterData.empId,
      );
      if (updateResult == k500Error) return kInternalError;
      // Update master data
      updatedData = {
        status: statusData,
        workMailId,
        workMailAdminId: SYSTEM_ADMIN_ID,
        miscData,
      };
      // New loan, New master data
      updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
      if (updateResult == k500Error) return kInternalError;
      if (status == '4')
        return await this.checkAndUpdateEmail(userId, null, reqData);
      else
        return {
          needUserInfo: true,
          showOTPBox: true,
          type: 'workEmail',
          otpBoxValue: email,
        };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region check and update to email details
  async checkAndUpdateEmail(userId, email, reqData) {
    try {
      // // Get user data
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = [
        'dates',
        'empId',
        'status',
        'workMailId',
        'salarySlipId',
      ];
      const include = [masterInclude];
      const attributes = [
        'id',
        'gender',
        'masterId',
        'recentDeviceId',
        'fullName',
        'email',
        'phone',
        'hashPhone',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const masterData = userData.masterData ?? {};
      let masterId = userData.masterId;
      let statusData = userData.masterData?.status ?? {};
      const submitStatus = [1, 3, 4];
      let reApplyData: any = {};
      if (submitStatus.includes(statusData.workMail) || reqData?.reApply) {
        reApplyData = await this.handleReApplyFlow(
          statusData,
          reqData,
          userData,
        );
        if (reApplyData.message) return reApplyData;
      }
      // New loan, New master data
      if (reApplyData.status) statusData = reApplyData.status;
      let workMailId;
      // Skip work email
      if (!email) statusData.workMail = 4;
      // Update employment data
      let updatedData: any = { workMailId };
      // New loan, New master data
      if (reApplyData.masterId) updatedData.masterId = reApplyData.masterId;
      let updateResult = await this.repository.updateRowData(
        updatedData,
        masterData.empId,
      );
      if (updateResult == k500Error) return kInternalError;
      // Update master data
      updatedData = {
        status: statusData,
        workMailId,
        workMailAdminId: SYSTEM_ADMIN_ID,
      };

      if (
        submitStatus.includes(statusData.company) &&
        submitStatus.includes(statusData.workMail) &&
        submitStatus.includes(statusData.salarySlip) &&
        !reApplyData?.status
      ) {
        masterData.dates.employment = new Date().getTime();
        updatedData.dates = masterData.dates;
      }
      // New loan, New master data
      if (reApplyData.masterId) masterId = reApplyData.masterId;
      updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
      if (updateResult == k500Error) return kInternalError;
      return { needUserInfo: true };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // Checks whether Email address exists in another user or not
  private async isEmailExist(email, userId) {
    // Preparation -> Query
    const attributes = ['id'];
    const where = { email, userId: { [Op.ne]: userId } };
    // Hit -> Query
    const existingData = await this.workEmailRepo.getRowWhereData(attributes, {
      where,
    });
    // Validation -> Query data
    if (existingData === k500Error) throw new Error();
    if (existingData)
      return k422ErrorMessage('Please verify your email and try again');

    return {};
  }

  // Re - apply route
  private async handleReApplyFlow(
    statusData,
    reqData,
    userData,
    updateLoanData?,
    companyName?,
    expressReapply?,
  ) {
    try {
      if (statusData.loan != 7 && statusData.loan != 2 && statusData.loan != -2)
        return { status: statusData, masterId: userData.masterId };
      if (!reqData.reApply) return kParamMissing('reApply');
      const purposeId = reqData.purposeId;
      if (!purposeId) return kParamMissing('purposeId');
      const userId = reqData.userId;
      const userInfo = await this.userRepo.getRowWhereData(
        [
          'appType',
          'typeOfDevice',
          'fullName',
          'email',
          'phone',
          'hashPhone',
          'uniqueId',
        ],
        { where: { id: userId } },
      );
      const appType = userInfo?.appType ?? 0;
      let completedLoan = 0;
      const where = { userId };
      const loanData = await this.loanRepo.getTableWhereData(['loanStatus'], {
        where,
      });
      if (loanData === k500Error) completedLoan = 0;
      const activeLoan = loanData.some((el) => el?.loanStatus === 'Active');
      if (activeLoan && !kDummyUserIds.includes(userId))
        return k422ErrorMessage('Loan is already Active!');
      completedLoan = loanData.filter(
        (el) => el?.loanStatus === 'Complete',
      ).length;

      companyName = companyName?.trim()?.toUpperCase();
      if (!companyName) {
        const empAttr = ['companyName', 'updatedCompanyName'];
        const empOptions = { where: { userId } };
        const existingData = await this.repository.getRowWhereData(
          empAttr,
          empOptions,
        );
        if (!existingData || existingData == k500Error) return kInternalError;
        companyName = (
          existingData?.updatedCompanyName ?? existingData?.companyName
        )
          ?.trim()
          ?.toUpperCase();
      }
      const companyData = await this.CompanyRepo.getRowWhereData(['id'], {
        where: { companyName: companyName },
      });
      if (!companyData || companyData == k500Error) return kInternalError;
      const companyId = companyData?.id;
      const creationData: any = {
        fullName: userData?.fullName ?? userInfo?.fullName ?? null,
        email: userData?.email ?? userInfo?.email ?? null,
        phone: userData?.phone ?? userInfo?.phone ?? null,
        hashPhone: userData?.hashPhone ?? userInfo?.hashPhone ?? null,
        interestRate: GLOBAL_RANGES.MIN_PER_DAY_INTEREST_RATE.toString(),
        purposeId,
        userId,
        completedLoan,
        appType,
        loanAcceptStatus: -1,
        kfsStatus: -1,
        userUniqueId: userInfo?.uniqueId,
        initialTypeOfDevice: userData?.typeOfDevice ?? userInfo?.typeOfDevice,
        ...(updateLoanData ?? {}),
      };
      if (companyName && companyId) creationData.companyId = companyData?.id;
      if (userData?.recentDeviceId)
        creationData.activeDeviceId = userData?.recentDeviceId;

      const createdData = await this.loanRepo.createRowData(creationData);
      if (createdData == k500Error) return kInternalError;
      if (createdData) {
        // update loan count for pre approval user
        await this.commonSharedService.setLoanCount(userId);

        if (companyName) {
          const key = `${companyName}_COMPANY_ACTIVITY`;
          await this.redisService.del(key);
        }
      }
      const loanId = createdData.id;

      // In v3 flow few steps are related to loan but getting triggered before the loan starts
      await this.logTracker.addLoanIdsToPreLoanFields(userId, loanId);

      const previousMasterData = await this.previousMasterData(userId);
      if (previousMasterData?.message) return previousMasterData;
      const otherInfo = previousMasterData.otherInfo ?? {
        maritalInfo: '',
        spouseName: '',
        motherName: '',
        dependents: 0,
        vehicleInfo: [],
        educationInfo: '',
        residentialInfo: '',
        employmentInfo: '',
        salaryInfo: 0,
      };

      // New master data status
      const newStatus = userData.masterData?.status ?? {};
      newStatus.loan = -1;
      newStatus.eligibility = -1;
      newStatus.bank = -1;
      newStatus.eMandate = -1;
      newStatus.eSign = -1;
      newStatus.disbursement = -1;
      delete newStatus.contact;
      delete newStatus.ipCheck;
      delete newStatus.reference;
      delete newStatus.residence;
      delete newStatus.repayment;

      if (GLOBAL_FLOW.WORK_MAIL_SALARYSLIP_SKIP) {
        newStatus.workMail = 4;
        newStatus.salarySlip = 4;
      }
      // new master data date
      const newDates = userData.masterData?.dates ?? {};
      delete newDates.eSign;
      delete newDates.banking;
      delete newDates.eMandate;
      delete newDates.eligibility;
      delete newDates.disbursement;
      if (GLOBAL_FLOW.WORK_MAIL_SALARYSLIP_SKIP) {
        newDates.employment = new Date().getTime();
      }

      try {
        if ([1, 3].includes(newStatus.aadhaar) && newDates.aadhaar) {
          const kycDate = this.typeService.getGlobalDate(
            new Date(newDates.aadhaar),
          );
          const currentDate = this.typeService.getGlobalDate(new Date());
          const diffDays = this.typeService.differenceInDays(
            kycDate,
            currentDate,
          );

          const kycAtr = ['kyc_mode'];
          const kycOption = {
            where: {
              userId: userId,
              aadhaarStatus: { [Op.or]: ['1', '3'] },
            },
            order: [['id', 'DESC']],
          };
          const kycRes = await this.kycRepo.getRowWhereData(kycAtr, kycOption);
          if (kycRes === k500Error) return kInternalError;

          if (
            diffDays >= REKYCDAYS ||
            (kycRes?.kyc_mode != 'ZOOP' &&
              kycRes?.kyc_mode != 'DIGILOCKER' &&
              kycRes?.kyc_mode != 'DIGILOCKER_IN_HOUSE' &&
              kycRes?.kyc_mode != 'ZOOP_DIGILOCKER' &&
              kycRes?.kyc_mode != 'DIGITAP')
          ) {
            newStatus.aadhaar = -1;
            delete newDates.aadhaar;
          }
        }
      } catch (error) {}

      await this.sharedMandate.checkPreviousMandates(userId);
      // Create new master data
      const oldMasterId = userData.masterId;
      let updatedData: any = {
        otherInfo,
        status: newStatus,
        loanId,
        dates: newDates,
        loanAcceptStatus: -1,
        kfsStatus: -1,
        kfsAcceptDate: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const checkMasterData = oldMasterId
        ? await this.masterRepo.getRowWhereData(['id'], {
            where: { loanId: null, id: oldMasterId },
          })
        : null;
      let newMasterData;

      if (checkMasterData) {
        if (typeof expressReapply === 'boolean')
          updatedData.expressReapply = expressReapply;
        await this.masterRepo.updateRowData(updatedData, checkMasterData.id);
        newMasterData = checkMasterData;
      } else {
        const rejection = {};
        updatedData = {
          status: newStatus,
          loanId,
          dates: newDates,
          rejection,
          loanAcceptStatus: -1,
          kfsStatus: -1,
          kfsAcceptDate: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          assignedCSE: null,
          expressReapply: null,
          pre_approve_status: null,
          pre_approve_amount: null,
        };

        if (typeof expressReapply === 'boolean')
          updatedData.expressReapply = expressReapply;

        newMasterData = await this.masterRepo.createRowDataWithCopy(
          updatedData,
          oldMasterId,
        );
        if (newMasterData == k500Error) return kInternalError;
      }

      await this.predictionService.assignToCSE(BANKINGADMINS, newMasterData.id);
      // Update loan data
      updatedData = { masterId: newMasterData.id };
      let updateResult = await this.loanRepo.updateRowData(updatedData, loanId);
      if (updateResult == k500Error) return kInternalError;

      // Update user data
      updateResult = await this.userRepo.updateRowData(
        { ...updatedData, lastLoanId: loanId, lastCrm: null },
        userId,
      );
      if (updateResult == k500Error) return kInternalError;
      const masterData = userData.masterData;
      if (masterData?.empId)
        updateResult = await this.repository.updateRowData(
          updatedData,
          masterData?.empId,
        );
      if (updateResult == k500Error) return kInternalError;

      if (masterData?.workMailId)
        updateResult = await this.workEmailRepo.updateRowData(
          updatedData,
          masterData?.workMailId,
        );
      if (updateResult == k500Error) return kInternalError;

      if (masterData?.salarySlipId)
        updateResult = await this.salarySlipRepo.updateRowData(
          updatedData,
          masterData?.salarySlipId,
        );
      if (updateResult == k500Error) return kInternalError;

      if (userData.isCibilConsent == 1) {
        const where = { userId };
        let loanCount = await this.loanRepo.getCountsWhere({ where });
        if (loanCount === k500Error) return kInternalError;
        if (GLOBAL_FLOW.KYC_CIBIL_HIT && loanCount == 1) {
          this.cibilService.updateLastCibilHardPullLoanId({
            userId,
            loanId,
          });
        } else {
          const cibilResult = await this.cibilService.cibilPersonalLoanScore({
            userId,
            loanId,
          });

          if (cibilResult?.isLoanDeclined == true) {
            const adminId = SYSTEM_ADMIN_ID;
            let targetDate = new Date();
            targetDate.setDate(
              targetDate.getDate() +
                GLOBAL_RANGES.COOL_OFF_WITH_BAD_CIBIL_SCORE,
            );

            // Reject loan at this step
            await this.adminService.changeBlacklistUser({
              userId,
              adminId,
              type: '2',
              status: '0',
              reason: BAD_CIBIL_SCORE_MSG,
              reasonId: 52,
              nextApplyDate: targetDate,
            });
            return { needUserInfo: true };
          }
        }
      }

      const isUnderAge = await this.checkIsEligibleAsPerAge(userId, loanId);
      return {
        status: newStatus,
        masterId: newMasterData.id,
        isUnderAge,
        loanId,
        otherInfo,
      };
    } catch (error) {
      console.log('1167', error);
      return kInternalError;
    }
  }

  private async previousMasterData(userId) {
    try {
      const attributes = ['otherInfo'];
      const options = { order: [['id', 'DESC']], where: { userId } };

      const masterData = await this.masterRepo.getRowWhereData(
        attributes,
        options,
      );
      if (masterData == k500Error) return kInternalError;
      return masterData ?? {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async verifyOTP(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const otp = reqData.otp;
      if (!otp) return kParamMissing('otp');
      // Get user data
      const empInclude: any = { model: employmentDetails };
      empInclude.attributes = [
        'id',
        'companyUrl',
        'companyName',
        'salarySlipId',
      ];
      const workMailInclude: any = { model: WorkMailEntity };
      workMailInclude.attributes = ['email', 'id', 'otp'];
      const masterInclude: any = { model: MasterEntity };
      masterInclude.include = [empInclude, workMailInclude];
      masterInclude.attributes = [
        'status',
        'loanId',
        'miscData',
        'dates',
        'salarySlipId',
      ];
      const include = [masterInclude];
      const attributes = ['masterId', 'isRedProfile'];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const masterId = userData.masterId;
      const masterData = userData.masterData ?? {};
      const empData = masterData.empData ?? {};
      const workMailData = masterData.workMailData ?? {};
      const statusData = masterData.status ?? {};
      const miscData = masterData.miscData ?? {};
      const dates = masterData.dates ?? {};
      const isRedProfile = (userData?.isRedProfile ?? 0) === 2;
      if ((empData?.companyUrl ?? '').trim().length == 0) {
        // Get company details from google apis
        const queryData = {
          needDetails: false,
          searchText: empData.companyName,
        };
        const companyData = await this.googleService.searchOrganisation(
          queryData,
        );
        // Not handling errors because user should not get affected for this error
        if (companyData && !companyData.message) {
          empData['companyAddress'] = companyData.formatted_address ?? '';
          empData.companyUrl = companyData.website ?? '';
          empData['companyPhone'] =
            companyData.international_phone_number ?? '';
          const updateResult = await this.repository.updateRowData(
            {
              companyUrl: empData.companyUrl,
              companyAddress: empData['companyAddress'],
              companyPhone: empData['companyPhone'],
            },
            empData.id,
          );
        }
      }

      // OTP validation
      if (workMailData.otp != otp)
        return k422ErrorMessage('Incorrect OTP, Please try again');
      // Work email domain verification with company url
      const isValidDomain = await this.domainValidation(
        workMailData.email,
        empData.companyUrl,
      );
      // Update work mail data
      const verificationData = {
        isValidDomain,
        statusData,
        miscData,
        dates,
        userId,
        workMailData,
        masterId,
        masterData,
        empData,
        isRedProfile,
      };
      await this.workMailOtpVerifcation(verificationData);
      return { needUserInfo: true };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //Work mail otp verfication
  async workMailOtpVerifcation(reqData) {
    try {
      const todayDate = new Date();
      const {
        isValidDomain,
        statusData,
        miscData,
        dates,
        userId,
        workMailData,
        masterId,
        masterData,
        empData,
        isRedProfile,
      } = reqData;

      //Update work mail domain
      let updatedData: any = {
        status: isValidDomain ? '1' : '0',
        approveById: SYSTEM_ADMIN_ID,
      };
      statusData.workMail = isValidDomain ? 1 : 0;

      //for skiping salary slip if work mail is selected by user.
      let updatedSalarySlipData: any = {};
      if (statusData.salarySlip == 2) {
        updatedSalarySlipData.status = '4';
        updatedSalarySlipData.url = '';
        updatedSalarySlipData.salarySlipDate = null;
        updatedSalarySlipData.salaryVerifiedDate = null;
        updatedSalarySlipData.rejectReason = null;
        updatedSalarySlipData.remark = null;
        updatedSalarySlipData.approveById = SYSTEM_ADMIN_ID;
      }
      statusData.salarySlip = 4;
      miscData.needSalarySlip = false;
      dates.employment = todayDate.getTime();

      /// if old defulter then approved
      if (isRedProfile) {
        updatedData.status = '1';
        statusData.workMail = 1;
      }
      if (statusData.workMail == 0) {
        const checkManualVerified =
          await this.commonSharedService.checkManualWorkMail(
            workMailData,
            empData,
            updatedData,
          );
        if (checkManualVerified) {
          updatedData.status = '1';
          statusData.workMail = 1;
        }
      }
      let updateResult = await this.workEmailRepo.updateRowData(
        updatedData,
        workMailData.id,
      );
      if (updateResult == k500Error) return kInternalError;

      if (statusData.workMail == 0) {
        await this.caAssignmentService.assignCA(
          kVerificationAccessStatus.employment,
          masterData.loanId,
        );
      }
      // Update master data
      updatedData = { status: statusData, miscData, dates, salarySlipId: null };
      updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
      if (updateResult == k500Error) return kInternalError;
      //for removing old salary slip id from employement Details table
      const empRes = await this.repository.updateRowData(
        { salarySlipId: null },
        empData?.id,
      );
      if (empRes == k500Error) return kInternalError;

      const masterAtr = ['id', 'salarySlipId'];
      const masterOptions = {
        where: { userId: userId, salarySlipId: { [Op.ne]: null } },
        order: [['id', 'DESC']],
      };
      const masterRes = await this.masterRepo.getRowWhereData(
        masterAtr,
        masterOptions,
      );
      if (masterRes === k500Error) return kInternalError;

      //updating correct master id in salary slip
      if (masterRes?.salarySlipId) {
        updatedSalarySlipData.masterId = masterRes?.id;
        const salarySlipRes = await this.salarySlipRepo.updateRowData(
          updatedSalarySlipData,
          masterRes?.salarySlipId,
        );
        if (salarySlipRes === k500Error) return kInternalError;
      }
      await this.checkAndUpdateEmail(userId, workMailData.email, reqData);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Work email domain verification with company url
  domainValidation(email: string, url) {
    try {
      if (!url || !email) return false;
      const domain = email.split('@')[1];
      return url.includes(domain);
    } catch (error) {
      return false;
    }
  }

  async generateOTP(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');

      // Get user data
      const workMailInclude: any = { model: WorkMailEntity };
      workMailInclude.attributes = ['id', 'otp', 'email'];
      const masterInclude: any = { model: MasterEntity };
      masterInclude.include = [workMailInclude];
      masterInclude.attributes = ['status'];
      const include = [masterInclude];
      const attributes = ['masterId', 'fullName', 'appType'];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const masterData = userData.masterData ?? {};
      const workMailData = masterData.workMailData ?? {};
      const statusData = masterData.status ?? {};

      if (statusData.workMail == 4)
        return k422ErrorMessage('Work email already skipped');
      // if (statusData.workMail == 1 || statusData.workMail == 3)
      //   return k422ErrorMessage('Work email already verified');
      if (!workMailData && !workMailData?.email)
        return k422ErrorMessage(
          'Kindly add work email before proceeding further',
        );

      const otp = this.commonService.generateOTP();
      const data = {
        name: userData.fullName,
        code: otp,
        userId: userId,
        appType: userData?.appType,
      };
      await this.notificationService.sendEmail(
        kTEmailOtp,
        workMailData?.email,
        data,
      );
      const updatedData = { otp };
      // Update work email data
      const updateResult = await this.workEmailRepo.updateRowData(
        updatedData,
        workMailData.id,
      );
      if (updateResult == k500Error) return kInternalError;

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async validateManualEmails(reqData) {
    try {
      // Single user request
      const userId = reqData.userId;
      if (userId) {
        const masterInclude: any = { model: MasterEntity };
        masterInclude.attributes = ['dates', 'miscData', 'status', 'id'];
        const userInclude: any = { model: registeredUsers };
        userInclude.attributes = ['appType'];
        const include = [masterInclude, userInclude];
        const attributes = ['email', 'id'];
        const options = { order: [['id', 'DESC']], include, where: { userId } };
        const workMailData = await this.workEmailRepo.getRowWhereData(
          attributes,
          options,
        );
        if (workMailData == k500Error) return kInternalError;
        if (!workMailData) return k422ErrorMessage(kNoDataFound);
        if (!workMailData.email)
          return k422ErrorMessage('Work email not found');
        const statusData = workMailData.masterData?.status ?? {};
        const acceptedStatuses = [1, 3, 5];
        if (!acceptedStatuses.includes(statusData.workMail))
          return k422ErrorMessage('work email verification failed');

        const emails = await this.googleService.checkParticularSender({
          email: workMailData.email,
          appType: workMailData.user.appType,
        });
        const isReceived = !emails.message && emails.length > 0;
        // Mail not sent
        if (!isReceived)
          return k422ErrorMessage(
            `Please send the mail from ${workMailData.email} to ${kVerificationsMail}`,
          );
        // Mail sent within last 24 hours
        else {
          // Update work mail data
          let updatedData: any = { status: '1', approveById: SYSTEM_ADMIN_ID };
          let updateResult = await this.workEmailRepo.updateRowData(
            updatedData,
            workMailData.id,
          );
          if (updateResult == k500Error) return kInternalError;

          // Update master data
          const dates = workMailData.masterData?.dates ?? {};
          statusData.workMail = 1;
          statusData.salarySlip = 4;
          const isCompanyVerified =
            statusData.company == 1 || statusData.company == 3;
          const isSalarySlipVerified =
            statusData.salarySlip == 1 || statusData.salarySlip == 3;
          statusData.salarySlip == 4;
          const isSalarySlipSubmitted =
            isSalarySlipVerified || statusData.salarySlip == 0;

          if (isCompanyVerified && isSalarySlipVerified)
            dates.employment = new Date().getTime();
          updatedData = {
            status: statusData,
            dates,
            workMailAdminId: SYSTEM_ADMIN_ID,
          };
          updateResult = await this.masterRepo.updateRowData(
            updatedData,
            workMailData.masterData.id,
          );
          if (updateResult == k500Error) return kInternalError;

          // Start new loan
          const isReApplyFlow = statusData.loan == 2 || statusData.loan == 7;
          if (
            isCompanyVerified &&
            (isSalarySlipVerified || isSalarySlipSubmitted) &&
            isReApplyFlow
          ) {
            const masterData = workMailData.masterData ?? {};
            const miscData = masterData.miscData ?? {};
            const purposes = [1, 2, 3, 4, 5, 6];
            const purposeId =
              miscData.nextLoanPurposeId ??
              purposes[Math.floor(Math.random() * purposes.length)];
            const response = await this.checkAndUpdateEmail(
              userId,
              workMailData.email,
              {
                userId,
                purposeId,
                reApply: true,
              },
            );
            if (response.message) return response;
            return { ...response };
          }
          return { needUserInfo: true };
        }
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async validateSalarySlip(reqData) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    let fileUrl = reqData.fileUrl;
    if (!fileUrl) return kParamMissing('fileUrl');
    const employeeDetails = reqData.employee_details;
    if (!employeeDetails) return kParamMissing('employee_details');
    let salarySlipDate =
      employeeDetails.SalaryPeriod ?? '1970-01-01' + kGlobalTrail;
    salarySlipDate = new Date(salarySlipDate);

    // Reset bucket origin
    const base64Data = await this.fileService.fileUrlToBase64(fileUrl);
    const extensionSpans = fileUrl.split('.');
    const extension = extensionSpans[extensionSpans.length - 1];
    fileUrl = await this.fileService.base64ToFileURL(base64Data, extension);

    // Get user data
    const masterInclude: any = { model: MasterEntity };
    masterInclude.attributes = [
      'dates',
      'empId',
      'miscData',
      'status',
      'loanId',
    ];
    const include = [masterInclude];
    const attributes = ['fullName', 'masterId'];
    const options = { include, where: { id: userId } };
    const userData = await this.userRepo.getRowWhereData(attributes, options);
    if (userData == k500Error) return kInternalError;
    if (!userData) return k422ErrorMessage(kNoDataFound);
    const masterData = userData.masterData ?? {};
    const statusData = masterData.status ?? {};
    const miscData = masterData.miscData ?? {};
    const masterId = userData.masterId;
    const loanId = masterData.loanId;
    // Route validation
    const notProcess = [2, 6, 7];
    if (notProcess.includes(statusData?.loan))
      return k422ErrorMessage(kLoanNotProgress);
    // verifying only one from work mail and salary slip

    let workMailId = null;
    if (
      statusData?.workMail == -1 ||
      statusData?.workMail == 2 ||
      statusData?.workMail == 5
    ) {
      statusData.workMail = 4;
      const workMailData = {
        email: '',
        masterId,
        status: '4',
        userId,
        approveById: SYSTEM_ADMIN_ID,
      };
      const workMailCreatedData = await this.workEmailRepo.createRowData(
        workMailData,
      );
      if (workMailCreatedData == k500Error) return kInternalError;
      workMailId = workMailCreatedData.id;

      // Update employment data
      const empData: any = { workMailId };
      // New loan, New master data
      const updateEmpResult = await this.repository.updateRowData(
        empData,
        userData.masterData.empId,
      );
      if (updateEmpResult == k500Error) return kInternalError;
    }

    // Other extracted data
    const otherInfo: any = {};
    if (employeeDetails.joiningDate)
      otherInfo.joiningDate = employeeDetails.joiningDate;
    if (employeeDetails.netPayAmount)
      otherInfo.netPayAmount = employeeDetails.netPayAmount;
    // Salary slip month validation
    const today = this.typeService.getGlobalDate(new Date());
    const diffInDays = this.typeService.dateDifference(today, salarySlipDate);
    let salarySlipStatus = '0';
    if (salarySlipDate.getFullYear() != 1970) {
      if (diffInDays > GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS)
        salarySlipStatus = '0';
      // return k422ErrorMessage('Kindly upload the latest month salary slip');
      else salarySlipStatus = '1';
      otherInfo.salarySlipDate = salarySlipDate.toJSON();
    }
    if (!employeeDetails.companyName) salarySlipStatus = '0';
    if (!employeeDetails.name) salarySlipStatus = '0';
    miscData.needSalarySlip = false;

    const empAttr = ['companyName'];
    const empOptions = { where: { userId } };
    const empData = await this.repository.getRowWhereData(empAttr, empOptions);
    if (empData == k500Error) return kInternalError;

    // Validation -> Offer letter
    let offerLetterResponse = {};
    try {
      if (employeeDetails?.Type == 'Offer Letter') {
        salarySlipStatus = '0';
        const filePath = await this.fileService.fileUrlToFile(fileUrl);
        const body = new FormData();
        const mediaPath = await fs.readFileSync(filePath);
        body.append('cName', empData?.companyName ?? '');
        body.append('file', mediaPath, filePath);
        body.append('fullName', userData?.fullName ?? '');

        const url = nValidateOfferLetter;
        const response = await this.api.post(url, body);
        await this.fileService.removeFile(filePath);
        if (response?.verify == true) salarySlipStatus = '1';
        offerLetterResponse = response;
      }
    } catch (error) {}
    employeeDetails.offerLetterResponse = offerLetterResponse;

    const creationData: any = {
      approveById: SYSTEM_ADMIN_ID,
      companyName: empData?.companyName,
      masterId,
      url: fileUrl,
      status: salarySlipStatus,
      userId,
      response: JSON.stringify(employeeDetails),
      ...otherInfo,
    };
    if (salarySlipStatus == '1') creationData.salaryVerifiedDate = today;
    else if (salarySlipStatus == '0') {
      await this.caAssignmentService.assignCA(
        kVerificationAccessStatus.employment,
        loanId,
      );
    }
    // Create salary slip data
    const createdData = await this.salarySlipRepo.createRawData(creationData);
    if (createdData == k500Error) return kInternalError;

    // Update employment data
    let updatedData: any = { salarySlipId: createdData.id };
    let updateResult = await this.repository.updateRowData(
      updatedData,
      masterData.empId,
    );
    if (updateResult == k500Error) return kInternalError;
    // Update master data
    statusData.salarySlip = +salarySlipStatus;
    updatedData = {
      miscData,
      status: statusData,
      dates: masterData.dates,
      salarySlipId: createdData.id,
      workMailId,
      workMailAdminId: SYSTEM_ADMIN_ID,
    };
    const approvedStatuses = [1, 3, 4];
    const isCompanyVerified = approvedStatuses.includes(statusData.company);
    const isWorkEmailVerified = approvedStatuses.includes(statusData.workMail);
    const isSalarySlipVerified = approvedStatuses.includes(
      statusData.salarySlip,
    );
    if (isCompanyVerified && isWorkEmailVerified && isSalarySlipVerified)
      updatedData.dates.employment = new Date();
    updatedData.salarySlipAdminId = SYSTEM_ADMIN_ID;
    updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
    if (updateResult == k500Error) return kInternalError;

    const key = `${userId}_USER_EMPLOYMENT_DETAILS`;
    await this.redisService.del(key);
    return { needUserInfo: true };
  }

  //#region update company verify date
  async verifiedCompanyDate(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      // Get user data
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = [
        'status',
        'miscData',
        'dates',
        'empId',
        'workMailId',
        'salarySlipId',
      ];
      const include = [masterInclude];
      const attributes = [
        'id',
        'masterId',
        'recentDeviceId',
        'fullName',
        'email',
        'phone',
        'hashPhone',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const statusData = userData.masterData?.status ?? {};
      if (statusData.loan != 2 && statusData.loan != 7 && statusData.loan != -2)
        return k422ErrorMessage(kSubmissionOfEmploymentNotCom);
      const masterData = userData.masterData ?? {};
      const miscData = masterData.miscData ?? {};
      const empData = userData?.employmentData ?? {};
      const nextLoanPurposeId =
        typeof miscData.nextLoanPurposeId == 'string'
          ? +miscData.nextLoanPurposeId
          : miscData.nextLoanPurposeId;
      const targetPurposeId =
        miscData.purposeId == 0 ? null : miscData.purposeId;
      const purposeId = targetPurposeId ?? nextLoanPurposeId;
      const data = { userId, purposeId, reApply: true };
      // statusData.company = -1;
      // // Need to reset the below status in case company added again
      // statusData.salarySlip = -1;
      // statusData.workMail = -1;
      return await this.handleReApplyFlow(statusData, data, userData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async empDetailsForBankingPro(reqData) {
    try {
      // Params validation
      const clienttxnid = reqData.clienttxnid;
      if (!clienttxnid) return kParamMissing('clienttxnid');

      const empInclude: any = { model: employmentDetails };
      empInclude.attributes = ['companyName', 'salary'];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = ['id'];
      userInclude.include = [empInclude];
      const include = [userInclude];
      const attributes = ['id'];
      const options = { include, where: { consentTxnId: clienttxnid } };

      const bankingData = await this.bankingRepo.getRowWhereData(
        attributes,
        options,
      );
      if (bankingData == k500Error) return kInternalError;
      if (!bankingData) return k422ErrorMessage(kNoDataFound);

      return bankingData.user?.employmentData ?? {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Checks user is eligible as per age criteria or not
  private async checkIsEligibleAsPerAge(userId, loanId) {
    try {
      const options = {
        where: { aadhaarStatus: '1', userId },
        order: [['id', 'desc']],
      };
      const att = ['id', 'aadhaarDOB'];
      const findKYC = await this.kycRepo.getRowWhereData(att, options);
      if (!findKYC || findKYC === k500Error) return true;
      if (!findKYC?.aadhaarDOB) return true;
      const isEligible: any = this.typeService.isEligibleAsPerAge(
        findKYC?.aadhaarDOB,
      );
      if (isEligible?.type) {
        const reason =
          isEligible?.type == '2' ? kMinAgeCriteria : kMaxAgeCriteria;
        const loanRejectRes = await this.eligiblityService.rejectLoan(
          SYSTEM_ADMIN_ID,
          loanId,
          reason,
          userId,
          null,
          null,
          true,
          true,
        );
        if (loanRejectRes === true) {
          const historyData = {
            reasonId: reason === kMinAgeCriteria ? 70 : 71,
            userId,
            reason,
            isBlacklist: '1',
            blockedBy: SYSTEM_ADMIN_ID,
          };
          await this.userBlockHistoryRepo.createRowData(historyData);
        }
        //reject step if loan is not available
        await this.eligiblityService.checkAndRejectSteps(userId, reason);
        return false;
      } else return true;
    } catch (error) {
      return true;
    }
  }

  async getFieldsForSameCompany(query) {
    try {
      const userId = query?.userId ?? '';
      if (!userId) return kParamMissing('userId');
      const isSame = query?.isSame ?? 'false';
      const isNew = query?.isNew ?? 'false';
      if (isSame == undefined) return kParamMissing('isSame');
      const necessaryList: any = await this.getNecessaryList();
      //for lsp user
      let topCompanies = necessaryList?.topCompanies.map((company, i) => ({
        id: i + 1,
        value: company,
      }));
      let sectors = necessaryList?.sectors.map((sectors, i) => ({
        id: sectors.id,
        value: sectors.sectorName,
      }));
      let designations = necessaryList?.designations.map((designations, i) => ({
        id: designations.id,
        value: designations.designationName,
      }));
      kFirstTimeEmploymentFields.forEach((data: any) => {
        if (data.key == 'companyId') {
          data.options = topCompanies;
        }
        if (data.key == 'sectorId') {
          data.options = sectors;
        }
        if (data.key == 'positionId') {
          data.options = designations;
        }
      });

      ///get user details for check completed loan count
      const userData = await this.repoManager.getRowWhereData(
        registeredUsers,
        ['completedLoans'],
        { where: { id: userId } },
      );

      const completedLoans = userData?.completedLoans ?? 0;
      if (isNew == 'true' || isNew == true) {
        const routeDetailsKey = userId + 'ROUTE_DETAILS';
        let cachedUserData = await this.redisService.getKeyDetails(
          routeDetailsKey,
        );
        cachedUserData = cachedUserData ? JSON.parse(cachedUserData) : {};
        if (
          +(cachedUserData?.userData?.previousSalary ?? 0) >=
          GLOBAL_RANGES.NEW_USER_MIN_SALARY
        )
          return kFirstTimeEmploymentFields;
        else {
          const newFieldsArr: any = [...kFirstTimeEmploymentFields];
          const salaryFiledObj = {
            title: 'Salary Amount',
            sub_title: 'Enter your monthly in-hand salary',
            key: 'netPaySalary',
            label: 'Salary Amount',
            type: 1,
            maxLength: MAX_LENGTH_SALARY_AMOUNT,
            validation: {
              regex:
                completedLoans > 0
                  ? SALARY_EXPRESSION.REPETER_USER
                  : SALARY_EXPRESSION.NEW_USER,
              error: 'Please enter valid salary',
            },
          };
          if (+(cachedUserData?.userData?.previousSalary ?? 0)) {
            newFieldsArr.splice(4, 0, salaryFiledObj);
            return newFieldsArr;
          } else {
            const msOps = { where: { userId }, order: [['id', 'DESC']] };
            const masterData = await this.masterRepo.getRowWhereData(
              ['id', 'otherInfo'],
              msOps,
            );
            if (
              +(masterData?.otherInfo?.salaryInfo ?? 0) >=
              GLOBAL_RANGES.NEW_USER_MIN_SALARY
            )
              return kFirstTimeEmploymentFields;
            else {
              newFieldsArr.splice(4, 0, salaryFiledObj);
              return newFieldsArr;
            }
          }
        }
      }

      const empData: any = await this.repository.getRowWhereData(
        ['otherInfo'],
        {
          where: { userId },
        },
      );
      if (empData === k500Error) return kInternalError;
      if (isSame == 'true' && empData?.otherInfo) {
        ///if loan completed then salary amount
        let kSameEmploymentFieldsUpdate = kSameEmploymentFields;
        kSameEmploymentFieldsUpdate[0].validation.regex =
          completedLoans > 0
            ? SALARY_EXPRESSION.REPETER_USER
            : SALARY_EXPRESSION.NEW_USER;
        return kSameEmploymentFieldsUpdate;
      } else {
        let kEmploymentFieldsUpdated = kEmploymentFields;
        kEmploymentFieldsUpdated[0].validation.regex =
          completedLoans > 0
            ? SALARY_EXPRESSION.REPETER_USER
            : SALARY_EXPRESSION.NEW_USER;
        return kEmploymentFieldsUpdated;
      }
    } catch (e) {
      return kInternalError;
    }
  }

  //#region
  async getEmployementData(query) {
    try {
      const empId = query.empId;
      if (!empId) return kParamMissing('empId');
      const designationAttr = ['id', 'designationName'];
      const desigantionInclude = {
        model: employmentDesignation,
        attributes: designationAttr,
      };
      const sectorAttr = ['id', 'sectorName'];
      const sectorInclude = { model: employmentSector, attributes: sectorAttr };
      const attributes = [
        'companyName',
        'id',
        'startDate',
        'endDate',
        'salary',
        'salaryDate',
        'type',
      ];
      const options = {
        where: { id: empId },
        include: [desigantionInclude, sectorInclude],
      };
      const result = await this.repository.getRowWhereData(attributes, options);
      if (result == k500Error) return kInternalError;
      if (!result?.startDate) result.startDate = new Date().toJSON();
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async expressSubmitDetails(reqData) {
    const loanPurpose = reqData?.loanPurpose;
    const salary = reqData?.salary;
    const oldBank = reqData?.oldBank; //oldBank is boolean
    const userId = reqData?.userId;
    const communicationLanguage = reqData?.prefLanguage;
    const loanId = reqData?.loanId;
    const newCompany = reqData?.newCompany; // newCompany is boolean
    if (!userId) return kParamMissing('userId');

    let response;
    let body;
    let expressData;
    let bankData;

    if (!loanPurpose) {
      const purposeList: any = await this.userService.getPurposeList();
      if (purposeList?.message) return purposeList;
      const communicationLanguage: any =
        await this.userService.getcommunicationLanguage(userId);
      if (communicationLanguage?.message) return communicationLanguage;
      expressData = { purposeList, communicationLanguage };
    } else if (loanPurpose && !salary && !oldBank) {
      bankData = await this.sharedBankingService.getBankData(userId);
      let companyData = await this.getCompanyData(userId);
      expressData = {
        ...bankData,
        companyName: (
          companyData?.updatedCompanyName ?? companyData?.companyName
        )
          ?.trim()
          ?.toUpperCase(),
      };
    } else if (loanPurpose && salary && !oldBank) {
      body = { userId, communicationLanguage, purposeId: loanPurpose };
      response = await this.userService.submitBasicDetails(body);
      if (response.message) return response;
      let companyName = reqData?.companyName?.trim()?.toUpperCase();
      if (!companyName) {
        const companyData = await this.getCompanyData(userId);
        companyName = (
          companyData?.updatedCompanyName ?? companyData?.companyName
        )
          ?.trim()
          ?.toUpperCase();
      } else {
        // Prevention -> User stuck (Lead generation)
        const companyAttr = ['id'];
        const companyOptions = { where: { companyName: companyName } };
        const companyData = await this.repoManager.getRowWhereData(
          GoogleCompanyResultEntity,
          companyAttr,
          companyOptions,
        );
        if (companyData == k500Error) throw new Error();
        if (!companyData) {
          const userCompany = companyName;
          companyName = 'OTHERS';
          reqData.companyName = companyName;

          // Store Manual company name
          new Promise(async (resolve, _) => {
            const creationData = {
              type: 'COMPANY_MANUAL_SEARCH',
              userId,
              value: userCompany,
            };
            await this.repoManager.createRowData(Experiment, creationData);
            resolve({});
          }).catch((err) => {});
        }
      }
      body = {
        userId,
        updateEmployment: true,
        netPaySalary: salary,
        companyName,
      };
      response = await this.reApplySubmitDetails(body);
      if (response.message) return response;
      if (response.needUserInfo == true) return response;
      expressData = await this.sharedBankingService.getBankData(userId);
    }
    if (loanId) {
      const attr = ['miscData', 'otherInfo'];
      const options = {
        where: { loanId },
      };
      const existingData = await this.masterRepo.getRowWhereData(attr, options);
      if (existingData == k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
      const bankData: any = await this.sharedBankingService.getBankData(userId);
      if (bankData.message) return bankData;
      if (existingData && bankData) {
        expressData = {
          ...expressData,
          prevFilledData: {
            purposeName: existingData?.miscData?.purposeName,
            purposeId: existingData?.miscData?.purposeId,
            enteredSalary:
              existingData?.otherInfo?.netPaySalary ??
              existingData?.otherInfo?.salaryInfo,
          },
          ...bankData,
        };
      }
    }
    if (expressData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    let data = await this.userService.routeDetails({ id: userId });

    if (newCompany === 'YES') {
      data.continueRoute = kEmploymentRoute;
    } else if (loanPurpose && salary && oldBank === 'YES') {
      const bankData: any = await this.sharedBankingService.getBankData(userId);
      const body = {
        bankCode: bankData?.bankCode,
        accNumber: bankData?.accountNumber,
        ifscCode: bankData?.ifsc,
        fipName: bankData?.fipName,
        userId,
        phone: data?.userData?.phone,
      };
      data = await this.bankinService.inviteForAA(body);
    } else if (loanPurpose && salary && oldBank === 'NO') {
      data.continueRoute = kNetBankingRoute;
    }
    data.expressData = expressData;
    return data;
  }

  async getCompanyData(userId) {
    const attributes = ['companyName', 'updatedCompanyName'];
    const options = {
      where: { userId },
    };
    const companyData = await this.repository.getRowWhereData(
      attributes,
      options,
    );
    if (companyData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return companyData;
  }

  async reApplySubmitDetails(reqData) {
    // Params validation
    const userId = reqData.userId;
    const update = reqData?.updateEmployment ?? false;
    if (!userId) return kParamMissing('userId');
    let companyName = reqData.companyName?.trim()?.toUpperCase();
    if (!companyName && !update) return kParamMissing('companyName');
    const salary = reqData?.netPaySalary;
    if (!salary) return kParamMissing('salary');

    if (companyName) {
      // Prevention -> User stuck (Lead generation)
      const companyAttr = ['id'];
      const companyOptions = { where: { companyName: companyName } };
      const companyData = await this.repoManager.getRowWhereData(
        GoogleCompanyResultEntity,
        companyAttr,
        companyOptions,
      );
      if (companyData == k500Error) throw new Error();
      if (!companyData) {
        const userCompany = companyName;
        companyName = 'OTHERS';
        reqData.companyName = companyName;
        // Store Manual company name
        new Promise(async (resolve, _) => {
          const creationData = {
            type: 'COMPANY_MANUAL_SEARCH',
            userId,
            value: userCompany,
          };
          await this.repoManager.createRowData(Experiment, creationData);
          resolve({});
        }).catch((err) => {});
      }
    }

    const netPaySalary = +(
      this.typeService.checkAndRemoveComma(salary ?? '') ?? 0
    );
    let salaryInfo = netPaySalary;
    reqData.salaryInfo = salaryInfo;

    const updateLoanData = {
      ...(netPaySalary ? { netPaySalary } : {}),
    };

    // Prepare data
    const todayDate = this.typeService.getGlobalDate(new Date());
    let companyVerification = '1';
    const empData: any = {
      companyAddress: '',
      companyUrl: '',
      companyPhone: '',
      companyVerification,
      companyName,
      employmentTypeId: 1,
      masterId: null,
      salary: netPaySalary,
      userId,
      verifiedDate: todayDate.toJSON(),
      otherInfo: {
        ...updateLoanData,
      },
      companyNameChangeBy: null,
      updatedCompanyName: null,
    };
    let checkCompany: any = true;

    checkCompany = await this.employmentSharedService.verifyAndStoreInfo(
      companyName,
      userId,
    );
    if (checkCompany?.message || checkCompany == k500Error) {
      companyName = 'OTHERS';
      reqData.companyName = companyName;
    }

    // Skip insta financial in case of error
    if (checkCompany?.message) checkCompany = true;
    if (checkCompany?.message) return checkCompany;
    if (checkCompany === false && companyName != 'OTHERS')
      companyVerification = '2';

    // Get user data
    const masterInclude: any = { model: MasterEntity };
    masterInclude.attributes = [
      'coolOffData',
      'otherInfo',
      'status',
      'dates',
      'miscData',
      'empId',
      'workMailId',
      'salarySlipId',
      'loanId',
    ];
    const kycInclude: any = { model: KYCEntity };
    kycInclude.attributes = [
      'aadhaarDOB',
      'aadhaarState',
      'aadhaarAddress',
      'aadhaarAddressResponse',
    ];
    const include = [masterInclude, kycInclude];
    const attributes = [
      'gender',
      'masterId',
      'recentDeviceId',
      'completedLoans',
      'leadId',
      'state',
      'categoryScore',
      'fullName',
      'email',
      'phone',
      'hashPhone',
      'isCibilConsent',
    ];
    const options = { include, where: { id: userId } };
    const userData = await this.userRepo.getRowWhereData(attributes, options);
    if (userData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    if (!userData) return k422ErrorMessage(kNoDataFound);
    //first time user
    if (
      userData?.completedLoans == 0 &&
      netPaySalary < GLOBAL_RANGES.MIN_EMPLOYMENT_SALARY
    )
      return k422ErrorMessage('Please enter valid salary');
    //repeat user
    else if (
      userData?.completedLoans > 0 &&
      netPaySalary < GLOBAL_RANGES.MIN_SALARY_AMOUNT
    )
      return k422ErrorMessage('Please enter valid salary');
    const statusData = userData.masterData?.status ?? {};
    if (!salaryInfo)
      salaryInfo = userData.masterData?.otherInfo?.salaryInfo ?? 0;
    const masterId = userData.masterId;

    empData.salary = salaryInfo;
    empData.masterId = masterId;

    // Double check -> Minimum salary criteria
    const userEnteredSalary =
      (reqData.salaryInfo ?? 0) > 0 ? reqData.salaryInfo : +salaryInfo;

    const checkResult = await this.doubleCheckMinSalary({
      masterId,
      userData,
      userEnteredSalary,
      userId,
    });
    if (checkResult?.needUserInfo) return checkResult;

    const miscData = userData.masterData?.miscData ?? {};

    if (
      statusData.loan != 2 &&
      statusData.loan != 7 &&
      statusData.loan != -2 &&
      statusData.loan != -1
    )
      return k422ErrorMessage(kSubmissionOfEmploymentNotCom);
    let empId;
    // Existing data
    const empOptions = { where: { userId } };
    const existingData = await this.repository.getRowWhereData(
      null,
      empOptions,
    );
    if (existingData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    if (statusData?.company == 2) {
      if (
        (companyName ?? '').toLowerCase().trim() ==
        (existingData?.companyName ?? '').toLowerCase()
      )
        return k422ErrorMessage(kCanNotSelectThisCompany);
    }

    // Move existing record into history and update new record
    if (existingData) {
      empId = existingData.id;
      // add previous company data if selected old company
      if (update) {
        empData.companyAddress = existingData.companyAddress;
        empData.companyUrl = existingData.companyUrl;
        empData.companyPhone = existingData.companyPhone;
        empData.companyVerification = existingData.companyVerification;
        empData.companyName = existingData.companyName;
        empData.employmentTypeId = existingData.employmentTypeId;
        empData.sectorId = existingData.sectorId;
        empData.designationId = existingData.designationId;
        empData.startDate = existingData.startDate;
        empData.salary = netPaySalary;
        reqData.empInfo = userData?.masterData?.otherInfo?.employmentInfo;
        reqData.salaryInfo =
          salaryInfo ?? userData?.masterData?.otherInfo?.salaryInfo;
        empData.otherInfo = {
          ...existingData.otherInfo,
          ...updateLoanData,
        };
        empData.salaryDate = existingData?.salaryDate;
        empData.companyNameChangeBy = existingData?.companyNameChangeBy;
        empData.updatedCompanyName = existingData?.updatedCompanyName;
        empData.workMailId = null;
        empData.salarySlipId = null;
      }
      existingData.id = undefined;

      const createdData = await this.empHistoryRepo.createRowData(existingData);
      if (createdData == k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

      const updateResult = await this.repository.updateRowData(empData, empId);
      if (updateResult == k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    }
    // Create new record
    else {
      empData.companyVerification = companyVerification;
      const createdData = await this.repository.createRowData(empData);
      if (createdData == k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
      empId = createdData.id;
    }
    // Update master data
    // Need to reset the below status in case company added again
    statusData.company = +companyVerification;
    statusData.salarySlip = 4;
    statusData.workMail = 4;
    miscData.needSalarySlip = true;
    const otherInfo = userData?.masterData?.otherInfo;

    if (netPaySalary) {
      otherInfo.netPaySalary = netPaySalary;
      otherInfo.salaryInfo = netPaySalary;
    }

    const updatedData = {
      empId,
      status: statusData,
      otherInfo,
      salarySlipId: null,
      workMailId: null,
      miscData,
    };
    const updateResult = await this.masterRepo.updateRowData(
      updatedData,
      masterId,
    );
    if (updateResult === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    // Validate blacklisted companies
    const isEligibleCompany: any =
      await this.sharedEligibility.isEligibleCompany(empData);
    if (isEligibleCompany.message) return isEligibleCompany;
    if (!isEligibleCompany) return { needUserInfo: true };
    if (checkCompany === false && companyName != 'OTHERS')
      return { needUserInfo: true };
    let purposeId = miscData.purposeId;
    if (purposeId == 0) purposeId = miscData.nextLoanPurposeId;
    if (typeof purposeId == 'string') purposeId = +purposeId;
    const data = { userId, purposeId, reApply: true };
    const result = await this.handleReApplyFlow(
      statusData,
      data,
      userData,
      updateLoanData,
      companyName,
      true, // here express reapply will always be true if the user comes through this function
    );
    if (result?.message) return result;

    const workMailData = {
      email: '',
      masterId: result?.masterId,
      status: '4',
      userId,
      approveById: SYSTEM_ADMIN_ID,
    };
    const workMailCreatedData = await this.workEmailRepo.createRowData(
      workMailData,
    );
    if (workMailCreatedData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    const isUnderAge = result?.isUnderAge ?? false;
    if (reqData?.empInfo && isUnderAge) {
      const submitData = await this.userSharedService.submitProfessionalDetails(
        reqData,
      );
      if (submitData?.message) return submitData;
    }
    let aadhaarState = userData?.kycData?.aadhaarState;
    if (!aadhaarState) {
      const aadhaarAddress = await this.typeService.getAadhaarAddress(
        userData.kycData,
      );
      aadhaarState = aadhaarAddress?.state;
    }

    // store category score if not there
    if (!userData.categoryScore) {
      await this.commonSharedService.getRiskCategoryByScoreData({
        userId,
        masterData: {
          otherInfo: result.otherInfo ?? userData.masterData.otherInfo,
        },
        aadhaarState,
        aadhaarDOB: userData.kycData.aadhaarDOB,
        liveState: userData.state,
      });
    }
    // check name validations
    const nameValidation = await this.sharedEligibility.nameValidation(userId);
    if (nameValidation === false) return { needUserInfo: true };
    return {};
  }

  async getEmploymentDetailsUsingEPFO(reqData) {
    const fullName = reqData.name;
    const appType = reqData?.appType;
    let phone = reqData.phone;
    if (phone.length > 10) phone = this.cryptService.decryptPhone(phone);
    phone = phone.toString();

    // Check UAT Series Mobile Number
    const firstSeries = phone.slice(0, 2);
    if (+firstSeries >= 11 && +firstSeries <= 59 && !gIsPROD) {
      if (!UATNumberSeries[firstSeries]) return;
      phone = UATNumberSeries[firstSeries];
      phone = phone.toString();
    } else if (!gIsPROD) return;

    let epfoData: any;
    let epfoService = await this.redisService.get(kEPFOService);
    if (typeof epfoService != 'string')
      epfoService = GlobalServices.EPFO_SERVICE;
    if (epfoService == '') return;
    else if (epfoService == 'PROTEAN')
      epfoData = await this.employmentSharedService.getUanDetails({
        mobileNo: phone,
        appType,
        typeOfService: 1,
      });
    else if (epfoService == 'BEFISC')
      epfoData = await this.employmentSharedService.getUanDetails({
        mobileNo: phone,
        appType,
        typeOfService: 2,
      });

    if (epfoData?.message) return;

    // Compare name as per aadhaar
    const isNameMissMatch: any = await this.validation.nameMatch(
      fullName?.trim()?.toLowerCase(),
      epfoData?.employeeName?.trim()?.toLowerCase(),
      appType,
    );

    const nameSimilarity = isNameMissMatch?.data ?? 0;
    let isValidName: boolean = false;
    if (isNameMissMatch?.valid && nameSimilarity >= NAME_MISS_MATCH_PER)
      isValidName = true;

    //check give screen or skip employment
    if (!isValidName || epfoData?.exitDate || epfoData?.exitReason) return;
    else if (!epfoData?.lastPFDate && !epfoData?.joiningDate) return;
    //logic on epfoData?.lastPFDate
    const todayDate = this.typeService.getGlobalDate(new Date());
    if (epfoData?.lastPFDate) {
      const lastPFDate = this.typeService.getGlobalDate(epfoData?.lastPFDate);
      const dayDiff = this.typeService.dateDifference(lastPFDate, todayDate);
      if (dayDiff > 60) return;
    }
    //logic on epfoData?.joiningDate
    else if (epfoData?.joiningDate) {
      const periodicLatestWorkingDate = this.typeService.getGlobalDate(
        epfoData?.joiningDate,
      );
      const tenureOfEmployment = epfoData?.tenureOfEmployment ?? 0;
      periodicLatestWorkingDate.setMonth(
        periodicLatestWorkingDate.getMonth() + tenureOfEmployment,
      );
      const dayDiff = this.typeService.dateDifference(
        periodicLatestWorkingDate,
        todayDate,
      );
      if (dayDiff > 45) return;
    }
    const companyNameFromUan = epfoData?.latestCompanyName
      ?.trim()
      ?.toUpperCase();

    if (!companyNameFromUan) return;
    let companyList: any = [];
    companyList = await this.instaFinancial.searchCompany(companyNameFromUan);
    if (
      (companyList.message || !companyList?.length) &&
      GlobalServices.COMPANY_SERVICE == 'PROTEAN'
    )
      companyList = await this.proteanService.searchCompany({
        searchStr: companyNameFromUan,
        appType,
      });
    if (companyList?.message) return;
    let companyName = companyList?.find(
      (cmp) => cmp?.trim()?.toUpperCase() == companyNameFromUan,
    );
    if (!companyName) {
      companyList = [companyNameFromUan];
      await this.CompanyRepo.createRowData({
        companyName: companyNameFromUan,
        source: epfoService,
      });
      companyName = companyNameFromUan;
    }
    const dateOfJoining = epfoData?.joiningDate
      ? epfoData?.joiningDate?.toJSON()?.substring(0, 10)
      : null;
    return {
      companyName,
      dateOfJoining,
    };
  }
}

import { HOST_URL } from 'src/constants/globals';
const calculateDate = (offset = 0) => {
  const date = new Date();
  date.setDate(date.getDate() + offset); // Adjust the date by the offset
  return date;
};

export const kEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Employment details',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Provide your professional details to know your employment.',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'fields',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 15,
            bottomRight: 15,
            topLeft: 15,
            topRight: 15,
          },
          padding: { left: 15, top: 15, right: 15, bottom: 15 },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 1,
                key: 'companyName',
                isSearch: true,
                labelText: "Company's name",
                title: "Your company's name",
                description: 'Where are you currently working?',
                options: [],
                searchApiData: {
                  endpoint: `${HOST_URL}v4/employment/searchCompany`,
                  requestType: 'GET',
                },
              },
              {
                type: 1,
                key: 'sectorId',
                padding: { top: 8 },
                isSearch: true,
                labelText: 'Employment sector',
                title: 'Employment sector',
                description: 'Sector that best describes your work',
                options: [
                  { id: 3, value: 'Homemaker' },
                  { id: 4, value: 'Hospital Natural Resources' },
                  { id: 5, value: 'Retail Sales' },
                  { id: 6, value: 'Telecom & Media' },
                  { id: 7, value: 'Agriculture & Farming' },
                  { id: 8, value: 'Leisure/Entertainment' },
                  { id: 9, value: 'Transportation' },
                  { id: 10, value: 'Home Business' },
                  { id: 11, value: 'Retired' },
                  { id: 12, value: 'Education' },
                  { id: 13, value: 'Manufaturing' },
                  { id: 14, value: 'Government' },
                  { id: 15, value: 'Information technology' },
                  { id: 16, value: 'Health care' },
                  { id: 17, value: 'Other' },
                  { id: 18, value: 'Banking & Finance' },
                  { id: 19, value: 'Services' },
                  { id: 20, value: 'Unemployed' },
                ],
              },
              {
                type: 1,
                key: 'designationId',
                padding: { top: 8 },
                isSearch: true,
                labelText: 'Designation',
                title: 'Your designation',
                description: "What's your job role?",
                options: [
                  { id: 1, value: 'Professional' },
                  { id: 2, value: 'Manager' },
                  { id: 3, value: 'Software Engineer' },
                  { id: 4, value: 'Sales' },
                  { id: 5, value: 'Driver' },
                  { id: 6, value: 'Executive' },
                  { id: 7, value: 'Office Staff' },
                  { id: 8, value: 'Skilled' },
                  { id: 9, value: 'Services' },
                  { id: 10, value: 'Construction worker' },
                  { id: 11, value: 'Factory Worker' },
                  { id: 12, value: 'Guard' },
                  { id: 13, value: 'Unskilled worker' },
                  { id: 14, value: 'Self-Employed' },
                  { id: 15, value: 'Other Occupations' },
                ],
              },
              {
                type: 6,
                key: 'empStartDate',
                padding: { top: 8 },
                labelText: 'Employment start date',
                helpText: 'EMPLOYMENT START DATE',
                firstDay: '1900-01-01 00:00:00.000',
                lastDay: '',
              },
              {
                type: 11,
                key: 'netPaySalary',
                padding: { top: 8 },
                labelText: 'Salary amount',
                hintText: 'Enter your monthly in-hand salary',
                formatters: [6],
              },
              {
                type: 6,
                key: 'lastPayDate',
                padding: { top: 8 },
                labelText: 'Last salary date',
                helpText: 'LAST SALARY DATE',
                firstDay: calculateDate(-90),
                lastDay: '',
              },
              {
                type: 6,
                key: 'nextPayDate',
                padding: { top: 8 },
                labelText: 'Next salary date',
                helpText: 'NEXT SALARY DATE',
                firstDay: '',
                lastDay: calculateDate(90),
              },
            ],
          },
        },
        {
          type: 2,
          key: 'continue',
          title: 'Continue',
          padding: { top: 20 },
          apiData: {
            endpoint: `${HOST_URL}v4/employment/submitDetails`,
            requestType: 'POST',
            ansKeys: [
              { key: 'companyName' },
              { key: 'sectorId' },
              { key: 'designationId' },
              { key: 'empStartDate' },
              { key: 'netPaySalary' },
              { key: 'lastPayDate' },
              { key: 'nextPayDate' },
            ],
            syncUserData: true,
            submitCurrentQuestion: true,
            isRedirect: true,
            canClearState: true,
          },
        },
      ],
    },
  },
};

export const kLspEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarActions: [
    {
      type: 17,
      key: 'helpButton',
      padding: { right: 16 },
      texts: [
        {
          text: 'HELP',
          style: {
            color: '0xff128391',
            fontSize: 14,
            fontWeight: 5,
            decoration: 4,
            decorationColor: '0xff128391',
          },
          clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
        },
      ],
    },
  ],
  appBarColor: '0xffFFFFFF',
  appBarElevation: 0,
  appBarLeading: {
    type: 7,
    key: 'back',
    iconCodePoint: '62834',
    color: '0xff020C0D',
    size: 28,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffFFFFFF',
  backgroundColor: '0xffFFFFFF',
  body: {
    type: 15,
    key: 'body',
    physics: 3,
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 3,
      key: 'view',
      crossAxisAlignment: 4,
      children: [
        {
          type: 17,
          key: 'title',
          texts: [
            {
              text: 'Employment details',
              style: { color: '0xff020C0D', fontSize: 26, fontWeight: 6 },
            },
          ],
        },
        {
          type: 17,
          key: 'description',
          texts: [
            {
              text: 'This step is quick and helps you get the best loan terms based on your employment.',
              style: { color: '0xff718589', fontSize: 14, fontWeight: 5 },
            },
          ],
        },
        {
          type: 18,
          key: 'separator',
          padding: { top: 30 },
          physics: 5,
          fields: [
            {
              type: 10,
              key: 'companyName',
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/company_name.svg',
              labelText: "Company's Name",
              title: "Your Company's Name",
              description: 'Where are you currently working?',
              options: [],
              searchApiData: {
                endpoint: `${HOST_URL}v4/employment/searchCompany`,
                requestType: 'GET',
              },
            },
            {
              type: 10,
              key: 'sectorId',
              padding: { top: 15 },
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/company_name.svg',
              labelText: 'Employment sector',
              title: 'Employment sector',
              description: 'Sector that best describes your work',
              options: [
                { id: 3, value: 'Homemaker' },
                { id: 4, value: 'Hospital Natural Resources' },
                { id: 5, value: 'Retail Sales' },
                { id: 6, value: 'Telecom & Media' },
                { id: 7, value: 'Agriculture & Farming' },
                { id: 8, value: 'Leisure/Entertainment' },
                { id: 9, value: 'Transportation' },
                { id: 10, value: 'Home Business' },
                { id: 11, value: 'Retired' },
                { id: 12, value: 'Education' },
                { id: 13, value: 'Manufaturing' },
                { id: 14, value: 'Government' },
                { id: 15, value: 'Information technology' },
                { id: 16, value: 'Health care' },
                { id: 17, value: 'Other' },
                { id: 18, value: 'Banking & Finance' },
                { id: 19, value: 'Services' },
                { id: 20, value: 'Unemployed' },
              ],
            },
            {
              type: 10,
              key: 'designationId',
              padding: { top: 15 },
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/job_type.svg',
              labelText: 'Designation',
              title: 'Your designation',
              description: "What's your job role?",
              options: [
                { id: 1, value: 'Professional' },
                { id: 2, value: 'Manager' },
                { id: 3, value: 'Software Engineer' },
                { id: 4, value: 'Sales' },
                { id: 5, value: 'Driver' },
                { id: 6, value: 'Executive' },
                { id: 7, value: 'Office Staff' },
                { id: 8, value: 'Skilled' },
                { id: 9, value: 'Services' },
                { id: 10, value: 'Construction worker' },
                { id: 11, value: 'Factory Worker' },
                { id: 12, value: 'Guard' },
                { id: 13, value: 'Unskilled worker' },
                { id: 14, value: 'Self-Employed' },
                { id: 15, value: 'Other Occupations' },
              ],
            },
            {
              type: 5,
              key: 'empStartDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Employment start date',
              title: 'Employment start date',
              description: 'When did you join the current company?',
              firstDay: '1900-01-01 00:00:00.000',
              lastDay: '',
            },
            {
              type: 9,
              key: 'netPaySalary',
              padding: { top: 15 },
              labelText: 'Salary amount',
              hintText: 'Enter your monthly in-hand salary',
              formatters: [6],
            },
            {
              type: 5,
              key: 'lastPayDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Last salary date',
              title: 'Most recent salary date',
              description: 'When did you get your last salary?',
              firstDay: calculateDate(-90),
              lastDay: '',
            },
            {
              type: 5,
              key: 'nextPayDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Next salary date',
              title: 'Next salary date',
              description: 'What is your next salary date?',
              firstDay: '',
              lastDay: calculateDate(90),
            },
          ],
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 4,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffFFFFFF',
    border: {
      top: { color: '0x8095B0B5', width: 0.5 },
    },
    child: {
      type: 1,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/employment/submitDetails`,
        requestType: 'POST',
        ansKeys: [
          { key: 'companyName' },
          { key: 'sectorId' },
          { key: 'designationId' },
          { key: 'empStartDate' },
          { key: 'netPaySalary' },
          { key: 'lastPayDate' },
          { key: 'nextPayDate' },
        ],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};

export const kReapplyEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Welcome back',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Fill the below mentioned details to get a loan',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'basicDetails',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 4,
                children: [
                  {
                    type: 15,
                    key: 'firstRow',
                    isFlexible: true,
                    crossAxisAlignment: 2,
                    mainAxisAlignment: 6,
                    children: [
                      {
                        type: 19,
                        key: 'number',
                        texts: [
                          {
                            text: '1',
                            style: {
                              color: '0xff000000',
                              fontSize: 20,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'title',
                        isFlexible: true,
                        padding: { left: 25 },
                        texts: [
                          {
                            text: 'Basic details',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                  {
                    type: 9,
                    key: 'check',
                    iconCodePoint: '57686',
                    color: '0xff000000',
                  },
                ],
              },
            ],
          },
        },
        {
          type: 5,
          key: 'employment',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 15 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '2',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Employment',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                type: 5,
                key: 'divider',
                height: 1.5,
                rWidth: 1.0,
                color: '0xffEBEBEB',
              },
              {
                type: 12,
                key: 'isSameCompany',
                padding: { bottom: 5 },
                fillColor: '0xffffffff',
                labelText: 'Are you still working at',
                hintText: 'Previous Company Name',
                options: [
                  { id: 0, value: 'Yes' },
                  { id: 1, value: 'No' },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 5,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffF1F1F1',
    child: {
      type: 2,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/employment/submitDetails`,
        requestType: 'POST',
        ansKeys: [{ key: 'isSameCompany' }],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};

export const KPreApprovedLoanScreenObject = {
  // MARK: App Bar
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'backBtn',
    iconCodePoint: '62832',
    color: '0xff000000',
    size: 23,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  // MARK: Body
  body: {
    type: 17,
    key: 'body',
    padding: { left: 20, top: 0, right: 20, bottom: 15 },
    child: {
      type: 4,
      key: 'columnView',
      crossAxisAlignment: 4,
      children: [
        // MARK: Title
        {
          type: 19,
          key: 'title',
          texts: [
            {
              text: 'Curated Loan Options',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        // MARK: Description
        {
          type: 19,
          key: 'description',
          padding: { top: 3, bottom: 15 },
          texts: [
            {
              text: 'One decision, two great choices',
              style: { color: '0xff000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        // MARK: Pre Approved Card
        {
          type: 22,
          key: 'preAppStackCard',
          padding: { top: 15, bottom: 15 },
          alignment: 7,
          children: [
            {
              type: 5,
              key: 'preApprovedMainCard',
              margin: { top: 6 },
              padding: { left: 4, top: 4, right: 4, bottom: 4 },
              borderRadius: {
                bottomLeft: 6,
                bottomRight: 6,
                topLeft: 6,
                topRight: 6,
              },
              boxShadow: {
                color: '0x26000000',
                blurRadius: 1,
                spreadRadius: 0,
                x: 1,
                y: 1,
              },
              rWidth: 1,
              child:
                // MARK: Pre Approved Inner card
                {
                  type: 5,
                  key: 'preApprovedSubCard',
                  padding: { left: 14, top: 14, right: 14, bottom: 14 },
                  borderRadius: {
                    bottomLeft: 4,
                    bottomRight: 4,
                    topLeft: 4,
                    topRight: 4,
                  },
                  gradient: {
                    gradientColors: ['0xffFFFFFF', '0xffE2EAFF'],
                    begin: 7,
                    end: 1,
                  },
                  rWidth: 1,
                  child: {
                    type: 4,
                    key: 'preApprovedColumn',
                    crossAxisAlignment: 4,
                    children: [
                      // MARK: First Row
                      {
                        type: 15,
                        crossAxisAlignment: 3,
                        mainAxisAlignment: 4,
                        padding: { top: 30 },
                        children: [
                          // Just Tap Chip
                          {
                            type: 5,
                            key: 'justTapChip',
                            margin: { bottom: 5 },
                            padding: {
                              left: 10,
                              top: 8,
                              right: 10,
                              bottom: 8,
                            },
                            borderRadius: {
                              bottomLeft: 20,
                              bottomRight: 20,
                              topLeft: 20,
                              topRight: 20,
                            },
                            gradient: {
                              gradientColors: ['0x1A053ED1', '0x00053ED1'],
                              begin: 5,
                              end: 6,
                            },
                            child: {
                              type: 19,
                              key: 'justTapText',
                              texts: [
                                {
                                  text: 'Just tap & Cash in',
                                  style: {
                                    color: '0xff053ED1',
                                    fontSize: 10,
                                    fontWeight: 5,
                                  },
                                },
                              ],
                            },
                          },
                          {
                            type: 10,
                            key: 'preAppImg',
                            height: 76,
                            imageURL:
                              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626206369.webp',
                          },
                        ],
                      },
                      // MARK: Text Column
                      {
                        type: 19,
                        key: 'preAppCardText1',
                        padding: { top: 10 },
                        texts: [
                          {
                            text: 'No waiting, No hassle',
                            style: {
                              color: '0xff000000',
                              fontSize: 12,
                              fontWeight: 4,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'preAppCardText2',
                        padding: { bottom: 20 },
                        texts: [
                          {
                            text: 'Pre-approved loan of ₹50,000',
                            style: {
                              color: '0xff000000',
                              fontSize: 16,
                              fontWeight: 6,
                            },
                          },
                        ],
                      },
                      // MARK: Continue BTN
                      {
                        type: 2,
                        key: 'preApproveApplyNow',
                        title: 'Apply Now',
                        apiData: {
                          endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                          requestType: 'POST',
                          syncUserData: true,
                          isRedirect: true,
                          canClearState: true,
                          extraData: {
                            preApproveFlow: true,
                          },
                        },
                      },
                    ],
                  },
                },
            },
            {
              type: 10,
              key: 'blueTagImg',
              height: 26,
              imageURL:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751627658459.webp',
            },
            {
              type: 19,
              key: 'blueTagText',
              padding: { top: 5 },
              texts: [
                {
                  text: 'READY TO DISBURSE',
                  style: {
                    color: '0xffFFFFFF',
                    fontSize: 10,
                    fontWeight: 6,
                  },
                },
              ],
            },
          ],
        },
        // MARK: Upgrade Loan Card
        {
          type: 22,
          key: 'preAppStackCard',
          padding: { top: 15 },
          alignment: 7,
          children: [
            {
              type: 5,
              key: 'upgradeLoanMainCard',
              margin: { top: 6 },
              padding: { left: 4, top: 4, right: 4, bottom: 4 },
              borderRadius: {
                bottomLeft: 6,
                bottomRight: 6,
                topLeft: 6,
                topRight: 6,
              },
              boxShadow: {
                color: '0x26000000',
                blurRadius: 1,
                spreadRadius: 0,
                x: 1,
                y: 1,
              },
              rWidth: 1,
              child:
                // MARK: Upgrade Loan Inner card
                {
                  type: 5,
                  key: 'upgradeLoanSubCard',
                  padding: { left: 14, top: 14, right: 14, bottom: 14 },
                  borderRadius: {
                    bottomLeft: 4,
                    bottomRight: 4,
                    topLeft: 4,
                    topRight: 4,
                  },
                  gradient: {
                    gradientColors: ['0xffFFFFFF', '0xffEAFFEA'],
                    begin: 7,
                    end: 1,
                  },
                  rWidth: 1,
                  child: {
                    type: 4,
                    key: 'upgradeLoanColumn',
                    crossAxisAlignment: 4,
                    children: [
                      // MARK: First Row
                      {
                        type: 15,
                        crossAxisAlignment: 3,
                        mainAxisAlignment: 4,
                        padding: { top: 30 },
                        children: [
                          // Just Tap Chip
                          {
                            type: 5,
                            key: 'quickVerifyChip',
                            margin: { bottom: 5 },
                            padding: {
                              left: 10,
                              top: 8,
                              right: 10,
                              bottom: 8,
                            },
                            borderRadius: {
                              bottomLeft: 20,
                              bottomRight: 20,
                              topLeft: 20,
                              topRight: 20,
                            },
                            gradient: {
                              gradientColors: ['0x1A008800', '0x00008800'],
                              begin: 5,
                              end: 6,
                            },
                            child: {
                              type: 19,
                              key: 'quickVerifyText',
                              texts: [
                                {
                                  text: 'Quick Verification',
                                  style: {
                                    color: '0xff008800',
                                    fontSize: 10,
                                    fontWeight: 5,
                                  },
                                },
                              ],
                            },
                          },
                          {
                            type: 10,
                            key: 'upgradeLoanImg',
                            height: 80,
                            imageURL:
                              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751627735881.webp',
                          },
                        ],
                      },
                      // MARK: Text Column
                      {
                        type: 19,
                        key: 'upgradeLoanText1',
                        padding: { top: 10 },
                        texts: [
                          {
                            text: 'Validate Employment & Income to',
                            style: {
                              color: '0xff000000',
                              fontSize: 12,
                              fontWeight: 4,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'upgradeLoanText2',
                        padding: { bottom: 20 },
                        texts: [
                          {
                            text: 'Access up to ₹3,00,000',
                            style: {
                              color: '0xff000000',
                              fontSize: 16,
                              fontWeight: 6,
                            },
                          },
                        ],
                      },
                      // MARK: Continue BTN
                      {
                        type: 2,
                        key: 'upgradeLoanApplyNow',
                        title: 'Apply Now',
                        apiData: {
                          endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                          requestType: 'POST',
                          syncUserData: true,
                          isRedirect: true,
                          canClearState: true,
                          extraData: {
                            preApproveFlow: false,
                          },
                        },
                      },
                    ],
                  },
                },
            },
            {
              type: 10,
              key: 'greenTagImg',
              height: 26,
              imageURL:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626151796.webp',
            },
            {
              type: 19,
              key: 'greenTagText',
              padding: { top: 5 },
              texts: [
                {
                  text: 'UPGRADE & UNLOCK MORE',
                  style: {
                    color: '0xffFFFFFF',
                    fontSize: 10,
                    fontWeight: 6,
                  },
                },
              ],
            },
          ],
        },
        // MARK: Safe & Secure
        {
          type: 15,
          mainAxisAlignment: 1,
          mainAxisSize: 1,
          padding: { top: 35, bottom: 10 },
          children: [
            {
              type: 10,
              key: 'safeSecureSvg',
              svgURL:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626244766.svg',
            },
            {
              type: 19,
              key: 'safeAndSecure',
              padding: { left: 8 },
              texts: [
                {
                  text: 'Safe & secure',
                  style: {
                    color: '0x66000000',
                    fontSize: 12,
                    fontWeight: 4,
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  },
};

export const KLspPreApprovedLoanScreenObject = {
  // MARK: App Bar
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 7,
    key: 'backBtn',
    iconCodePoint: '62834',
    color: '0xff020C0D',
    size: 28,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  // MARK: Body
  body: {
    type: 15,
    key: 'body',
    padding: { left: 20, top: 0, right: 20, bottom: 15 },
    child: {
      type: 3,
      key: 'columnView',
      crossAxisAlignment: 4,
      children: [
        // MARK: Title
        {
          type: 17,
          key: 'title',
          texts: [
            {
              text: 'Exclusive Loan Offers ',
              style: { color: '0xff020C0D', fontSize: 25, fontWeight: 6 },
            },
          ],
        },
        // MARK: Description
        {
          type: 17,
          key: 'description',
          padding: { top: 3, bottom: 15 },
          texts: [
            {
              text: 'Let’s pick your perfect match',
              style: { color: '0xff718589', fontSize: 13, fontWeight: 5 },
            },
          ],
        },
        // MARK: Pre Approved Card
        {
          type: 4,
          key: 'preApprovedMainCard',
          margin: { top: 15, bottom: 15 },
          padding: { left: 5, top: 5, right: 5, bottom: 5 },
          borderRadius: {
            bottomLeft: 15,
            bottomRight: 15,
            topLeft: 15,
            topRight: 15,
          },
          boxShadow: {
            color: '0x1A000000',
            blurRadius: 10,
            spreadRadius: 0,
            x: 1,
            y: 1,
          },
          rWidth: 1,
          child:
            // MARK: Pre Approved Inner card
            {
              type: 4,
              key: 'preApprovedSubCard',
              borderRadius: {
                bottomLeft: 13,
                bottomRight: 13,
                topLeft: 13,
                topRight: 13,
              },
              gradient: {
                gradientColors: ['0xff78DAE6', '0xffC9FFF0'],
                begin: 7,
                end: 1,
              },
              rWidth: 1,
              child: {
                type: 3,
                key: 'preApprovedMainColumn',
                mainAxisAlignment: 6,
                crossAxisAlignment: 2,
                children: [
                  // MARK: Top Banner SVG
                  {
                    type: 8,
                    key: 'preAppTopBanner',
                    rWidth: 1,
                    height: 100,
                    imageURL:
                      'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751632210807.webp',
                  },
                  // MARK: Pre Approve Inner Column
                  {
                    type: 3,
                    key: 'preApprovedInnerColumn',
                    crossAxisAlignment: 2,
                    padding: { left: 15, top: 0, right: 15, bottom: 15 },
                    children: [
                      // MARK: Just Tap Chip
                      {
                        type: 4,
                        key: 'justTapChip',
                        margin: { top: 13 },
                        padding: {
                          left: 15,
                          top: 10,
                          right: 15,
                          bottom: 10,
                        },
                        borderRadius: {
                          bottomLeft: 40,
                          bottomRight: 40,
                          topLeft: 40,
                          topRight: 40,
                        },
                        child: {
                          type: 17,
                          key: 'justTapText',
                          texts: [
                            {
                              text: 'JUST A TAP AWAY',
                              style: {
                                color: '0xff128391',
                                fontSize: 10,
                                fontWeight: 7,
                              },
                            },
                          ],
                        },
                      },
                      // MARK: Text Column
                      {
                        type: 17,
                        key: 'preAppCardText1',
                        padding: { top: 10 },
                        texts: [
                          {
                            text: 'Congratulations!',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 4,
                            },
                          },
                        ],
                      },
                      {
                        type: 17,
                        key: 'preAppCardText2',
                        texts: [
                          {
                            text: 'Pre-approved loan of ₹50,000',
                            style: {
                              color: '0xff000000',
                              fontSize: 16,
                              fontWeight: 7,
                            },
                          },
                        ],
                      },
                      // MARK: Get Now BTN
                      {
                        type: 4,
                        key: 'preAppBtn',
                        margin: { top: 17 },
                        borderRadius: {
                          bottomLeft: 10,
                          bottomRight: 10,
                          topLeft: 10,
                          topRight: 10,
                        },
                        boxShadow: {
                          color: '0x40000000',
                          blurRadius: 4,
                          spreadRadius: 0,
                          x: 0,
                          y: 3,
                        },
                        child: {
                          type: 1,
                          key: 'preApproveGetNow',
                          title: 'Get Now',
                          apiData: {
                            endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                            requestType: 'POST',
                            syncUserData: true,
                            isRedirect: true,
                            canClearState: true,
                            extraData: {
                              isPreApproval: true,
                            },
                          },
                        },
                      },
                    ],
                  },
                ],
              },
            },
        },

        // MARK: Dotted OR Line
        {
          type: 13,
          key: 'orRow',
          children: [
            {
              type: 22,
              key: 'leftDotted',
              isFlexible: true,
              flex: 4,
              color: '0xff718589',
              dashPattern: [5, 10],
            },
            {
              type: 17,
              key: 'orText',
              isFlexible: true,
              padding: { left: 7, right: 7 },
              texts: [
                {
                  text: 'OR',
                  style: {
                    color: '0xff718589',
                    fontSize: 13,
                    fontWeight: 4,
                  },
                },
              ],
            },
            {
              type: 22,
              key: 'rightDotted',
              isFlexible: true,
              flex: 4,
              color: '0xff718589',
              dashPattern: [5, 10],
            },
          ],
        },

        // MARK: Upgrade Loan Card
        {
          type: 4,
          key: 'upgradeLoanMainCard',
          margin: { top: 15 },
          padding: { left: 5, top: 5, right: 5, bottom: 5 },
          borderRadius: {
            bottomLeft: 15,
            bottomRight: 15,
            topLeft: 15,
            topRight: 15,
          },
          boxShadow: {
            color: '0x1A000000',
            blurRadius: 10,
            spreadRadius: 0,
            x: 1,
            y: 1,
          },
          rWidth: 1,
          child:
            // MARK: Upgrade Loan Inner card
            {
              type: 4,
              key: 'upgradeLoanSubCard',
              borderRadius: {
                bottomLeft: 13,
                bottomRight: 13,
                topLeft: 13,
                topRight: 13,
              },
              gradient: {
                gradientColors: ['0xffFFC9F6', '0xffFCD446'],
                begin: 5,
                end: 6,
              },
              rWidth: 1,
              child: {
                type: 3,
                key: 'upgradeLoanMainColumn',
                mainAxisAlignment: 6,
                crossAxisAlignment: 2,
                children: [
                  // MARK: Top Banner SVG
                  {
                    type: 8,
                    key: 'upgradeLoanTopBanner',
                    rWidth: 1,
                    height: 100,
                    imageURL:
                      'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751632179510.webp',
                  },
                  // MARK: Upgrade Loan Inner Column
                  {
                    type: 3,
                    key: 'upgradeLoanInnerColumn',
                    crossAxisAlignment: 2,
                    padding: { left: 15, top: 0, right: 15, bottom: 15 },
                    children: [
                      // MARK: Quick Verify Chip
                      {
                        type: 4,
                        key: 'quickVerifyChip',
                        margin: { top: 13 },
                        padding: {
                          left: 20,
                          top: 10,
                          right: 20,
                          bottom: 10,
                        },
                        color: '0x00FFFFFF',
                        borderRadius: {
                          bottomLeft: 40,
                          bottomRight: 40,
                          topLeft: 40,
                          topRight: 40,
                        },
                        border: {
                          left: { color: '0x1A000000', width: 1 },
                          top: { color: '0x1A000000', width: 1 },
                          right: { color: '0x1A000000', width: 1 },
                          bottom: { color: '0x1A000000', width: 1 },
                        },
                        child: {
                          type: 17,
                          key: 'quickVerifyText',
                          texts: [
                            {
                              text: 'QUICK VERIFICATION',
                              style: {
                                color: '0xff000000',
                                fontSize: 10,
                                fontWeight: 7,
                              },
                            },
                          ],
                        },
                      },
                      // MARK: Text Column
                      {
                        type: 17,
                        key: 'upgradeLoanCardText1',
                        padding: { top: 10 },
                        texts: [
                          {
                            text: 'Verify your employment and income to',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 4,
                            },
                          },
                        ],
                      },
                      {
                        type: 17,
                        key: 'upgradeLoanCardText2',
                        texts: [
                          {
                            text: 'Unlock up to ₹3,00,000',
                            style: {
                              color: '0xff000000',
                              fontSize: 16,
                              fontWeight: 7,
                            },
                          },
                        ],
                      },
                      // MARK: Get Now BTN
                      {
                        type: 4,
                        key: 'upgradeLoanBtn',
                        margin: { top: 17 },
                        borderRadius: {
                          bottomLeft: 10,
                          bottomRight: 10,
                          topLeft: 10,
                          topRight: 10,
                        },
                        boxShadow: {
                          color: '0x40000000',
                          blurRadius: 4,
                          spreadRadius: 0,
                          x: 0,
                          y: 3,
                        },
                        child: {
                          type: 1,
                          key: 'upgradeLoanGetNow',
                          title: 'Get Now',
                          titleColor: '0xff000000',
                          backgroundColor: '0xffFFFFFF',
                          apiData: {
                            endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                            requestType: 'POST',
                            syncUserData: true,
                            isRedirect: true,
                            canClearState: true,
                            extraData: {
                              isPreApproval: false,
                            },
                          },
                        },
                      },
                    ],
                  },
                ],
              },
            },
        },
      ],
    },
  },
};

// Imports
import Admin from 'firebase-admin';
import * as fs from 'fs';
import * as FormData from 'form-data';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import { HypothecationPaymentsEntity } from 'src/entities/hypothecationPayment.entity';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import {
  CIBIL_ADMIN_ID,
  MIN_RAZORPAY_BALANCE,
  PAGE_LIMIT,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { kMaskAccount, kMaskPhone } from 'src/constants/strings';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
  kSuccessData,
  kNoDataFound,
} from 'src/constants/responses';
import { admin } from 'src/entities/admin.entity';
import * as str from 'src/constants/strings';
import { employmentDesignation } from 'src/entities/designation.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { APILogger } from 'src/entities/api_logger.schema';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { EMIRepository } from 'src/repositories/emi.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { LocationRepository } from 'src/repositories/location.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { StampRepository } from 'src/repositories/stamp.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import {
  gIsPROD,
  sortingColumnOptionForTodayDisbursement,
} from 'src/constants/globals';
import { DeviceRepository } from 'src/repositories/device.repositoy';
import { DeviceInfoInstallAppRepository } from 'src/repositories/deviceInfoInstallApp.repository';
let firebaseDB: Admin.firestore.Firestore;
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { EmiEntity } from 'src/entities/emi.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { MediaRepository } from 'src/repositories/media.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { BankingRepository } from 'src/repositories/banking.repository';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { DateService } from 'src/utils/date.service';
import { loanTransaction } from 'src/entities/loan.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { SequelOptions } from 'src/interfaces/include.options';
import { MasterEntity } from 'src/entities/master.entity';
import { EnvConfig } from 'src/configs/env.config';
import { esignEntity } from 'src/entities/esign.entity';
import { APIService } from 'src/utils/api.service';
import { kErrorMsgs } from 'src/constants/strings';
import {
  MSG91_BALANCE_URL,
  nCibilRiskCategoryFromBatch,
  nRazorXFetchBalance,
  nWaWalletCheck,
} from 'src/constants/network';
import { RedisService } from 'src/redis/redis.service';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import {
  confirmation_phrases,
  kCollectionTabUploadDocList,
  kLegalTabUploadDocList,
  kLoanTabUploadDocList,
  kLspMSG91Headers,
  kMSG91Headers,
  RedisKeys,
  QUALITY_PARAMETER_ROLE_ID,
  QUALITY_PARAMETER_ROLE_ID_UAT,
  QUALITY_PARAMETER_START_DATE,
  LENDER_EMI_REMINDER_WINDOW_DAYS,
} from 'src/constants/objects';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { StringService } from 'src/utils/string.service';
import { NUMBERS } from 'src/constants/numbers';
import { ErrorContextService } from 'src/utils/error.context.service';
import { FileService } from 'src/utils/file.service';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';

@Injectable()
export class DashboardService {
  constructor(
    private readonly StampRepo: StampRepository,
    private readonly locationRepo: LocationRepository,
    private readonly loanRepo: LoanRepository,
    private readonly emiRepo: EMIRepository,
    private readonly typeService: TypeService,
    private readonly bankRepo: BankingRepository,
    private readonly strService: StringService,
    private readonly cryptService: CryptService,
    private readonly disbursmentRepo: DisbursmentRepository,
    private readonly mediaRepo: MediaRepository,
    private readonly userRepo: UserRepository,
    private readonly transactionRepo: TransactionRepository,
    private readonly deviceRepo: DeviceRepository,
    private readonly dateService: DateService,
    private readonly repoManager: RepositoryManager,
    // Shared services
    private readonly commonServices: CommonSharedService,
    private readonly deviceAppInfoRepo: DeviceInfoInstallAppRepository,
    @Inject(forwardRef(() => CalculationSharedService))
    private readonly sharedCalculation: CalculationSharedService,
    private readonly api: APIService,
    private readonly redis: RedisService,
    private readonly razorpay: RazorpoayService,
    private readonly slack: SlackService,
    private readonly errorContextService: ErrorContextService,
    private readonly fileService: FileService,
    private readonly elephantService: ElephantService,
  ) {
    if (process.env.MODE == 'PROD' || process.env.MODE == 'UAT')
      firebaseDB = Admin.firestore();
  }

  async fetchAllStampDetails(query) {
    try {
      const options: any = this.prepareStampDetailsOptions(query);
      if (options?.message) return kInternalError;
      const stampCount = await this.getStampCount();
      if (stampCount?.message) return stampCount;
      const stampDetailsData = await this.getStampDetailsData(options);
      if (stampDetailsData?.message) return stampDetailsData;
      const finalStampData: any = this.prepareStampFinalData(
        stampDetailsData.rows,
      );
      if (finalStampData?.message) return finalStampData;
      return { stampCount, finalStampData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  prepareStampDetailsOptions(query) {
    try {
      const page = query.page ?? 1;
      const status = query.status ?? 1;
      const search = query.search ?? '';
      let conditions;
      if (status == 0 && !search)
        conditions = {
          takenStatus: { [Op.eq]: '0' },
        };
      else if (status == 0 && search && search.length >= 3)
        conditions = {
          takenStatus: { [Op.eq]: '0' },
          certificateNo: { [Op.iRegexp]: search },
        };
      else if (status == 1 && !search) conditions = null;
      else if (status == 1 && search && search.length >= 3)
        conditions = { certificateNo: { [Op.iRegexp]: search } };
      else if (status == 2 && !search)
        conditions = {
          takenStatus: { [Op.eq]: '1' },
          signStatus: { [Op.eq]: '0' },
        };
      else if (status == 2 && search && search.length >= 3)
        conditions = {
          takenStatus: { [Op.eq]: '1' },
          signStatus: { [Op.eq]: '0' },
          certificateNo: { [Op.iRegexp]: search },
        };
      else if (status == 3 && !search)
        conditions = {
          signStatus: { [Op.eq]: '1' },
        };
      else if (status == 3 && search && search.length >= 3)
        conditions = {
          signStatus: { [Op.eq]: '1' },
          certificateNo: { [Op.iRegexp]: search },
        };
      const skip1 = page * PAGE_LIMIT - PAGE_LIMIT;
      const options = {
        offset: skip1,
        limit: PAGE_LIMIT,
        order: [['createdAt', 'DESC']],
        where: conditions,
      };
      return options;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getStampCount() {
    try {
      const stampCount: any = {};
      stampCount.all = await this.StampRepo.getCountsWhere({});
      stampCount.free = await this.StampRepo.getCountsWhere({
        where: { takenStatus: '0' },
      });
      stampCount.inUse = await this.StampRepo.getCountsWhere({
        where: { takenStatus: '1', signStatus: '0' },
      });
      stampCount.used = await this.StampRepo.getCountsWhere({
        where: { signStatus: '1' },
      });
      return stampCount;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async getStampDetailsData(options) {
    try {
      const attr = [
        'id',
        'certificateNo',
        'certificateIssuedDate',
        'accountReference',
        'uniqueDocReference',
        'purchasedBy',
        'descOfDoc',
        'description',
        'considerationPrice',
        'firstParty',
        'secondParty',
        'stampDutyPaidBy',
        'stampDutyAmount',
        'stampId',
        'stampImage',
        'takenStatus',
        'takenStatusDate',
        'signStatus',
        'signStatusDate',
      ];
      const stampData = await this.StampRepo.getTableWhereDataWithCounts(
        attr,
        options,
      );
      if (stampData == k500Error) return kInternalError;
      return stampData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  prepareStampFinalData(list) {
    try {
      const finalData = [];
      list.forEach((ele) => {
        try {
          const tempData = {};
          if (ele?.certificateIssuedDate == '') ele.certificateIssuedDate = '-';
          if (ele?.uniqueDocReference == '') ele.uniqueDocReference = '-';
          if (ele?.purchasedBy == '') ele.purchasedBy = '-';
          tempData['Stamp id'] = ele?.id ?? '-';
          tempData['Stampduty paid by'] = ele?.stampDutyPaidBy ?? '-';
          tempData['Stampduty amount'] = ele?.stampDutyAmount ?? '-';
          tempData['Certificate no'] = ele?.certificateNo ?? '-';
          tempData['Certificate issued date'] =
            ele?.certificateIssuedDate ?? '-';
          tempData['Account reference'] = ele?.accountReference ?? '-';
          tempData['Unique doc reference'] = ele?.uniqueDocReference ?? '-';
          tempData['Purchased by'] = ele?.purchasedBy ?? '-';
          tempData['Desc of doc'] = ele?.descOfDoc ?? '-';
          tempData['Consideration price'] = ele?.considerationPrice ?? '-';
          tempData['First party'] = ele?.firstParty ?? '-';
          finalData.push(tempData);
        } catch (error) {}
      });
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funGetLocationHistory(query) {
    try {
      const userId = query.userId;
      const attributes = [
        'id',
        'userId',
        'location',
        'lat',
        'long',
        'bearing',
        'createdAt',
      ];
      const options = { where: { userId } };
      const locationData = await this.locationRepo.getTableWhereData(
        attributes,
        options,
      );
      if (locationData === k500Error) return kInternalError;
      return locationData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region
  // get all disbursed loans
  async allDisbursedLoans(query) {
    query.sortingOrder =
      query.sortingOrder == -1 ? 'DESC NULLS LAST' : 'ASC NULLS LAST';
    query.sortingByDbColumn =
      sortingColumnOptionForTodayDisbursement[query.sortingBy];

    const adminId = query.hAdmin;

    let findData: any = await this.findLoanData(query);
    let totalData: any;
    if (query?.count === 'true')
      totalData = await this.getDisbursedTotalData(query);

    if (totalData === k500Error) return kInternalError;
    if (findData.message) return findData;

    const prePareData: any = await this.prePareAllDisbursedData(
      findData,
      adminId,
    );
    if (totalData?.message) return totalData;
    if (prePareData?.message) return prePareData;
    let preParetotalData: any;
    if (totalData) preParetotalData = this.prePareDisbursedTotalData(totalData);
    if (preParetotalData?.message) return preParetotalData;

    const count = findData?.count;
    if (query?.needFileUrl === 'true' && query?.download === 'true') {
      const rawExcelData = {
        sheets: ['all_disbursedLoan_report'],
        data: [prePareData],
        sheetName: 'all-disbursedLoan-report.xlsx',
      };
      const fileUrl = await this.fileService.objectToExcelURL(rawExcelData);
      if (fileUrl.message) fileUrl;
      return { fileUrl };
    }
    return {
      count,
      rows: prePareData,
      ...preParetotalData,
      sortList: Object.keys(sortingColumnOptionForTodayDisbursement),
    };
  }
  //#endregion

  //#region
  // find loan data
  private async findLoanData(query) {
    const options = this.prePareOptions(query);
    let searchText: string = query?.searchText ?? '';
    // minimum 3 characters required for searching
    if (searchText.length < 3) searchText = '';
    if (searchText && searchText.startsWith('l-')) {
      searchText = searchText.replace('l-', '');
      options.where.id = +searchText;
      searchText = '';
    }
    if (options.message) return options;
    const include = this.getAllDisbursedInlcued(searchText);
    options.include = include;
    const attributes = [
      'id',
      'interestRate',
      'approvedDuration',
      'netApprovedAmount',
      'loan_disbursement_date',
      'stampFees',
      'loanFees',
      'completedLoan',
      'charges',
      'insuranceDetails',
      'insuranceId',
      'qualityParameters',
      'appType',
      'processingFees',
      'categoryTag',
      'eligibilityDetails',
    ];

    //change option.order for varies sorting
    if (query.sortingBy) {
      let sortingColumn;
      if (attributes.includes(query.sortingByDbColumn)) {
        if (query.sortingByDbColumn == 'loan_disbursement_date')
          sortingColumn = Sequelize.literal(
            `"loanTransaction"."${query.sortingByDbColumn}"::date`,
          );
        else
          sortingColumn = Sequelize.literal(
            `"loanTransaction"."${query.sortingByDbColumn}"::numeric`,
          );
      } else if (
        query.sortingByDbColumn == 'fullName' &&
        query.sortingBy == 'LoanApprovedBy'
      )
        sortingColumn = Sequelize.literal(
          `"adminData"."${query.sortingByDbColumn}"`,
        );
      else if (['fullName', 'gender'].includes(query.sortingByDbColumn))
        sortingColumn = Sequelize.literal(
          `"registeredUsers"."${query.sortingByDbColumn}"`,
        );
      else if (['cibilScore', 'plScore'].includes(query.sortingByDbColumn))
        sortingColumn = Sequelize.literal(
          `"cibilData"."${query.sortingByDbColumn}"`,
        );
      else if (['salary', 'salaryDate'].includes(query.sortingByDbColumn))
        sortingColumn = Sequelize.literal(
          `"bankingData"."${query.sortingByDbColumn}"`,
        );
      options.order = [[sortingColumn, query.sortingOrder]];
    }

    const find = await this.loanRepo.getTableWhereDataWithCounts(
      attributes,
      options,
    );
    if (!find || find === k500Error) return kInternalError;
    const loanIds = find.rows.map((loan) => loan.id);
    const where = { loanId: loanIds };
    // Hit -> Query
    const emiData: any = await this.emiRepo.getTableWhereData(
      [
        'id',
        'loanId',
        'emi_amount',
        'principalCovered',
        'interestCalculate',
        'penalty',
      ],
      { where },
    );
    // Validation -> Query data
    if (emiData == k500Error) throw new Error();

    const att = [
      'id',
      'loanId',
      'payout_id',
      'amount',
      'account_number',
      'ifsc',
      'bank_name',
    ];
    const disbData = await this.disbursmentRepo.getTableWhereData(att, {
      where,
    });
    // Validation -> Query data
    if (disbData === k500Error) throw new Error();

    //get emi data and disbursement data for loan
    find.rows.map((loan) => {
      try {
        let emis = emiData.filter((emi) => emi.loanId == loan.id);
        loan.emiData = emis;
        loan.disbursementData = [disbData.find((f) => f.loanId === loan.id)];
      } catch (error) {}
    });

    return find;
  }
  //#endregion

  //#region prePare Options
  private prePareOptions(query) {
    try {
      const startDate = this.typeService
        .getGlobalDate(query?.start_date ?? new Date())
        .toJSON();
      const endDate = this.typeService
        .getGlobalDate(query?.end_date ?? new Date())
        .toJSON();
      const type = (query?.type ?? '').toUpperCase();
      const where: any = {};
      where.loan_disbursement = '1';
      where.loan_disbursement_date = { [Op.gte]: startDate, [Op.lte]: endDate };
      const options: any = { where };
      if (query?.download != 'true') {
        const page = +(query?.page ?? 1);
        options.offset = page * PAGE_LIMIT - PAGE_LIMIT;
        options.limit = PAGE_LIMIT;
      }
      options.order = [
        ['id', 'DESC'],
        ['loan_disbursement_date', 'DESC'],
      ];
      if (type === 'SYSTEM')
        options.where.manualVerificationAcceptId = {
          [Op.or]: [CIBIL_ADMIN_ID, SYSTEM_ADMIN_ID, null],
        };
      else if (type === 'MANUAL')
        options.where.manualVerificationAcceptId = {
          [Op.notIn]: [CIBIL_ADMIN_ID, SYSTEM_ADMIN_ID],
        };

      return options;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region all disbursed loan inlcued
  private getAllDisbursedInlcued(searchText) {
    let where;
    if (searchText)
      if (!isNaN(searchText)) {
        let encryptedData = this.cryptService.encryptPhone(searchText);
        if (encryptedData == k500Error) return k500Error;
        encryptedData = encryptedData.split('===')[1];
        where = { phone: { [Op.like]: '%' + encryptedData + '%' } };
      } else if (isNaN(searchText))
        where = { fullName: { [Op.iRegexp]: searchText } };

    //
    const subScrMode = {
      model: SubScriptionEntity,
      attributes: ['id', 'mode'],
    };
    //
    const predictionInclude = {
      model: PredictionEntity,
      attributes: ['categorizationTag'],
    };
    // Table join -> Banking entity
    const bankingInclude: SequelOptions = { model: BankingEntity };
    bankingInclude.attributes = [
      'adminSalary',
      'otherDetails',
      'salary',
      'salaryDate',
      'adminId',
    ];
    bankingInclude.required = false;

    //
    const adminModel = {
      model: admin,
      attributes: ['id', 'fullName'],
      as: 'adminData',
    };
    //
    const purposeModel = {
      model: loanPurpose,
      attributes: ['id', 'purposeName'],
    };
    /// user include
    const designationModel = {
      model: employmentDesignation,
      attributes: ['designationName', 'id'],
    };
    const empInclude = {
      model: employmentDetails,
      attributes: ['id', 'companyName', 'salary'],
      include: [designationModel],
    };
    const kycInclude = {
      model: KYCEntity,
      attributes: ['id', 'aadhaarDOB', 'aadhaarState'],
    };
    const cibilInclude = {
      model: CibilScoreEntity,
      attributes: ['cibilScore', 'id', 'plScore', 'overdueBalance'],
      required: false,
      order: [['id', 'DESC']],
    };
    const masterInclude: SequelOptions = { model: MasterEntity };
    masterInclude.attributes = ['assignedCSE', 'otherInfo'];
    const userModel = {
      model: registeredUsers,
      attributes: [
        'id',
        'fullName',
        'phone',
        'gender',
        'completedLoans',
        'city',
        'state',
        'kycId',
        'lastCrm',
      ],
      where,
      include: [empInclude, kycInclude, masterInclude],
    };

    const include = [
      adminModel,
      purposeModel,
      subScrMode,
      userModel,
      predictionInclude,
      bankingInclude,
      cibilInclude,
    ];
    return include;
  }
  //#endregion

  //#region pre pare all disbured Data
  private async prePareAllDisbursedData(findData: any, adminId) {
    const maskOptions = await this.commonServices.findMaskRole(adminId);
    /// Get insurance id(s) from data, when insurance id is found
    const ids = findData.rows
      .filter((item) => item.insuranceId !== null)
      .map((item) => item.insuranceId);

    /// Get insurance data based on collected loan id(s)
    let insuranceData = [];
    if (ids.length)
      insuranceData = await this.elephantService.funGetInsuranceData([
        ...new Set(ids),
      ]);
    const array = [];
    for (let index = 0; index < findData.rows.length; index++) {
      try {
        const element = findData.rows[index];
        const onlineFee = Math.round(+(element?.charges?.insurance_fee ?? 0));
        const riskAssessmentCharges = Math.round(
          +(element?.charges?.risk_assessment_charge ?? 0),
        );
        const documentCharges = Math.round(
          +(element?.charges?.doc_charge_amt ?? 0),
        );
        const gstAmount = Math.round(+(element?.charges?.gst_amt ?? 0));
        const insurancePremium = element?.insuranceId
          ? Math.round(+(element?.insuranceDetails?.totalPremium ?? 0))
          : 0;
        const collateralCharges = Math.round(
          +(element?.charges?.collateral_charge_amt ?? 0),
        );

        let policyExpiryDate = '-';
        if (element?.insuranceId) {
          const data = insuranceData.find(
            (item) => element.insuranceId === item.id,
          );

          if (data?.response) {
            const insurance = JSON.parse(data.response);
            const policyEndDate =
              insurance?.care?.policy_end_date ??
              insurance?.acko?.policy_end_date;
            policyExpiryDate = policyEndDate
              ? this.typeService.dateToJsonStr(policyEndDate)
              : '-';
          }
        }

        let riskCategory;
        if (element?.categoryTag || element?.categoryTag == 0) {
          riskCategory = str.userCategoryTag[element?.categoryTag];
        } else if (element?.predictionData?.categorizationTag)
          riskCategory = element?.predictionData?.categorizationTag?.slice(
            0,
            -5,
          );
        else '-';

        const temp: any = {};
        const user = element?.registeredUsers;
        const masterData = user?.masterData ?? {};
        const assignedCSE = masterData?.assignedCSE;
        const employment = user?.employmentData;
        const disbursed = element?.disbursementData[0];
        const emiData = element.emiData;
        const bankingData = element?.bankingData ?? {};
        const disbDate = new Date(element.loan_disbursement_date);
        const lastCrm = user?.lastCrm;
        const cibilData = element?.cibilData ?? {};
        let totalInterestAmount = 0;
        let totalExpectedAmount = 0;

        for (let index = 0; index < emiData.length; index++) {
          try {
            const emi = emiData[index];
            const principalAmount = emi?.principalCovered ?? 0;
            const interestAmount = emi?.interestCalculate ?? 0;
            totalInterestAmount += interestAmount;
            totalExpectedAmount += principalAmount + interestAmount;
          } catch (error) {}
        }
        totalExpectedAmount = Math.floor(totalExpectedAmount);
        totalInterestAmount = Math.floor(totalInterestAmount);

        const qualityParameters = element.qualityParameters ?? {};
        const qualityAdminId = qualityParameters.adminId;
        temp['userId'] = user?.id ?? '';
        temp['Loan ID'] = element?.id;
        temp['Name'] = user?.fullName ?? '-';
        const phone = this.cryptService.decryptPhone(user?.phone);
        temp['Phone'] = maskOptions.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;
        temp['Completed loans'] = element?.completedLoan ?? '0';
        temp['App platform'] =
          element?.appType == 1
            ? EnvConfig.nbfc.nbfcShortName
            : EnvConfig.nbfc.appName;
        temp['Employment information'] =
          masterData?.otherInfo?.employmentInfo ?? '';
        const annumInterest =
          element?.eligibilityDetails?.annumInterest ??
          element?.eligibilityDetails?.anummIntrest;
        const loanAnnualRate = +annumInterest;
        const annualInterestRate = loanAnnualRate
          ? loanAnnualRate.toFixed(2)
          : (+element?.interestRate * 365).toFixed(2);
        temp['Interest rate'] = `${annualInterestRate}%`;
        temp['Loan tenure (days)'] = +element?.approvedDuration;
        temp['Approved amount'] = +(+element?.netApprovedAmount).toFixed();
        temp['Disbursed amount'] = +((disbursed?.amount ?? 0) / 100).toFixed();
        temp['Collateral amount'] = collateralCharges;
        temp['Processing fees'] = this.typeService.manageAmount(
          (+element?.netApprovedAmount * element?.processingFees) / 100,
        );
        temp['Document charges'] = documentCharges;
        temp['Online convenience fees'] = onlineFee;
        temp['Risk assessment fees'] = riskAssessmentCharges;
        temp['Insurance premium'] = insurancePremium;
        temp['Stamp duty fees'] = +element?.stampFees;
        temp['GST amount'] = gstAmount;
        temp['Total expected amount'] = +totalExpectedAmount;
        temp['Total interest amount'] = +totalInterestAmount;
        temp['Total EMI'] = emiData.length;
        temp['Cibil score'] = cibilData?.cibilScore ?? '-';
        temp['Pl score'] = cibilData?.plScore ?? '-';
        temp['Company name'] = employment?.companyName ?? '';
        temp['Designation'] = employment?.designation?.designationName ?? '';
        temp['Gender'] = (user?.gender ?? 'MALE').toUpperCase();
        temp['State'] = user?.kycData?.aadhaarState ?? '-';
        temp['Account number'] = maskOptions.isDisbursementAccMask
          ? this.cryptService.dataMasking(
              'bankAccount',
              disbursed?.account_number,
            )
          : disbursed?.account_number;

        temp['Bank name'] = disbursed?.bank_name;
        temp['Ifsc  code'] = disbursed?.ifsc;
        temp['Mandate type'] = element?.subscriptionData?.mode ?? str.kSigndesk;
        temp['Disbursement date'] = this.typeService.getDateFormatted(disbDate);
        temp['Loan purpose'] =
          element?.purpose?.purposeName ?? 'Personal reason';
        temp['Bank approved by'] =
          (await this.commonServices.getAdminData(bankingData?.adminId))
            ?.fullName ?? '-';
        temp['Loan approved by'] = element?.adminData?.fullName ?? 'system';
        temp['Insurance'] = element?.insuranceId ? 'Yes' : 'No';
        temp['Insurance end date'] = policyExpiryDate;
        temp['Risk category'] = riskCategory;

        temp['Quality status'] = qualityAdminId ? 'Checked' : 'Not checked';
        temp['Quality admin'] = !qualityAdminId
          ? '-'
          : (await this.commonServices.getAdminData(qualityAdminId)).fullName;
        temp['Last crm by'] = lastCrm?.adminName ?? '-';
        temp['CRM'] = lastCrm?.statusName ?? '-';
        temp['Crm date'] = lastCrm?.createdAt
          ? this.typeService.getDateFormatted(lastCrm?.createdAt)
          : '-';
        temp['Remark'] = lastCrm?.remark ?? '-';
        temp['Disposition'] = lastCrm?.dispositionName ?? '-';

        temp['Salary date'] = bankingData?.salaryDate ?? '-';
        temp['Approved salary'] =
          bankingData?.salary ??
          bankingData?.adminSalary ??
          bankingData?.otherDetails?.salary?.average ??
          '-';
        temp['Assigned CSE'] =
          (await this.commonServices.getAdminData(assignedCSE))?.fullName ??
          '-';
        temp['Overdue amount'] = cibilData?.overdueBalance ?? '-';

        array.push(temp);
      } catch (error) {}
    }
    return array;
  }
  //#endregion

  //#region get Disbursed Total Data
  private async getDisbursedTotalData(query) {
    try {
      query.download = 'true';
      query.type = 'ALL';
      const options = this.prePareOptions(query);
      if (options.message) return options;
      const att = [
        'manualVerificationAcceptId',
        'completedLoan',
        [Sequelize.fn('COUNT', Sequelize.col('loanTransaction.id')), 'count'],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('netApprovedAmount'),
              'double precision',
            ),
          ),
          'amount',
        ],
      ];
      options.where.completedLoan = 0;
      options.where.loanStatus = { [Op.or]: ['Active', 'Complete'] };
      options.group = ['completedLoan', 'manualVerificationAcceptId'];
      delete options.order;
      const multiOptions: any = {
        ...options,
        where: { ...options.where, completedLoan: { [Op.gt]: 0 } },
      };
      let [firstLoans, multipleLoans]: any[] = [
        this.loanRepo.getTableWhereData(att, options),
        this.loanRepo.getTableWhereData(att, multiOptions),
      ];
      [firstLoans, multipleLoans] = await Promise.all([
        firstLoans,
        multipleLoans,
      ]);
      if (!firstLoans || firstLoans === k500Error) return kInternalError;
      if (!multipleLoans || multipleLoans === k500Error) return kInternalError;
      return { firstLoans, multipleLoans };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region pre pare disbursed Total Data
  private prePareDisbursedTotalData(totalData) {
    const firstLoans = totalData?.firstLoans ?? [];
    const multipleLoans = totalData?.multipleLoans ?? [];
    const data = {
      totalCount: 0,
      totalAmount: 0,
      systemTotalCount: 0,
      systemTotalAmount: 0,
      manualTotalCount: 0,
      manualTotalAmount: 0,
      newTotalCount: 0,
      newTotalAmount: 0,
      repeatedTotalCount: 0,
      repeatedTotalAmount: 0,
    };
    firstLoans.forEach((ele) => {
      try {
        const id = ele?.manualVerificationAcceptId ?? SYSTEM_ADMIN_ID;
        const amount = ele?.amount ?? 0;
        const count = +(ele?.count ?? 0);
        data.totalCount += count;
        data.totalAmount += amount;
        data.newTotalCount += count;
        data.newTotalAmount += amount;
        if (id === SYSTEM_ADMIN_ID || id === CIBIL_ADMIN_ID) {
          data.systemTotalCount += count;
          data.systemTotalAmount += amount;
        } else {
          data.manualTotalCount += count;
          data.manualTotalAmount += amount;
        }
      } catch (error) {}
    });

    multipleLoans.forEach((ele) => {
      try {
        const id = ele?.manualVerificationAcceptId ?? SYSTEM_ADMIN_ID;
        const amount = ele?.amount ?? 0;
        const count = +(ele?.count ?? 0);
        data.totalCount += count;
        data.totalAmount += amount;
        data.repeatedTotalCount += count;
        data.repeatedTotalAmount += amount;
        if (id === SYSTEM_ADMIN_ID || id === CIBIL_ADMIN_ID) {
          data.systemTotalCount += count;
          data.systemTotalAmount += amount;
        } else {
          data.manualTotalCount += count;
          data.manualTotalAmount += amount;
        }
      } catch (error) {}
    });
    return data;
  }

  // 5770
  //  #start region  getRepaidCardData
  async getRepaidCardData() {
    try {
      const att: any = [
        [Sequelize.fn('sum', Sequelize.col('paidAmount')), 'amount'],
        [
          Sequelize.literal(`
            (SUM("sgstForClosureCharge"+ 
            "cgstForClosureCharge"+ 
            "igstForClosureCharge"+ 
            "sgstOnBounceCharge"+ 
            "cgstOnBounceCharge"+ 
            "igstOnBounceCharge"+ 
            "sgstOnPenalCharge"+ 
            "cgstOnPenalCharge"+ 
            "igstOnPenalCharge"+ 
            "sgstOnLegalCharge"+ 
            "igstOnLegalCharge"+ 
            "cgstOnLegalCharge"))
          `),
          'totalGST',
        ],
      ];
      const where: any = {
        status: 'COMPLETED',
        type: { [Op.ne]: 'REFUND' },
      };
      const option = { useMaster: false, where };
      const totalAmount = await this.transactionRepo.getRowWhereData(
        att,
        option,
      );

      if (totalAmount === k500Error) return kInternalError;

      // Make common dates
      // CURRENT MONTH
      const cStartDate = new Date();
      cStartDate.setDate(1);
      cStartDate.setHours(0);
      const cEndDate = new Date();
      cEndDate.setFullYear(cEndDate.getFullYear(), cEndDate.getMonth() + 1, 0);
      cEndDate.setHours(23, 59);

      option.where.completionDate = {
        [Op.gte]: cStartDate.toJSON(),
        [Op.lte]: cEndDate.toJSON(),
      };
      const cAmount = await this.transactionRepo.getRowWhereData(att, option);
      if (cAmount === k500Error) return kInternalError;

      // LAST MONTH
      const lStartDate = new Date();
      lStartDate.setDate(1);
      lStartDate.setMonth(lStartDate.getMonth() - 1);
      lStartDate.setHours(0);
      const lEndDate = new Date();
      lEndDate.setDate(15);
      // lEndDate.setMonth(lEndDate.getMonth() - 1);
      lEndDate.setDate(0);
      lEndDate.setHours(23, 59);

      option.where.completionDate = {
        [Op.gte]: lStartDate.toJSON(),
        [Op.lte]: lEndDate.toJSON(),
      };
      const lAmount = await this.transactionRepo.getRowWhereData(att, option);
      if (lAmount === k500Error) return kInternalError;

      const finalTotalRepay = Math.round(totalAmount?.amount) ?? 0;
      const cmTotalRepay = Math.round(cAmount?.amount) ?? 0;
      const lmTotalRepay = Math.round(lAmount?.amount) ?? 0;
      const GST = Math.round(totalAmount.totalGST) ?? 0;
      if (isNaN(GST)) return kInternalError;
      const repaidAmount = Math.round(finalTotalRepay - GST);
      const paidCreditAmount = await this.getPaidCreditAmount();

      return {
        finalTotalRepay,
        cmTotalRepay,
        lmTotalRepay,
        GST,
        repaidAmount,
        paidCreditAmount,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion

  tempDate;
  tempCount;

  //#region get chat count
  async getChatCount() {
    const chat_limit = 999;
    if (this.tempDate) {
      const cTime = new Date().getTime();
      const diff = cTime - this.tempDate;
      if (diff < 60000) {
        if (this.tempCount >= chat_limit) return `${chat_limit}+`;
        else return this.tempCount.toString();
      }
    }

    const key = (gIsPROD ? 'PROD' : 'UAT') + '-Recent Chats';

    const docData =
      EnvConfig.nbfcType == '1'
        ? await firebaseDB
            .collection(key)
            .where('count', '>', 0)
            .where('nbfcType', '==', 1)
            .limit(chat_limit)
            .get()
        : await firebaseDB
            .collection(key)
            .where('count', '>', 0)
            .where('nbfcType', '==', 0)
            .limit(chat_limit)
            .get();
    const querySnapshots: any = docData.docs;
    this.tempCount = querySnapshots.length;
    this.tempDate = new Date().getTime();
    if (this.tempCount >= chat_limit) return `${chat_limit}+`;
    else return this.tempCount.toString();
  }

  // #startregion get payment data
  async funGetPaymentData() {
    try {
      let sDate = this.typeService.getGlobalDate(new Date()).toJSON();
      let eDate = this.typeService.getGlobalDate(new Date()).toJSON();

      const options: any = {
        where: {
          completionDate: { [Op.gte]: sDate, [Op.lte]: eDate },
          status: 'COMPLETED',
          type: { [Op.ne]: 'REFUND' },
          subStatus: {
            [Op.or]: [
              {
                [Op.ne]: 'REVERSE_SETTLEMENT',
              },
              { [Op.eq]: null },
            ],
          },
        },
      };
      const attributes = [
        'id',
        'completionDate',
        'status',
        'source',
        'paidAmount',
        'principalAmount',
        'interestAmount',
      ];
      const paymentData =
        await this.transactionRepo.getTableWhereDataWithCounts(
          attributes,
          options,
        );

      if (paymentData === k500Error) return kInternalError;
      options.include = [
        { model: registeredUsers, where: { typeOfDevice: '1' } },
      ];
      let iosDeviceCounts = await this.transactionRepo.getCountsWhere(options);
      if (iosDeviceCounts === k500Error) iosDeviceCounts = 0;
      let totalPaymentAmount = 0;
      let totalprincipalAmount = 0;
      let totalInterestAmount = 0;
      paymentData.rows.forEach((ele) => {
        totalPaymentAmount += +ele?.paidAmount;
        totalprincipalAmount += +ele?.principalAmount;
        totalInterestAmount += +ele?.interestAmount;
      });
      totalPaymentAmount = Math.floor(totalPaymentAmount);
      totalprincipalAmount = Math.floor(totalprincipalAmount);
      totalInterestAmount = Math.floor(totalInterestAmount);
      return {
        totalPaymentAmount,
        iosDeviceCounts,
        count: paymentData?.count,
        totalprincipalAmount,
        totalInterestAmount,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // #start region
  private getDates() {
    const date = new Date().getDate();
    const month = new Date().getMonth();
    const month1 = new Date().getMonth() + 1;
    const year = new Date().getFullYear();
    const lastDate =
      month1 === 2
        ? year & 3 || (!(year % 25) && year & 15)
          ? 28
          : 29
        : 30 + ((month1 + (month1 >> 3)) & 1);
    let startDate: Date;
    let endDate: Date;
    try {
      if (date > 15) {
        startDate = this.typeService.getGlobalDate(new Date(year, month, date));
        endDate = this.typeService.getGlobalDate(
          new Date(year, month, lastDate),
        );
        endDate.setDate(endDate.getDate() + 15);
      } else {
        startDate = new Date(year, month, date);
        endDate = new Date(year, month, lastDate);
      }
    } catch (error) {}
    endDate.setDate(endDate.getDate() + 1);
    return { startDate, endDate };
  }
  //#endregion

  // get 15 Days Count And Amount Emi Data
  async get15DaysCountAndAmountEmiData() {
    try {
      let startDate;
      let endDate;
      const tempData = this.getDates();
      startDate = this.typeService.getGlobalDate(tempData?.startDate);
      endDate = this.typeService.getGlobalDate(tempData?.endDate);
      endDate.setDate(endDate.getDate() - 1);
      const emiOption = {
        where: {
          emi_date: {
            [Op.gte]: startDate.toJSON(),
            [Op.lte]: endDate.toJSON(),
          },
          payment_status: '0',
        },
        group: ['loanId'],
      };
      const emiList: any = await this.emiRepo.getTableWhereData(
        ['loanId'],
        emiOption,
      );
      if (emiList === k500Error) return kInternalError;
      const loanList = emiList.map((el) => el?.loanId);
      const loanAttr = ['id', 'loanStatus', 'cibilBatchCategory'];
      const loanOptions = {
        where: { id: loanList },
        include: [
          {
            model: EmiEntity,
            attributes: [
              'id',
              'emi_date',
              'emi_amount',
              'loanId',
              'payment_done_date',
              'payment_status',
              'payment_due_status',
            ],
            where: {
              payment_status: '0',
            },
          },
        ],
      };
      const upcomingEMIData = await this.loanRepo.getTableWhereData(
        loanAttr,
        loanOptions,
      );

      if (upcomingEMIData === k500Error) return kInternalError;

      let upcomingEMI = [];
      let defaulterEMI = [];
      upcomingEMIData.forEach((element) => {
        try {
          const dueStatus = element?.emiData.map(
            (el) => el?.payment_due_status,
          );
          if (dueStatus.includes('1')) defaulterEMI.push(element);
          upcomingEMI.push(element);
        } catch (error) {}
      });
      let first15DayAmount = 0;
      let last15DayAmount = 0;
      let first15DayCount = 0;
      let last15DayCount = 0;
      let totalLast15DayHighRiskUserCount = 0;
      let totalFirst15DayHighRiskUserCount = 0;
      let defaultEMIAmount = 0;
      let defaultEMICount = 0;
      let totalDefaultHighRiskUserCount = 0;
      upcomingEMI.forEach((element) => {
        try {
          const emiData = element?.emiData;
          let last15DayHighRiskUserCount = 0;
          let first15DayHighRiskUserCount = 0;
          emiData.forEach((ele) => {
            try {
              if (
                ele?.payment_status == '0' &&
                ele?.payment_due_status == '0'
              ) {
                const emiDate = new Date(ele.emi_date);
                if (
                  emiDate.getTime() >= startDate.getTime() &&
                  emiDate.getTime() <= endDate.getTime()
                ) {
                  const day = emiDate.getDate();
                  element.newEmiData = ele;
                  if (day > 15) {
                    last15DayAmount += +ele?.emi_amount;
                    last15DayCount += 1;
                    if (element?.cibilBatchCategory == 2)
                      last15DayHighRiskUserCount++;
                  } else {
                    first15DayAmount += +ele?.emi_amount;
                    first15DayCount += 1;
                    if (element?.cibilBatchCategory == 2)
                      first15DayHighRiskUserCount++;
                  }
                }
              }
            } catch (error) {}
          });
          totalLast15DayHighRiskUserCount += last15DayHighRiskUserCount;
          totalFirst15DayHighRiskUserCount += first15DayHighRiskUserCount;
        } catch (error) {}
      });

      defaulterEMI.forEach((element) => {
        try {
          const emiData = element?.emiData;
          emiData.forEach((ele) => {
            try {
              if (
                ele?.payment_status == '0' &&
                ele?.payment_due_status == '0'
              ) {
                const emiDate = new Date(ele.emi_date);
                if (emiDate >= startDate && emiDate <= endDate) {
                  element.newEmiData = ele;
                  defaultEMIAmount += +ele?.emi_amount;
                  defaultEMICount += 1;
                  delete element?.emiData;
                }
              }
            } catch (error) {}
          });
          if (element?.cibilBatchCategory == 2) totalDefaultHighRiskUserCount++;
        } catch (error) {}
      });

      let result: any = {
        startDate,
        endDate,
        first15DayAmount,
        last15DayAmount,
        first15DayCount,
        last15DayCount,
        defaultEMIAmount,
        defaultEMICount,
        totalDefaultHighRiskUserCount,
        totalFirst15DayHighRiskUserCount,
        totalLast15DayHighRiskUserCount,
      };

      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get Active Part Payment Details
  async getActivePartPaymentDetails(query) {
    try {
      const adminId = query?.adminId ?? -1;
      const searchText = query?.searchText ?? '';

      const where: any = { loanStatus: 'Active' };
      if (adminId != -1) where.followerId = adminId;
      const include = [];
      const order = [['id', 'desc']];
      const traInclude = {
        attributes: ['id'],
        model: TransactionEntity,
        where: { status: 'COMPLETED', type: 'PARTPAY' },
        include: [
          {
            attributes: ['id'],
            model: EmiEntity,
            where: {
              payment_status: '0',
              payment_due_status: '1',
              penalty_days: { [Op.gte]: 1 },
            },
          },
        ],
        order,
      };
      include.push(traInclude);

      /// emi
      const attributes = [
        'id',
        'principalCovered',
        'interestCalculate',
        'penalty',
        'partPaymentPenaltyAmount',
        'penalty_days',
        'pay_type',
        'fullPayPenalty',
        'emi_amount',
        'payment_status',
        'payment_due_status',
      ];
      const emiInclude = {
        attributes,
        model: EmiEntity,
        include: [
          {
            attributes: [
              'id',
              'paidAmount',
              'principalAmount',
              'interestAmount',
              'penaltyAmount',
            ],
            model: TransactionEntity,
            required: false,
            where: { status: 'COMPLETED' },
          },
        ],
      };
      include.push(emiInclude);
      /// admin
      /// user
      const userWhere: any = {};
      if (searchText) userWhere.fullName = { [Op.iRegexp]: searchText };
      const userInclude = {
        attributes: ['id', 'fullName', 'phone', 'email'],
        model: registeredUsers,
        where: userWhere,
      };

      include.push(userInclude);
      const options = { where, include };
      const att = ['id', 'followerId', 'manualVerificationAcceptId'];
      const result = await this.loanRepo.getTableWhereData(att, options);
      if (result === k500Error) return kInternalError;
      if (result.length == 0) return result;
      const finalData = await this.prePareActivePartPayData(result);
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prePare active part pay data
  private async prePareActivePartPayData(result) {
    try {
      const finalData = [];
      for (let index = 0; index < result.length; index++) {
        const e = result[index];
        const adminfollowerData =
          (await this.commonServices.getAdminData(e?.followerId))?.fullName ??
          '-';
        e.followerData = adminfollowerData;
        const adminData = await this.commonServices.getAdminData(
          e?.manualVerificationAcceptId,
        );
        e.adminData = { fullName: adminData?.fullName ?? '-' };

        try {
          const temp = {
            'Loan ID': e.id,
            Name: e?.registeredUsers?.fullName,
            'Mobile number': this.cryptService.decryptPhone(
              e?.registeredUsers?.phone,
            ),
            Email: e?.registeredUsers?.email,
            userId: e?.registeredUsers?.id,
            'Penalty days': 0,
            'Paid amount': 0,
            'Total unpaid emi amount': 0,
            'Unpaid emi amount': 0,
            'Unpaid penalty amount': 0,
            'Loan approved by': e?.adminData?.fullName,
            'Follower name': e?.followerData,
          };

          const emiData = e?.emiData;
          const remainData = {
            emiAmount: 0,
            paidPenaltyAmount: 0,
            fullPayPenalty: 0,
            penalty: 0,
          };
          for (let index = 0; index < emiData.length; index++) {
            const element = emiData[index];
            try {
              const status = element?.payment_status;
              if (status === '0' && element?.payment_due_status == '1') {
                temp['Penalty days'] += element?.penalty_days ?? 0;
                remainData.emiAmount +=
                  status === '1' ? 0 : +(element?.emi_amount ?? 0);
                remainData.penalty += +element?.penalty;
              }
              const transaction = element?.transactionData;
              transaction.forEach((res) => {
                try {
                  temp['Paid amount'] += +(res?.paidAmount ?? 0);
                } catch (error) {}
              });
            } catch (error) {}
          }
          temp['Total unpaid emi amount'] =
            remainData?.emiAmount + remainData?.penalty;
          temp['Unpaid emi amount'] = remainData?.emiAmount;
          temp['Unpaid penalty amount'] = remainData?.penalty;
          finalData.push(temp);
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // #startregion get all chat documents user userId
  async fetchAllChatDocumentByUserId(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const attr = [
        'id',
        'userId',
        'docUrl',
        'docType',
        'type',
        'password',
        'isDeleted',
        'adminId',
        'createdAt',
        'loanId',
      ];
      const obj: any = { [Op.or]: [{ [Op.eq]: false }, { [Op.eq]: null }] };
      const options = {
        where: {
          userId: userId,
          isDeleted: obj,
          docType: {
            [Op.notIn]: kLegalTabUploadDocList,
          },
        },
        order: [['id', 'DESC']],
      };
      const data: any = await this.mediaRepo.getTableWhereData(attr, options);
      if (data === k500Error) return kInternalError;
      let checkIfAddressProf = false;
      for (let i = 0; i < data.length; i++) {
        const ele = data[i];
        try {
          const adminId = ele?.adminId;
          let adminData = await this.commonServices.getAdminData(adminId);
          ele.adminData =
            adminData?.id === null
              ? { fullName: 'User' }
              : { id: adminData?.id, fullName: adminData?.fullName };

          if (ele.docType === 'addressProof') {
            checkIfAddressProf = true;
          }
        } catch (error) {}
      }

      if (!checkIfAddressProf) {
        const userData = await this.userRepo.getRowWhereData(
          ['id', 'homeProofImage', 'createdAt', 'residenceAdminId'],
          { where: { id: userId } },
        );
        if (userData === k500Error) return kInternalError;
        if (!userData) return k422ErrorMessage(str.kNoDataFound);
        if (userData?.homeProofImage) {
          const type = userData?.homeProofImage?.split('.').reverse()[0];
          data.push({
            userId,
            docUrl: userData.homeProofImage,
            type,
            docType: 'addressProof',
            createdAt: userData.createdAt,
            adminData: userData?.residenceAdminData ?? {
              fullName: 'User',
            },
          });
        }
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // #start region get all device by userID
  async getAllDeviceByUserId(query) {
    // Validation -> Parameter
    const userId = query?.userId;
    if (!userId) return kParamMissing('userId');
    // Hit -> Query
    const user = await this.userRepo.getRowWhereData(['id', 'recentDeviceId'], {
      where: { id: userId },
    });
    if (user === k500Error) throw new Error();
    if (!user) return k422ErrorMessage(str.kNoDataFound);
    // Preparation -> Query
    const attributes = ['id', 'deviceId', 'createdAt', 'updatedAt'];
    // Hit -> Query
    const deviceData = await this.deviceRepo.getTableWhereData(attributes, {
      where: { userId },
      order: [['id', 'DESC']],
    });
    // Validation -> Query data
    if (deviceData === k500Error) throw new Error();

    const idList = [...new Set(deviceData.map((item) => item?.deviceId))];
    const attr = ['id', 'deviceId', 'deviceInfo', 'webDeviceInfo'];
    const opts = { where: { deviceId: idList } };
    const deviceDetails = await this.deviceAppInfoRepo.getTableWhereData(
      attr,
      opts,
    );
    // Validation -> Query data
    if (deviceDetails === k500Error) throw new Error();

    const uniqueDevice = [];
    for (let i = 0; i < deviceData.length; i++) {
      try {
        const ele = deviceData[i];
        const deviceId = ele?.deviceId;
        const find = uniqueDevice.find((fd) => fd?.deviceId == deviceId);
        if (find) continue;

        const obj: any = { deviceId };
        obj.status = deviceId == user?.recentDeviceId ? 'Active' : 'Inactive';
        const details = deviceDetails.find((f) => f?.deviceId == deviceId);
        const deviceInfo = details?.deviceInfo
          ? JSON.parse(details?.deviceInfo)
          : {};
        obj.Type = deviceInfo?.brand
          ? 'Android'
          : deviceInfo?.localizedModel
          ? 'IOS'
          : '-';

        obj.brand = deviceInfo?.brand ?? deviceInfo?.name ?? '-';
        obj.deviceName = deviceInfo?.model ?? '-';
        obj.OSVersion =
          deviceInfo?.release_version ?? deviceInfo?.systemVersion ?? '-';

        // Web device info
        if (details.webDeviceInfo) {
          try {
            const webInfo = JSON.parse(details.webDeviceInfo);
            if (webInfo.user_agent) {
              const browserDetails = this.getBrowserDetails(webInfo.user_agent);
              obj.brand = browserDetails?.os_family;
              obj.Type = 'WEB';
              obj.deviceName = browserDetails?.browser_family;
            }
          } catch (error) {}
        }

        const createDate = this.dateService.dateToReadableFormat(
          ele?.createdAt,
        );
        const updateDate = this.dateService.dateToReadableFormat(
          ele?.updatedAt,
        );
        obj.createdAt = `${createDate.readableStr} ${createDate.hours}:${createDate.minutes} ${createDate.meridiem}`;
        obj.updatedAt = `${updateDate.readableStr} ${updateDate.hours}:${updateDate.minutes} ${updateDate.meridiem}`;
        uniqueDevice.push(obj);
      } catch (error) {}
    }
    uniqueDevice.sort((a, b) => a.status.localeCompare(b.status));
    return uniqueDevice;
  }

  private getBrowserDetails(userAgentString) {
    var ua = userAgentString || navigator.userAgent;
    var browserDetails: any = {};

    var match =
      ua.match(
        /(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i,
      ) || [];
    if (/trident/i.test(match[1])) {
      var tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
      browserDetails.browser_family = 'IE';
      browserDetails.browser_version = tem[1] || '';
    }
    if (match[1] === 'Chrome') {
      let tem = ua.match(/\b(OPR|Edge)\/(\d+)/);
      if (tem != null) {
        browserDetails.browser_family = tem[1].replace('OPR', 'Opera');
        browserDetails.browser_version = tem[2];
      }
    }
    match = match[2]
      ? [match[1], match[2]]
      : [navigator.appName, navigator.appVersion, '-?'];

    if (browserDetails.browser_family === undefined) {
      browserDetails.browser_family = match[0];
      browserDetails.browser_version = match[1];
    }

    var OSName = 'Unknown OS';
    if (ua.indexOf('Win') != -1) OSName = 'Windows';
    if (ua.indexOf('Mac') != -1) OSName = 'Mac';
    if (ua.indexOf('Linux') != -1) OSName = 'Linux';
    if (ua.indexOf('iPhone') != -1) OSName = 'iPhone';
    if (ua.indexOf('iPad') != -1) OSName = 'iPad';
    if (ua.indexOf('Android') != -1) OSName = 'Android';

    browserDetails.os_family = OSName;

    return browserDetails;
  }

  // #start get net banking data
  async getLoanDataForNetBanking(query) {
    try {
      const loanId = query?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const bankInc = {
        model: BankingEntity,
        attributes: [
          'accountNumber',
          'name',
          'bank',
          'adminSalary',
          'salary',
          'additionalAccountNumber',
          'additionalBank',
          'accountDetails',
          'additionalAccountDetails',
        ],
      };
      const loanAttr = [
        'id',
        'userId',
        'loanStatus',
        'approvedDuration',
        'fullName',
      ];
      const loanOptions = {
        where: { id: loanId },
        include: [bankInc],
        useMaster: false,
      };
      const loanData = await this.repoManager.getRowWhereData(
        loanTransaction,
        loanAttr,
        loanOptions,
      );
      if (loanData === k500Error) throw new Error();

      const emplyattr = ['companyName', 'salary'];
      const emplyOptions = {
        where: { userId: loanData.userId },
        useMaster: false,
      };
      const employmentData = await this.repoManager.getRowWhereData(
        employmentDetails,
        emplyattr,
        emplyOptions,
      );
      if (employmentData === k500Error) throw new Error();
      const bankingData = loanData?.bankingData;
      if (!bankingData) return k422ErrorMessage(str.kNoDataFound);

      if (bankingData?.accountDetails) {
        const accountDetails = JSON.parse(bankingData?.accountDetails);
        bankingData.accountDetails = accountDetails;
      }
      if (bankingData?.additionalAccountDetails) {
        const additionalAccountDetails = JSON.parse(
          bankingData?.additionalAccountDetails,
        );
        bankingData.additionalAccountDetails = additionalAccountDetails;
      }

      return {
        loanId: loanData.id,
        userId: loanData.userId,
        fullName: loanData.fullName,
        accountDetails: bankingData.accountDetails,
        additionalAccountDetails: bankingData.additionalAccountDetails,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  // Loan History Counts
  async getLoanTransactionService(userId) {
    try {
      const loanHistoryData = {
        totalAmount: 0,
        totalCount: 0,
        onTimeAmount: 0,
        onTimeCount: 0,
        delayAmount: 0,
        delayCount: 0,
        inProgressAmount: 0,
        inProgressCount: 0,
        defaulterAmount: 0,
        defaulterCount: 0,
      };
      const loanAttr = [
        'id',
        'userId',
        'loanStatus',
        'loanAmount',
        'manualVerificationAcceptId',
      ];
      const include = [
        {
          model: EmiEntity,
          as: 'emiData',
          attributes: [
            'id',
            'userId',
            'loanId',
            'emi_date',
            'emi_amount',
            'payment_status',
            'payment_due_status',
            'penalty',
          ],
          order: [['emi_date', 'ASC']],
        },
      ];
      const options = {
        useMaster: false,
        where: { userId },
        order: [['id', 'DESC']],
        include,
      };
      const loanData = await this.loanRepo.getTableWhereData(loanAttr, options);
      if (loanData === k500Error) return kInternalError;
      for (let i = 0; i < loanData.length; i++) {
        try {
          const data = { ...loanData[i] };

          loanHistoryData.totalCount += 1;
          if (data.loanStatus == 'Active' || data.loanStatus == 'Complete') {
            let onTimeIsCounted = false;
            let delayIsCounted = false;
            let inProgressIsCounted = false;
            let defaulterIsCounted = false;
            let defaulterEmisCounts = 0;
            for (let i = 0; i < data.emiData.length; i++) {
              try {
                const currentEmi = { ...data.emiData[i] };
                loanHistoryData.totalAmount += parseFloat(
                  currentEmi.emi_amount,
                );
                // emi paid without any penalty
                if (
                  currentEmi.payment_status == '1' &&
                  currentEmi.payment_due_status == '0'
                ) {
                  loanHistoryData.onTimeAmount += parseFloat(
                    currentEmi.emi_amount,
                  );
                  if (!onTimeIsCounted) loanHistoryData.onTimeCount += 1;
                  onTimeIsCounted = true;
                }
                // emi paid with penalty
                if (
                  currentEmi.payment_status == '1' &&
                  currentEmi.payment_due_status == '1'
                ) {
                  loanHistoryData.delayAmount +=
                    parseFloat(currentEmi.emi_amount) + currentEmi.penalty;
                  if (!delayIsCounted) loanHistoryData.delayCount += 1;
                  delayIsCounted = true;
                }
                // emi unpaid but no penalty
                if (
                  currentEmi.payment_status == '0' &&
                  currentEmi.payment_due_status == '0'
                ) {
                  loanHistoryData.inProgressAmount += parseFloat(
                    currentEmi.emi_amount,
                  );
                  if (!inProgressIsCounted)
                    loanHistoryData.inProgressCount += 1;
                  inProgressIsCounted = true;
                }
                // emi unpaid but penalty
                if (
                  currentEmi.payment_status == '0' &&
                  currentEmi.payment_due_status == '1'
                ) {
                  defaulterEmisCounts++;
                  if (defaulterEmisCounts === data.emiData.length) {
                    // Full pay amount
                    const fullPayData =
                      await this.sharedCalculation.getFullPaymentData({
                        loanId: data.id,
                      });
                    if (fullPayData?.message) return fullPayData;
                    loanHistoryData.defaulterAmount = fullPayData.totalAmount;
                  } else {
                    loanHistoryData.defaulterAmount +=
                      parseFloat(currentEmi.emi_amount) + currentEmi.penalty;
                  }
                  if (!defaulterIsCounted) loanHistoryData.defaulterCount += 1;
                  defaulterIsCounted = true;
                }
              } catch (error) {}
            }
          }
        } catch (error) {}
      }
      const result = {
        total: loanHistoryData.totalAmount.toFixed(2),
        totalCount: loanHistoryData.totalCount,
        onTime: loanHistoryData.onTimeAmount.toFixed(2),
        onTimeCount: loanHistoryData.onTimeCount,
        delay: loanHistoryData.delayAmount.toFixed(2),
        delayCount: loanHistoryData.delayCount,
        inProgress: loanHistoryData.inProgressAmount.toFixed(2),
        inProgressCount: loanHistoryData.inProgressCount,
        defaulter: loanHistoryData.defaulterAmount.toFixed(2),
        defaulterCount: loanHistoryData.defaulterCount,
      };
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getDashboardData() {
    const listResult = await Promise.all([
      // #01 -> Users insights
      this.registeredUsersInsights(),
      // #02 -> AUM insights
      this.aumDetails(),
      // #03 -> Disbursed insights
      this.disbursedLoanInsights(),
    ]);

    return { ...listResult[0], ...listResult[1], ...listResult[2] };
  }

  // #01 -> Users insights
  private async registeredUsersInsights() {
    // Preparation ->  Last month's insights
    let date = new Date();
    let lastMonthY = date.getFullYear();
    let lastMonthM = date.getMonth() - 1;
    let lastMonthD = new Date(lastMonthY, lastMonthM, 1);
    let lastMonthD1 = new Date(lastMonthY, lastMonthM + 1, 0);
    const range = this.typeService.getUTCDateRange(
      lastMonthD.toString(),
      lastMonthD1.toString(),
    );
    const lastMonthOptions = {
      where: {
        createdAt: { [Op.gte]: range.fromDate, [Op.lte]: range.endDate },
      },
    };
    const lastMonthIOSOptions = {
      where: {
        createdAt: { [Op.gte]: range.fromDate, [Op.lte]: range.endDate },
        typeOfDevice: '1',
      },
    };

    // Current month's insights
    let y = date.getFullYear();
    let m = date.getMonth();
    let currMonthD = new Date(y, m, 1);
    currMonthD.setDate(currMonthD.getDate());
    let currMonthD1 = new Date();
    const currRange = this.typeService.getUTCDateRange(
      currMonthD.toString(),
      currMonthD1.toString(),
    );
    const currentMonthOptions = {
      where: {
        createdAt: {
          [Op.gte]: currRange.fromDate,
          [Op.lte]: currRange.endDate,
        },
      },
    };
    const currentMonthIOSOptions = {
      where: {
        createdAt: {
          [Op.gte]: currMonthD.toJSON(),
          [Op.lte]: currMonthD1.toJSON(),
        },
        typeOfDevice: '1',
      },
    };

    //for today insights
    const todayStart = this.typeService.getUTCDate(new Date().toJSON());

    const todayOptions = { where: { createdAt: { [Op.gte]: todayStart } } };
    const todayIOSOptions = {
      where: { createdAt: { [Op.gte]: todayStart }, typeOfDevice: '1' },
    };

    const uniqueLoanUserAttr = [
      [
        Sequelize.literal(`COUNT(DISTINCT "loanTransaction"."userId")`),
        'uniqueUsersCount',
      ],
    ];
    const uniqueLoanUserOptions = {
      where: {
        loanStatus: { [Op.in]: ['Active', 'Complete'] },
      },
    };

    const listResult = await Promise.all([
      // All
      this.userRepo.getCountsWhere({}),
      // Last month
      this.userRepo.getCountsWhere(lastMonthOptions),
      this.userRepo.getCountsWhere(lastMonthIOSOptions),
      // Current month
      this.userRepo.getCountsWhere(currentMonthOptions),
      this.userRepo.getCountsWhere(currentMonthIOSOptions),
      //today
      this.userRepo.getCountsWhere(todayOptions),
      this.userRepo.getCountsWhere(todayIOSOptions),
      // Unique loan users
      this.loanRepo.getRowWhereData(uniqueLoanUserAttr, uniqueLoanUserOptions),
    ]);

    return {
      totalUsers: listResult[0],
      totalUniqueUsers: +listResult[7].uniqueUsersCount,
      lastMonthUsers: listResult[1],
      lastMonthIOSUsers: listResult[2],
      lastMonthAndroidUsers: listResult[1] - listResult[2],
      currentMonthUsers: listResult[3],
      currentMonthIOSUsers: listResult[4],
      currentMonthAndroidUsers: listResult[3] - listResult[4],
      todayUsers: listResult[5],
      todayIOSUsers: listResult[6],
      todayAndroidUsers: listResult[5] - listResult[6],
    };
  }

  // #02 -> Recovered insights
  private async recoveredAmountInsights() {
    const loanIds = await this.getLoanIds();
    const listResult: any = await Promise.all([
      this.getPartPrincipalAmount(loanIds),
      this.getReceivedPrincipal(),
      this.getUpcommingPrincipal(),
      this.getPrincipal180delayDays(loanIds),
      this.getInterest90delayDays(),
    ]);

    const defaulterPrincipal: any = await this.defaultedPrincipal(
      listResult[0]?.partPaidPriAmtOfless180Days ?? 0,
      loanIds,
    );
    const upcommingPrincipal = Math.round(listResult[2]?.principalAmt ?? 0);
    const upcommingInterest = Math.round(listResult[2]?.interestAmt ?? 0);
    const grossPrincipal = Math.round(
      upcommingPrincipal + upcommingInterest + defaulterPrincipal,
    );
    const writeOfprincipal = Math.round(
      (listResult[3]?.remaningPrincipal ?? 0) -
        (listResult[0]?.partPayPriAmtOfAbove180Days ?? 0),
    );

    const remeaningIntOf1To90days = Math.round(
      listResult[4]?.remaningInterest ?? 0,
    );
    const netprincipal = Math.round(
      upcommingPrincipal + defaulterPrincipal + remeaningIntOf1To90days,
    );
    return {
      receivedPrincipal: Math.round(
        listResult[0]?.partPaidPrincipalAmount + listResult[1],
      ),
      upcommingPrincipal,
      upcommingInterest,
      grossPrincipal,
      writeOfprincipal,
      netprincipal,
      defaulterPrincipal,
      remeaningIntOf1To90days,
    };
  }

  // #03 -> Disbursed insights
  private async disbursedLoanInsights() {
    // Last month's insights
    const date = this.typeService.getGlobalDate(new Date());
    const y = date.getFullYear();
    const m = date.getMonth();
    const lastMonthD = this.typeService.getGlobalDate(new Date(y, m - 1, 1));
    const lastMonthD1 = this.typeService.getGlobalDate(new Date(y, m, 0));

    // Current month's insights
    const curreDate = this.typeService.getGlobalDate(new Date());
    const currY = curreDate.getFullYear();
    const currM = curreDate.getMonth();
    const currD = this.typeService.getGlobalDate(new Date(currY, currM, 1));
    const currD1 = this.typeService.getGlobalDate(new Date());

    const listResult = await Promise.all([
      // Last month's insights
      this.getDisbursedUsersCountWhere(lastMonthD, lastMonthD1),
      this.getDisbursedUsersCountWhere(
        lastMonthD,
        lastMonthD1,
        'NEWUSERSCOUNT',
      ),
      this.getDisbursedUsersCountWhere(
        lastMonthD,
        lastMonthD1,
        'IOSDEVICECOUNT',
      ),
      // Current month's insights
      this.getDisbursedUsersCountWhere(currD, currD1),
      this.getDisbursedUsersCountWhere(currD, currD1, 'NEWUSERSCOUNT'),
      this.getDisbursedUsersCountWhere(currD, currD1, 'IOSDEVICECOUNT'),
      // Today's insights
      this.getTodaysDisbursmentCountsWithAmount(),
      // Total insights
      this.loanRepo.getRowWhereData(
        [
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
          [
            Sequelize.fn(
              'SUM',
              Sequelize.cast(
                Sequelize.col('netApprovedAmount'),
                'double precision',
              ),
            ),
            'totalApprovedAmount',
          ],
        ],
        {
          where: { loanStatus: { [Op.in]: ['Active', 'Complete'] } },
        },
      ),
    ]);

    const {
      averageLoanTenure: lastMonthAverageLoanTenure,
      averageAPR: lastMonthAverageAPR,
    } = this.getAverageAPRAndAverageLoanTenure(listResult[0].rows);
    const {
      averageLoanTenure: currentMonthAverageLoanTenure,
      averageAPR: currentMonthAverageAPR,
    } = this.getAverageAPRAndAverageLoanTenure(listResult[3].rows);
    return {
      lastMonthData: {
        totalCount: +listResult[0].disbursedCount,
        totalAmount: +listResult[0].amount,
        newUsersCount: +listResult[1].disbursedCount,
        newUsersAmount: +listResult[1].amount,
        iosUsersCount: +listResult[2].disbursedCount,
        iosUsersAmount: +listResult[2].amount,
        androidUsersCount:
          +listResult[0].disbursedCount - +listResult[2].disbursedCount,
        androidUsersAmount: +listResult[0].amount - +listResult[2].amount,
        averageAPR: lastMonthAverageAPR.toFixed(2) + '%',
        averageLoanTenureDays: Math.floor(lastMonthAverageLoanTenure) + 'D',
      },
      currentMonthData: {
        totalCount: +listResult[3].disbursedCount,
        totalAmount: +listResult[3].amount,
        newUsersCount: +listResult[4].disbursedCount,
        newUsersAmount: +listResult[4].amount,
        iosUsersCount: +listResult[5].disbursedCount,
        iosUsersAmount: +listResult[5].amount,
        androidUsersCount:
          +listResult[3].disbursedCount - +listResult[5].disbursedCount,
        androidUsersAmount: +listResult[3].amount - +listResult[5].amount,
        averageAPR: currentMonthAverageAPR.toFixed(2) + '%',
        averageLoanTenureDays: Math.floor(currentMonthAverageLoanTenure) + 'D',
      },
      todayData: listResult[6],
      totalDisbursedCount: +listResult[7].count,
      totalApprovedAmount: Math.round(+listResult[7].totalApprovedAmount),
    };
  }

  private async getDisbursedUsersCountWhere(d, d1, type?) {
    const rawOptions: any = {
      include: [],
      where: {
        loan_disbursement: '1',
        loan_disbursement_date: {
          [Op.gte]: d.toJSON(),
          [Op.lte]: d1.toJSON(),
        },
      },
    };

    //  get counts of new users with daterange
    if (type == 'NEWUSERSCOUNT') {
      const tempLoan: any = await this.loanRepo.getTableWhereData(
        ['id', 'userId', 'completedLoan', 'netApprovedAmount'],
        rawOptions,
      );
      if (tempLoan === k500Error) throw new Error();

      const userList = [];
      let newCount = 0;
      let newAmount = 0;
      tempLoan.forEach((e) => {
        userList.push(e.userId);
        if (e.completedLoan < 1) {
          newCount++;
          newAmount += +e.netApprovedAmount;
        }
      });
      return { disbursedCount: newCount, amount: newAmount.toFixed(2) };
    } else if (type == 'IOSDEVICECOUNT') {
      rawOptions.include.push({
        model: registeredUsers,
        attributes: ['id'],
        where: { typeOfDevice: '1' },
      });
    }

    // Total count & amount
    const disbursedCount: any = await this.loanRepo.getTableWhereDataWithCounts(
      [
        'id',
        'netApprovedAmount',
        'netEmiData',
        'approvedDuration',
        'charges',
        'loanFees',
      ],
      rawOptions,
    );
    if (disbursedCount === k500Error) throw new Error();

    // Preparation -> Response
    let amount = 0;
    disbursedCount.rows.forEach((element) => {
      amount += +element.netApprovedAmount;
    });
    amount = +amount.toFixed(2);
    return {
      disbursedCount: +disbursedCount.count,
      amount,
      rows: disbursedCount?.rows ?? [],
    };
  }

  private async getTodaysDisbursmentCountsWithAmount() {
    // Preparation -> Query
    const today = this.typeService.getGlobalDate(new Date()).toJSON();
    const loanAttr: any = [
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalCount'],
      [
        Sequelize.fn(
          'SUM',
          Sequelize.cast(
            Sequelize.col('netApprovedAmount'),
            'double precision',
          ),
        ),
        'totalApprovedAmount',
      ],
    ];
    const loanOptions = {
      where: {
        loan_disbursement_date: today,
        loanStatus: { [Op.in]: ['Active', 'Complete'] },
      },
    };
    // Hit -> Query
    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    // Validation -> Query data
    if (loanData == k500Error) throw new Error();

    const todayD = new Date();
    todayD.setHours(0, 0, 1, 0);
    const todayD1 = new Date();
    todayD1.setHours(23, 59, 59);

    // Today's new user's insights
    const allNewDisbursedUsersCount = await this.getDisbursedUsersCountWhere(
      todayD,
      todayD1,
      'NEWUSERSCOUNT',
    );
    // Today's repeater user's insights
    let repeatedUserAmount =
      +loanData.totalApprovedAmount - +allNewDisbursedUsersCount.amount;
    repeatedUserAmount = +repeatedUserAmount.toFixed(2);
    // Today's IOS user's insights
    const allIOSDevicesCounts: any = await this.getDisbursedUsersCountWhere(
      todayD,
      todayD1,
      'IOSDEVICECOUNT',
    );

    // Preparation -> Return response
    return {
      totalCount: +loanData.totalCount,
      totalApprovedAmount: +loanData.totalApprovedAmount,
      totalNewUsers: allNewDisbursedUsersCount.disbursedCount,
      totalRepeatedUser:
        +loanData.totalCount - +allNewDisbursedUsersCount.disbursedCount,
      newUserAmount: +allNewDisbursedUsersCount.amount,
      repeatedUserAmount,
      iosDeviceCounts: +allIOSDevicesCounts.disbursedCount,
    };
  }

  // total defaulter principal
  async defaultedPrincipal(totalPartPrincipalAmount, loanIds) {
    // Query preparations
    const attributes: any = [
      [
        Sequelize.fn('SUM', Sequelize.col('principalCovered')),
        'principalAmount',
      ],
    ];
    const today = this.typeService.getGlobalDate(new Date()).toJSON();

    const options = {
      where: {
        loanId: loanIds.loanIdOfless180,
        emiDate: { [Op.lte]: today },
      },
    };
    // Query
    const emiData: any = await this.emiRepo.getRowWhereData(
      attributes,
      options,
    );

    if (emiData == k500Error) return kInternalError;
    const defaultedPrincipal = emiData?.principalAmount ?? 0;

    return Math.round(
      Math.abs(defaultedPrincipal ?? 0) -
        Math.abs(totalPartPrincipalAmount ?? 0),
    );
  }

  private async getPartPrincipalAmount(loanIds) {
    const baseQuery = (additionalConditions = '') => `
        SELECT "principalAmount", "emiData"."id" AS "emiData.id"
        FROM "TransactionEntities" AS "TransactionEntity"
        LEFT JOIN "EmiEntities" AS "emiData"
        ON "TransactionEntity"."emiId" = "emiData"."id"
        WHERE "TransactionEntity"."status" = 'COMPLETED'
        AND "TransactionEntity"."type" != 'REFUND'
        ${additionalConditions}
        GROUP BY "TransactionEntity"."id", "emiData"."id";
    `;
    let transList1 = [];
    if (loanIds?.loanIdOfless180)
      transList1 = await this.repoManager.injectRawQuery(
        TransactionEntity,
        baseQuery(`AND "emiData"."loanId" IN (${loanIds?.loanIdOfless180})`),
      );
    let transList2 = [];
    if (loanIds?.loanIdOfAbove180)
      transList2 = await this.repoManager.injectRawQuery(
        TransactionEntity,
        baseQuery(`AND "emiData"."loanId" IN (${loanIds?.loanIdOfAbove180})`),
      );

    let allTransactionList = await this.repoManager.injectRawQuery(
      TransactionEntity,
      baseQuery(`AND "emiData"."penalty_days" > 0 
                AND "emiData"."payment_status" = '0' 
                AND "emiData"."payment_due_status" = '1'`),
    );

    let partPaidPriAmtOfless180Days = 0;
    let partPayPriAmtOfAbove180Days = 0;
    let partPaidPrincipalAmount = 0;

    transList1.forEach((el) => {
      if (
        typeof el?.principalAmount === 'number' &&
        !isNaN(el.principalAmount)
      ) {
        partPaidPriAmtOfless180Days += el.principalAmount;
      }
    });

    transList2.forEach((el) => {
      if (
        typeof el?.principalAmount === 'number' &&
        !isNaN(el.principalAmount)
      ) {
        partPayPriAmtOfAbove180Days += el.principalAmount;
      }
    });

    allTransactionList.forEach((el) => {
      if (
        typeof el?.principalAmount === 'number' &&
        !isNaN(el.principalAmount)
      ) {
        partPaidPrincipalAmount += el.principalAmount;
      }
    });

    return {
      partPaidPrincipalAmount: Math.round(partPaidPrincipalAmount),
      partPaidPriAmtOfless180Days: Math.round(partPaidPriAmtOfless180Days),
      partPayPriAmtOfAbove180Days: Math.round(partPayPriAmtOfAbove180Days),
    };
  }

  // total received principal
  async getReceivedPrincipal() {
    // Query preparations
    const attributes: any = [
      [
        Sequelize.fn('SUM', Sequelize.col('principalCovered')),
        'principalAmount',
      ],
    ];
    const options = { where: { payment_status: '1' } };
    // Query
    const emiData: any = await this.emiRepo.getRowWhereData(
      attributes,
      options,
    );
    if (emiData === k500Error) throw new Error();

    return Math.round(emiData.principalAmount);
  }

  async getPartPayPrincipal180DelayDays() {
    const sqlQuery = `SELECT 
            SUM("principalCovered") AS "principalAmount"
          FROM 
              "TransactionEntities" AS "TransactionEntity" 
          LEFT OUTER JOIN 
              "EmiEntities" AS "emiData" 
          ON 
              "TransactionEntity"."emiId" = "emiData"."id" 
          WHERE 
              "TransactionEntity"."status" = 'COMPLETED' 
              AND "TransactionEntity"."type" != 'REFUND' 
          AND "emiData"."payment_status" = '0' 
                          AND "emiData"."payment_due_status" = '1' 
                          AND "emiData"."penalty_days" > 180 
                 
          GROUP BY 
              "TransactionEntity"."id", 
              "emiData"."id";`;

    const loanData = await this.repoManager.injectRawQuery(
      TransactionEntity,
      sqlQuery,
    );
    if (!loanData || loanData == k500Error) throw new Error();
  }

  // principal of 180+ delay days user's and exist in loss of assest
  async getPrincipal180delayDays(loanIds) {
    if (!loanIds?.loanIdOfAbove180) return { remaningPrincipal: 0 };
    const attributes: any = [
      [
        Sequelize.fn('SUM', Sequelize.col('principalCovered')),
        'principalAmount',
      ],
    ];
    const today = this.typeService.getGlobalDate(new Date()).toJSON();
    const options = {
      where: {
        loanId: loanIds.loanIdOfAbove180,
        emiDate: { [Op.lte]: today },
      },
    };
    // Query
    const emiData: any = await this.emiRepo.getRowWhereData(
      attributes,
      options,
    );
    if (emiData === k500Error) throw new Error();
    return { remaningPrincipal: Math.round(emiData?.principalAmount ?? 0) };
  }

  async getInterest90delayDays() {
    try {
      const sqlQuery = `SELECT COALESCE(SUM(COALESCE(dl.interest_covered, 0) - COALESCE(dl.total_paid_interest, 0)), 0) AS total_remaining_interest
      FROM public."loanTransactions" lt
      INNER JOIN (SELECT e."loanId",
           SUM(CASE 
                   WHEN e.penalty_days > 0 AND e.penalty_days <= 90 THEN e."interestCalculate"
                   ELSE 0 
               END) AS interest_covered,
           SUM(CASE 
                   WHEN e.penalty_days > 0 AND e.penalty_days <= 90 THEN COALESCE(e.paid_interest, 0)
                   ELSE 0 
               END) AS total_paid_interest
      FROM public."EmiEntities" e
      WHERE e.payment_due_status = '1'
      AND e.payment_status = '0'  
      GROUP BY e."loanId") dl ON lt.id = dl."loanId"
      WHERE  dl.interest_covered > 0;`;

      const loanData = await this.repoManager.injectRawQuery(
        loanTransaction,
        sqlQuery,
      );

      if (!loanData || loanData == k500Error) throw new Error();
      return {
        remaningInterest: Math.round(
          loanData[0]?.total_remaining_interest ?? 0,
        ),
      };
    } catch (error) {}
  }

  //#region getPaidCreditAmount
  // function to get paid refund credit statement amount
  async getPaidCreditAmount() {
    // 1. Aggregate paidAmount from transaction table
    const attributes = [
      [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'paidAmount'],
    ];

    const paidResult = await this.transactionRepo.getTableWhereData(
      attributes,
      {
        where: {
          status: str.kCompleted,
          type: { [Op.ne]: str.kRefund },
          source: { [Op.or]: [str.kCredit, str.kCreditPay] },
          subSource: { [Op.or]: [str.kCredit, str.kCreditPay] },
        },
      },
    );

    if (!paidResult || paidResult === k500Error)
      throw new Error('Error in Getting Paid Credit Amount from Transactions');
    const paidAmount = Number(paidResult[0]?.paidAmount ?? 0);

    // 2. Check Redis cache
    const redisKey = 'DIRECT_CREDIT_TRANSFER';
    const cachedAmount = await this.redis.get(redisKey);

    if (cachedAmount !== null && !isNaN(+cachedAmount)) {
      return paidAmount + +cachedAmount;
    }

    // 3. Fallback to DB query if cache is empty
    const query = `
      SELECT SUM(("creditData"->>'amount')::numeric) AS amount
      FROM public."registeredUsers"
      WHERE "creditData" IS NOT NULL;
    `;
    const result = await this.repoManager.injectRawQuery(
      registeredUsers,
      query,
    );
    if (!result || result === k500Error)
      throw new Error('Error in Getting Credit Amount from registeredUsers');
    const userCreditAmount = Number(result[0]?.amount ?? 0);

    // 4. Set Redis for next time
    await this.redis.set(redisKey, userCreditAmount.toString());

    return paidAmount + userCreditAmount;
  }

  //#endregion

  // total upcomming principal
  async getUpcommingPrincipal() {
    const emiAtt: any = [
      [Sequelize.fn('SUM', Sequelize.col('principalCovered')), 'principalAmt'],
      [Sequelize.fn('SUM', Sequelize.col('interestCalculate')), 'interestAmt'],
    ];
    const options = { where: { payment_status: '0', payment_due_status: '0' } };
    const totalPrAmt: any = await this.emiRepo.getRowWhereData(emiAtt, options);
    if (!totalPrAmt || totalPrAmt === k500Error) return kInternalError;
    return {
      principalAmt: Math.round(totalPrAmt?.principalAmt),
      interestAmt: Math.round(totalPrAmt?.interestAmt),
    };
  }

  async fetchAllDocumentsLoanWise(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');
    const loanId = reqData?.loanId;
    try {
      let attachmentDocs: any;
      if (!loanId) {
        attachmentDocs = await this.fetchAllChatDocumentByUserId({
          userId,
          loanId,
        });
        if (attachmentDocs?.message) return attachmentDocs;
      } else {
        attachmentDocs = await this.getNocData(loanId);
        if (attachmentDocs?.message) return attachmentDocs;
      }

      const otherDocs = await this.getOtherDocuments(reqData);
      if (otherDocs?.message) return otherDocs;

      const finalData = await this.prepareFinalData(attachmentDocs, otherDocs);
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getOtherDocuments(reqData) {
    const userId = reqData?.userId;
    const loanId = +reqData?.loanId;
    try {
      const attributes = ['id', 'nocURL'];
      const eSignInclude = {
        model: esignEntity,
        attributes: ['signed_document_upload', 'content_type', 'createdAt'],
      };

      const insuranceInclude = {
        model: InsuranceEntity,
        attributes: ['insuranceURL', 'insuranceURL1', 'createdAt'],
      };
      const options: any = {};
      options.where = loanId ? { id: loanId, userId } : { userId };
      options.order = [['id', 'desc']];
      options.include = [eSignInclude, insuranceInclude];

      const documentDetails = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (documentDetails === k500Error) return kInternalError;
      return documentDetails;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region
  async getNocData(loanId) {
    const response = [];

    const attr = [
      'id',
      'userId',
      'docUrl',
      'docType',
      'type',
      'password',
      'isDeleted',
      'adminId',
      'createdAt',
      'loanId',
    ];
    const options = {
      where: {
        loanId,
        [Op.or]: [
          { docType: ['SETTLED NOC', 'NORMAL NOC'] },
          { docCategory: ['ON_HOLD'] },
        ],
      },
      order: [['id', 'DESC']],
    };

    const nocData = await this.mediaRepo.getTableWhereData(attr, options);
    if (nocData == k500Error || !nocData?.length) return response;
    for (let index = 0; index < nocData.length; index++) {
      const element = nocData[index];
      response.push({
        loanId: element?.loanId,
        userId: element?.userId,
        docUrl: element?.docUrl ?? '-',
        type: element?.type ?? '-',
        docType: element?.docType ?? '-',
        createdAt: element.createdAt,
        adminData: await this.commonServices.getAdminData(element?.adminId),
      });
    }
    return response;
  }
  //#endregion

  async prepareFinalData(attachmentDocs, otherDocs) {
    const finalData = [];
    let hasMediaNoc = false;
    try {
      if (attachmentDocs?.length) {
        for (let attachmentDoc of attachmentDocs) {
          if (
            attachmentDoc?.loanId &&
            ['SETTLED NOC', 'NORMAL NOC'].includes(attachmentDoc?.docType)
          ) {
            hasMediaNoc = true;
          }
          const doc = {
            loanId: attachmentDoc?.loanId ?? '-',
            document: attachmentDoc?.docType ?? '-',
            documentType: attachmentDoc?.type ?? '-',
            password: attachmentDoc?.password ?? '-',
            docUrl: attachmentDoc?.docUrl ?? '-',
            uploadedBy: attachmentDoc?.adminData?.fullName ?? '-',
            date: attachmentDoc?.createdAt
              ? this.dateService.readableDate(attachmentDoc?.createdAt)
              : '-',
          };
          finalData.push(doc);
        }
      }

      for (let otherDoc of otherDocs) {
        if (otherDoc?.nocURL && !hasMediaNoc) {
          const docType = otherDoc.nocURL.split('.').reverse()[0];
          const nocDoc = {
            loanId: otherDoc?.id ?? '-',
            document: 'NOC',
            documentType: docType ?? '-',
            password: '-',
            docUrl: otherDoc?.nocURL ?? '-',
            uploadedBy: '-',
            date: '-',
          };
          finalData.push(nocDoc);
        }

        if (otherDoc?.eSignData) {
          const loanAgreeDoc = {
            loanId: otherDoc?.id ?? '-',
            document: 'Loan Agreement',
            documentType: otherDoc?.eSignData.content_type.toLowerCase() ?? '-',
            password: '-',
            docUrl: otherDoc?.eSignData.signed_document_upload ?? '-',
            uploadedBy: '-',
            date: otherDoc?.eSignData?.createdAt
              ? this.dateService.readableDate(otherDoc?.eSignData.createdAt)
              : '-',
          };
          finalData.push(loanAgreeDoc);
        }

        if (otherDoc?.insuranceData) {
          const insuranceDoc1 = {
            loanId: otherDoc?.id ?? '-',
            document: 'Health & Emi Insurance(Care)',
            documentType: 'pdf',
            password: '-',
            docUrl: otherDoc?.insuranceData?.insuranceURL ?? '-',
            uploadedBy: '-',
            date: otherDoc?.insuranceData?.createdAt
              ? this.dateService.readableDate(
                  otherDoc?.insuranceData?.createdAt,
                )
              : '-',
          };
          const insuranceDoc2 = {
            loanId: otherDoc?.id ?? '-',
            document: 'Loss of job Insurance(Acko)',
            documentType: 'pdf',
            password: '-',
            docUrl: otherDoc?.insuranceData?.insuranceURL1 ?? '-',
            uploadedBy: '-',
            date: otherDoc?.insuranceData?.createdAt
              ? this.dateService.readableDate(
                  otherDoc?.insuranceData?.createdAt,
                )
              : '-',
          };
          finalData.push(insuranceDoc1, insuranceDoc2);
        }
      }

      return finalData;
    } catch (error) {}
  }

  async funUploadBatchFile(reqData) {
    const path = `${reqData?.file?.destination}${reqData?.file?.filename}`;
    const filePath = fs.readFileSync(path);

    const body = new FormData();
    body.append('file', filePath, reqData?.file?.filename);
    const url = nCibilRiskCategoryFromBatch;
    const data = await this.api.post(url, body);
    if (data === k500Error) throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    const totalLists = this.typeService.splitToNChunks(data.data, 1000);
    const riskCategoryMap = {
      'LOW RISK': 0,
      'MODERATE RISK': 1,
      'HIGH RISK': 2,
    };
    const promiseList = [];
    for (let index = 0; index < totalLists.length; index++) {
      const targetList = totalLists[index];
      let updateQuery = '';
      for (let i = 0; i < targetList.length; i++) {
        const ele = targetList[i];
        const loanId = ele.LoanId;
        const cibilBatchCategory = riskCategoryMap[ele['Risk Category']];
        updateQuery += `UPDATE  public."loanTransactions"
        SET "cibilBatchCategory" = '${cibilBatchCategory}' 
        WHERE id = '${loanId}'; `;
      }
      promiseList.push(
        this.repoManager.injectRawQuery(loanTransaction, updateQuery),
      );
    }
    const updates = await Promise.all(promiseList);
    if (updates.some((update) => update === k500Error))
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return true;
  }

  async bankBalance(reqData): Promise<any> {
    const phrases = confirmation_phrases;
    const randomIndex = Math.floor(Math.random() * phrases.length);
    const phrase = phrases[randomIndex];
    const isRefresh = reqData.isRefresh == 'true';
    const isNotify = reqData.notify == 'true';

    let existingData = await this.redis.getKeyDetails('RAZOR_X_BALANCE');
    if (existingData && !isRefresh) {
      try {
        existingData = JSON.parse(existingData);
        existingData.phrase = phrase;
        return existingData;
      } catch (error) {}
    }

    const balanceData: any = {
      phrase,
      yes: '-1',
      lastUpdated: new Date().toJSON(),
      repaymentBalance: [],
      whatsAppBalance: {
        LSP: -1,
        NBFC: -1,
      },
      ICCDBalance: { CARE: -1 },
    };
    balanceData.repaymentBalance = await this.razorpay.getRepaymentBalance();

    if (EnvConfig.nbfc.nbfcType === '0') {
      let YESdata: any = await this.razorpay.checkBalance();
      if (YESdata?.message) YESdata = {};
      if (YESdata?.amount) {
        balanceData.yes = this.typeService.manageAmount(YESdata?.amount);
        balanceData.yes = this.typeService.amountNumberWithCommas(
          balanceData.yes,
        );
      }
    } else {
      const sessionData = await this.redis.getKeyDetails('RAZOR_X_SESSION');
      const headers = { cookie: sessionData, origin: 'https://x.razorpay.com' };
      const yesUrl =
        nRazorXFetchBalance +
        `&id=${EnvConfig.razorPay.RAZOR_X_YES_BANK_ACC_ID}`;
      const yesResponse = await this.api.get(yesUrl, null, null, { headers });
      if (yesResponse && yesResponse !== k500Error) {
        const yesData = yesResponse?.data?.items ?? [{}];
        balanceData.yes = this.typeService.amountNumberWithCommas(
          (yesData[0].balance ?? -1) / 100,
        );
      }
    }

    const lspWaToken = await this.redis.getKeyDetails('WA_LSP_TOKEN');
    if (lspWaToken) {
      const headers = { authorization: 'Bearer ' + lspWaToken };
      const response = await this.api.get(nWaWalletCheck, null, headers);
      if (response && response != k500Error) {
        if (response?.success == true && response.data) {
          const responseData = response.data[0];
          if (responseData.totalBalance != null) {
            const lspWaBalance = Math.floor(responseData.totalBalance / 10000);
            balanceData.whatsAppBalance.LSP = lspWaBalance;
          }
        }
      }
    }

    const nbfcWaToken = await this.redis.getKeyDetails('WA_NBFC_TOKEN');
    if (nbfcWaToken) {
      const headers = { authorization: 'Bearer ' + nbfcWaToken };
      const response = await this.api.get(nWaWalletCheck, null, headers);
      if (response && response != k500Error) {
        if (response?.success == true && response.data) {
          const responseData = response.data[0];
          if (responseData.totalBalance != null) {
            const nbfcWaBalance = Math.floor(responseData.totalBalance / 10000);
            balanceData.whatsAppBalance.NBFC = nbfcWaBalance;
          }
        }
      }
    }
    // check insurance wallet balance
    const ICCDBalance: any =
      await this.elephantService.funGetICCDBalanceCheck();
    if (ICCDBalance?.ICCDBalance)
      balanceData.ICCDBalance.CARE = ICCDBalance?.ICCDBalance;

    await this.redis.updateKeyDetails(
      'RAZOR_X_BALANCE',
      JSON.stringify(balanceData),
      60 * 5,
    );

    if (isNotify) {
      if (balanceData.yes >= 0 && balanceData.yes <= 1000000) {
        const text = `
        Important Alert ! :warning:\nNBFC - ${EnvConfig.nbfc.nbfcName}\nRazorpayX Disbursement balance is below 10Lakhs (YES BANK)\nCurrent balance: Rs. ${balanceData.yes}`;
        await this.slack.sendMsg({
          channel: gIsPROD
            ? EnvConfig.slack.accountsChannelId
            : EnvConfig.slack.testBot,
          text,
        });
      }
    }
    return balanceData;
  }

  async autoDebitScreenshots(reqData) {
    const page = +(reqData?.page ?? 1);
    const isCountOnly = reqData?.isCountOnly ?? false;
    const fromDate =
      reqData.fromDate ?? this.dateService.getGlobalDate(new Date()).toJSON();

    if (isCountOnly) {
      const query = `SELECT COUNT(id)::int, COALESCE(SUM("principalCovered" + "interestCalculate"), 0) AS "totalEMIAmount"
      FROM public."EmiEntities" where "emi_date" = '${fromDate}' and "adScreenshotUrl" IS NOT NULL`;
      const ssData = await this.repoManager.injectRawQuery(EmiEntity, query);
      if (ssData == k500Error) return kInternalError;
      return ssData[0];
    }

    // DB Query
    const userInclude = {
      model: registeredUsers,
      attributes: ['fullName', 'phone'],
    };
    const transInclude = {
      model: TransactionEntity,
      attributes: ['status', 'emiId', 'subSource'],
      where: { subSource: str.kAutoDebit },
      required: false,
    };
    const options: any = {
      useMaster: false,
      where: { emi_date: fromDate, adScreenshotUrl: { [Op.ne]: null } },
      include: [userInclude, transInclude],
      order: [['updatedAt', 'ASC']],
      offset: page * PAGE_LIMIT - PAGE_LIMIT,
      limit: PAGE_LIMIT,
    };
    const screenshotData = await this.emiRepo.getTableWhereDataWithCounts(
      [
        'id',
        'userId',
        'adScreenshotUrl',
        'loanId',
        'principalCovered',
        'interestCalculate',
        'emiNumber',
        'updatedAt',
      ],
      options,
    );
    if (screenshotData == k500Error) return kInternalError;

    const data = screenshotData.rows;
    const prepareData = [];
    // Itrating On Today's Data of Screenshot Uploaded By Users
    for (let index = 0; index < data.length; index++) {
      const ele = data[index];
      const adStatus = ele?.transactionData ? ele?.transactionData[0] : {};
      // Preparing Data
      let obj = {};
      let userId = ele?.userId;
      let fullName = ele?.user?.fullName ?? '-';
      let phone = this.cryptService.decryptPhone(ele?.user?.phone);
      let loanId = ele?.loanId;
      let emiAmount =
        (ele?.principalCovered ?? 0) + (ele?.interestCalculate ?? 0);
      emiAmount = this.strService.readableAmount(emiAmount);
      let emiNumber = ele?.emiNumber;
      let date = this.dateService.dateToReadableFormat(new Date());
      let formattedDate = date.readableStr ?? '-';
      let imageUrl = ele?.adScreenshotUrl ?? '-';

      obj['Loan ID'] = loanId;
      obj['Name'] = fullName;
      obj['Phone Number'] = phone;
      obj['EMI No.'] = emiNumber;
      obj['Amount'] = emiAmount;
      obj['Date'] = formattedDate;
      obj['AD Status'] = adStatus?.status ?? '-';
      obj['Screenshot'] = imageUrl;
      obj['userId'] = userId;

      prepareData.push(obj); // Pushing Prepared Data
    }
    return { count: screenshotData?.count, rows: prepareData };
  }

  async getLoanIds() {
    try {
      const toDate = this.typeService.getGlobalDate(new Date()).toJSON();
      const sqlQuery = `SELECT   
     lt."id" AS "loanId",
	 -- 	 DPD Days EMI wise
     CASE WHEN "e1"."emi_date" = '${toDate}' OR "e1"."payment_due_status" = '0' THEN 0
          WHEN "e1"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e1"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e1"."payment_due_status" = '1' AND "e1"."payment_done_date" <= '${toDate}' THEN TO_DATE("e1"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_1_dpd",
     CASE WHEN "e2"."emi_date" >= '${toDate}' OR "e2"."payment_due_status" = '0' THEN 0
          WHEN "e2"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e2"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e2"."payment_due_status" = '1' AND "e2"."payment_done_date" <= '${toDate}' THEN TO_DATE("e2"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_2_dpd", 
     CASE WHEN "e3"."emi_date" >= '${toDate}' OR "e3"."payment_due_status" = '0' THEN 0
          WHEN "e3"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e3"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e3"."payment_due_status" = '1' AND "e3"."payment_done_date" <= '${toDate}' THEN TO_DATE("e3"."payment_done_date" , 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_3_dpd",
     CASE WHEN "e4"."emi_date" >= '${toDate}' OR "e4"."payment_due_status" = '0' THEN 0
          WHEN "e4"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e4"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e4"."payment_due_status" = '1' AND "e4"."payment_done_date" <= '${toDate}' THEN TO_DATE("e4"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_4_dpd",
     CASE WHEN "e5"."emi_date" >= '${toDate}' OR "e5"."payment_due_status" = '0' THEN 0
          WHEN "e5"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e5"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e5"."payment_due_status" = '1' AND "e5"."payment_done_date" <= '${toDate}' THEN TO_DATE("e5"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_5_dpd", 
     CASE WHEN "e6"."emi_date" >= '${toDate}' OR "e6"."payment_due_status" = '0' THEN 0
          WHEN "e6"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e6"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e6"."payment_due_status" = '1' AND "e6"."payment_done_date" <= '${toDate}' THEN TO_DATE("e6"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END AS "emi_6_dpd",           
	 
--      Max DPD among all 4 EMIS
GREATEST(
     CASE WHEN "e1"."emi_date" >= '${toDate}' OR "e1"."payment_due_status" = '0' THEN 0
          WHEN "e1"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e1"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e1"."payment_due_status" = '1' AND "e1"."payment_done_date" <= '${toDate}' THEN TO_DATE("e1"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e2"."emi_date" >= '${toDate}' OR "e2"."payment_due_status" = '0' THEN 0
          WHEN "e2"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e2"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e2"."payment_due_status" = '1' AND "e2"."payment_done_date" <= '${toDate}' THEN TO_DATE("e2"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e3"."emi_date" >= '${toDate}' OR "e3"."payment_due_status" = '0' THEN 0
          WHEN "e3"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e3"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e3"."payment_due_status" = '1' AND "e3"."payment_done_date" <= '${toDate}' THEN TO_DATE("e3"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e4"."emi_date" >= '${toDate}' OR "e4"."payment_due_status" = '0' THEN 0
          WHEN "e4"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e4"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e4"."payment_due_status" = '1' AND "e4"."payment_done_date" <= '${toDate}' THEN TO_DATE("e4"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e5"."emi_date" >= '${toDate}' OR "e5"."payment_due_status" = '0' THEN 0
          WHEN "e5"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e5"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e5"."payment_due_status" = '1' AND "e5"."payment_done_date" <= '${toDate}' THEN TO_DATE("e5"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END ,   
      CASE WHEN "e6"."emi_date" >= '${toDate}' OR "e6"."payment_due_status" = '0' THEN 0
          WHEN "e6"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e6"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e6"."payment_due_status" = '1' AND "e6"."payment_done_date" <= '${toDate}' THEN TO_DATE("e6"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END        
     ) AS "max_dpd"
	 
FROM public."loanTransactions" AS "lt"

-- Join all 4 EMIs (Loan wise record)
LEFT JOIN "EmiEntities" AS "e1" ON "e1"."loanId" = "lt"."id" AND "e1"."emiNumber" = 1
LEFT JOIN "EmiEntities" AS "e2" ON "e2"."loanId" = "lt"."id" AND "e2"."emiNumber" = 2
LEFT JOIN "EmiEntities" AS "e3" ON "e3"."loanId" = "lt"."id" AND "e3"."emiNumber" = 3
LEFT JOIN "EmiEntities" AS "e4" ON "e4"."loanId" = "lt"."id" AND "e4"."emiNumber" = 4
LEFT JOIN "EmiEntities" AS "e5" ON "e5"."loanId" = "lt"."id" AND "e5"."emiNumber" = 5
LEFT JOIN "EmiEntities" AS "e6" ON "e6"."loanId" = "lt"."id" AND "e6"."emiNumber" = 6

     WHERE ("e1"."payment_status" = '0' AND "e1"."payment_due_status" = '1') or 
("e2"."payment_status" = '0' AND "e2"."payment_due_status" = '1') or
("e3"."payment_status" = '0' AND "e3"."payment_due_status" = '1') or
("e4"."payment_status" = '0' AND "e4"."payment_due_status" = '1') or
("e5"."payment_status" = '0' AND "e5"."payment_due_status" = '1') or
("e6"."payment_status" = '0' AND "e6"."payment_due_status" = '1') 
 
	 GROUP BY 
	 lt."id", lt."id", lt."netApprovedAmount", lt."loan_disbursement_date", lt."interestRate", lt."approvedDuration", lt."loanCompletionDate", lt."Loss_assets",
   "e1"."emi_date", "e2"."emi_date", "e3"."emi_date", "e4"."emi_date", "e5"."emi_date","e6"."emi_date",
	 "e1"."payment_due_status",  "e2"."payment_due_status",  "e3"."payment_due_status",  "e4"."payment_due_status", "e5"."payment_due_status", "e6"."payment_due_status",
	 "e1"."payment_status",  "e2"."payment_status",  "e3"."payment_status",  "e4"."payment_status","e5"."payment_status","e6"."payment_status",
	 "e1"."payment_done_date",  "e2"."payment_done_date",  "e3"."payment_done_date",  "e4"."payment_done_date","e5"."payment_done_date","e6"."payment_done_date",
	 "e1"."principalCovered", "e2"."principalCovered", "e3"."principalCovered", "e4"."principalCovered","e5"."principalCovered","e6"."principalCovered",
	 "e1"."interestCalculate", "e2"."interestCalculate", "e3"."interestCalculate", "e4"."interestCalculate","e5"."interestCalculate", "e6"."interestCalculate"

-- Filter based on MAX DPD
HAVING GREATEST(
     CASE WHEN "e1"."emi_date" >= '${toDate}' OR "e1"."payment_due_status" = '0' THEN 0
          WHEN "e1"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e1"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e1"."payment_due_status" = '1' AND "e1"."payment_done_date" <= '${toDate}' THEN TO_DATE("e1"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e1"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e2"."emi_date" >= '${toDate}' OR "e2"."payment_due_status" = '0' THEN 0
          WHEN "e2"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e2"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e2"."payment_due_status" = '1' AND "e2"."payment_done_date" <= '${toDate}' THEN TO_DATE("e2"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e2"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e3"."emi_date" >= '${toDate}' OR "e3"."payment_due_status" = '0' THEN 0
          WHEN "e3"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e3"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e3"."payment_due_status" = '1' AND "e3"."payment_done_date" <= '${toDate}' THEN TO_DATE("e3"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e3"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e4"."emi_date" >= '${toDate}' OR "e4"."payment_due_status" = '0' THEN 0
          WHEN "e4"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e4"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e4"."payment_due_status" = '1' AND "e4"."payment_done_date" <= '${toDate}' THEN TO_DATE("e4"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e4"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,
     CASE WHEN "e5"."emi_date" >= '${toDate}' OR "e5"."payment_due_status" = '0' THEN 0
          WHEN "e5"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e5"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e5"."payment_due_status" = '1' AND "e5"."payment_done_date" <= '${toDate}' THEN TO_DATE("e5"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e5"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END,  
      CASE WHEN "e6"."emi_date" >= '${toDate}' OR "e6"."payment_due_status" = '0' THEN 0
          WHEN "e6"."payment_status" = '0' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          WHEN "e6"."payment_done_date" > '${toDate}' THEN TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
		  WHEN "e6"."payment_due_status" = '1' AND "e6"."payment_done_date" <= '${toDate}' THEN TO_DATE("e6"."payment_done_date", 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("e6"."emi_date", 'YYYY-MM-DDTHH24:MI:SS')
          END      
     )>0 and   "lt"."loanStatus" NOT IN ('Complete','Rejected')`;

      const loanData = await this.repoManager.injectRawQuery(
        loanTransaction,
        sqlQuery,
      );

      if (!loanData || loanData == k500Error) throw new Error();

      const loanIdOfAbove180 = loanData
        .filter((el) => el.max_dpd > 180)
        .map((el) => el.loanId); // Extract loanIds where max_dpd > 180

      const loanIdOfless180 = loanData
        .filter((el) => el.max_dpd <= 180)
        .map((el) => el.loanId); // Extract loanIds where max_dpd <= 180

      return {
        loanIdOfAbove180,
        loanIdOfless180,
      };
    } catch (error) {}
  }

  async aumDetails() {
    const key = RedisKeys.AUM_DETAILS;
    const existingData = await this.redis.getKeyDetails(key);
    if (existingData) {
      try {
        return JSON.parse(existingData);
      } catch (error) {
        console.log('JSON parse error at aumDetails');
      }
    }

    const insightsData: any = await this.recoveredAmountInsights();
    insightsData.last_updated_at = new Date().toJSON();
    const value = JSON.stringify(insightsData);
    const exp_time = NUMBERS.THREE_HOURS_IN_SECONDS;
    await this.redis.updateKeyDetails(key, value, exp_time);

    return insightsData;
  }

  private getAPRForLoan(loanData) {
    const netEmiData = loanData?.netEmiData ?? [];
    const totalIntererst = netEmiData?.reduce((total, emi) => {
      const emiData = emi ? JSON.parse(emi) : {};
      const interest = +(emiData?.InterestCalculate ?? 0);
      total += interest;
      return total;
    }, 0);
    const gstAmount = loanData?.charges?.gst_amt ?? 0;
    const netApprovedAmount = +(loanData?.netApprovedAmount ?? 0);
    const percentageChange =
      ((totalIntererst + +(loanData?.loanFees ?? 0) - gstAmount) /
        netApprovedAmount) *
      100;

    return (percentageChange * 365) / +(loanData?.approvedDuration ?? 0);
  }

  private getAverageAPRAndAverageLoanTenure(loanList) {
    let totalApr = 0;
    let totalLoanTenure = 0;
    const length = loanList.length;
    loanList?.forEach((loan) => {
      totalApr = totalApr + this.getAPRForLoan(loan);
      totalLoanTenure = totalLoanTenure + +(loan.approvedDuration ?? 0);
    });
    return {
      averageLoanTenure: length > 0 ? totalLoanTenure / length : 0,
      averageAPR: length > 0 ? totalApr / length : 0,
    };
  }

  async getAllUploadList(query) {
    const listType = query.listType ?? 'loanWise';
    if (
      !(
        listType == 'loanWise' ||
        listType == 'legalWise' ||
        listType == 'collectionWise'
      )
    )
      return kInvalidParamValue('listType');
    if (listType == 'loanWise') return kLoanTabUploadDocList;
    else if (listType == 'collectionWise') return kCollectionTabUploadDocList;
    else return kLegalTabUploadDocList;
  }

  async razorpayBalance() {
    if (!gIsPROD) return;
    // Only Works for Primary NBFC
    if (EnvConfig.nbfc.nbfcType === '1') return true;
    const balance: any = await this.razorpay.checkBalance();
    if (balance?.message) return balance;
    let { bank, accountNumber, amount } = balance;
    amount = this.typeService.manageAmount(amount);
    amount = '₹' + this.typeService.amountNumberWithCommas(amount);
    accountNumber = bank + '-' + accountNumber;
    // Preparing Text from Data
    let text = `*Account* : ${accountNumber} \n ${bank} Available Account Balance is *${amount}*`;
    // Checking Amount for Warning Message
    let minRazorypayBalance = await this.redis.get('MIN_RAZORPAY_BALANCE');
    let warningAmount = minRazorypayBalance ?? MIN_RAZORPAY_BALANCE;
    if (amount <= warningAmount) {
      warningAmount = this.typeService.amountNumberWithCommas(warningAmount);
      text += `\n :warning: *Your Account Balance is Below Than ₹${warningAmount} !!! Please Take Immediate Action* :warning:`;
    }

    // Sending Message to Slack Channel
    await this.slack.sendMsg({
      channel: EnvConfig.slack.accountsAlert,
      text,
      sourceStr: false,
    });
    if (!minRazorypayBalance)
      await this.redis.set('MIN_RAZORPAY_BALANCE', MIN_RAZORPAY_BALANCE);
    return true;
  }

  //#region
  async insuranceBalance() {
    const nbfc = EnvConfig.nbfcType;
    if (!gIsPROD) return;
    else if (nbfc == '1') return;

    // check insurance wallet balance
    const ICCDBalance: any =
      await this.elephantService.funGetICCDBalanceCheck();

    if (ICCDBalance?.message) return;
    else if (
      ICCDBalance &&
      ICCDBalance?.ICCDBalance &&
      ICCDBalance?.ICCDBalance <= 10000
    ) {
      const formattedBalance = this.typeService.amountNumberWithCommas(
        ICCDBalance?.ICCDBalance,
      );

      const slackChannelId = gIsPROD
        ? EnvConfig.slack.accountsAlert
        : EnvConfig.slack.channelId;
      // Preparing the alert message with a warning
      const careAlertText = `CARE BALANCE: ₹${formattedBalance}\n:warning: Your Account Balance is Below ₹10,000 !!! Please Take Immediate Action :warning:`;

      // Sending Message to Slack Channel
      await this.slack.sendMsg({
        channel: slackChannelId,
        text: careAlertText,
        sourceStr: false,
      });
    }
  }
  //#endregion

  async getCollectionDocuments(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');

    const attributes = ['docUrl', 'docType', 'type', 'adminId', 'createdAt'];
    const options = {
      where: {
        userId,
        isDeleted: { [Op.not]: true },
        docType: { [Op.in]: kCollectionTabUploadDocList },
      },
      order: [['id', 'DESC']],
    };
    const collectionData = await this.mediaRepo.getTableWhereData(
      attributes,
      options,
    );
    if (collectionData == k500Error)
      throw new Error('Unable to fetch collection data');

    const collectionRecord = [];
    let collectionObj: any = {};

    if (!collectionData?.length) return collectionRecord;
    for (let i = 0; i < collectionData?.length; i++) {
      const currRecord = collectionData[i];
      const adminName =
        (await this.commonServices.getAdminData(currRecord?.adminId))
          ?.fullName ?? '-';

      collectionObj = {};
      collectionObj['Document'] = currRecord?.docType ?? '-';
      collectionObj['Document Type'] = currRecord?.type ?? '-';
      collectionObj['Updated By'] = adminName;
      collectionObj['Uploaded Date'] =
        this.dateService.formatDate(currRecord?.createdAt) ?? '-';
      collectionObj['View'] = currRecord?.docUrl ?? '-';
      collectionRecord.push(collectionObj);
    }
    return collectionRecord;
  }

  //#region
  async dailyQualityParamCheck(query) {
    try {
      let today = query?.date || new Date();
      today = this.typeService.getGlobalDate(today);
      let qualityStartDate = new Date(QUALITY_PARAMETER_START_DATE);
      qualityStartDate = this.typeService.getGlobalDate(qualityStartDate);

      const loans = await this.loanRepo.getTableWhereData(
        [
          'id',
          'completedLoan',
          'qualityParameters',
          'loan_disbursement_date',
          'manualVerification',
          'bankingId',
        ],
        {
          where: {
            loanStatus: ['Active', 'Complete'],
            loan_disbursement_date: {
              [Op.gte]: qualityStartDate.toJSON(),
              [Op.lte]: today.toJSON(),
            },
          },
        },
      );

      if (loans == k500Error) throw new Error();
      if (!loans?.length) return kSuccessData;
      const bankIds = loans.map((entry) => entry.bankingId);

      const bankingData = await this.bankRepo.getTableWhereData(['loanId'], {
        where: {
          salaryVerification: '3',
          id: bankIds,
        },
        order: [['id', 'DESC']],
      });

      let agentSummary: any = {};
      let manualDisbursementCount = 0;
      let backlogNew = 0;
      let backlogRepeat = 0;

      const queryDate = query?.date ? new Date(query?.date) : new Date();

      for (let index = 0; index < loans.length; index++) {
        const loan = loans[index];
        let loan_disbursement_date = new Date(loan?.loan_disbursement_date);

        let isManual = false;
        if (loan?.manualVerification == '3') isManual = true;
        else {
          const bank = bankingData.find((data) => data?.loanId == loan?.id);
          if (bank?.loanId) isManual = true;
        }

        ///manage new user manual disbursement count
        if (
          loan?.completedLoan == 0 &&
          isManual &&
          loan_disbursement_date.toDateString() == queryDate.toDateString()
        )
          manualDisbursementCount += 1;

        ///manage backlog counts
        if (!loan.qualityParameters && isManual) {
          if (loan.completedLoan > 0) backlogRepeat += 1;
          else backlogNew += 1;
        }

        const qp = loan.qualityParameters;
        if (!qp || !qp.submission_date) continue;
        const submission_date = new Date(qp.submission_date);

        ///manage quality parameter count
        if (submission_date.toDateString() == queryDate.toDateString()) {
          const adminId = qp.adminId;
          const adminData = await this.commonServices.getAdminData(adminId);
          const name = adminData?.fullName || null;
          if (!name) continue;
          if (!agentSummary[adminId]) {
            agentSummary[adminId] = { name, newLoan: 0, repeatedLoan: 0 };
          }
          if (loan.completedLoan > 0) {
            agentSummary[adminId].repeatedLoan += 1;
          } else {
            agentSummary[adminId].newLoan += 1;
          }
        }
      }

      const totalBacklog = backlogNew + backlogRepeat;
      const headers = [
        'Category',
        'Agent',
        'New Loans',
        'Repeat Loans',
        'Total Verified',
      ];

      const roleId = gIsPROD
        ? QUALITY_PARAMETER_ROLE_ID
        : QUALITY_PARAMETER_ROLE_ID_UAT;
      let qualityAgents = await this.commonServices.getAllAdminData();
      qualityAgents = qualityAgents.filter((admin) => admin.roleId == roleId);

      const response = [];
      if (qualityAgents?.length) {
        for (const agent of qualityAgents) {
          if (!agentSummary[agent.id]) {
            agentSummary[agent.id] = {
              name: agent.fullName,
              newLoan: 0,
              repeatedLoan: 0,
            };
          }
        }
      }

      for (const [index, agent] of qualityAgents.entries()) {
        const summary = agentSummary[agent.id];
        response.push({
          Category: index === 0 ? 'Agent-wise Verification' : '',
          Agent: summary.name,
          'New Loans': summary.newLoan,
          'Repeat Loans': summary.repeatedLoan,
          'Total Verified': summary.newLoan + summary.repeatedLoan,
        });
      }

      let formattedTable = null;
      if (response?.length) {
        formattedTable = this.commonServices.formatSlackMesseageTable({
          headers,
          callData: response,
        });
      }

      ///prepare slack message
      const todayDate = this.dateService.dateToReadableFormat(
        new Date(today) || new Date(),
      );

      const formatBacklogDate =
        this.dateService.dateToReadableFormat(
          new Date(QUALITY_PARAMETER_START_DATE),
          'DD-MM-YYYY',
        )?.readableStr || QUALITY_PARAMETER_START_DATE;

      const headerString =
        `Quality Team Report for *${todayDate?.readableStr || ''}*` +
        `\n*New Loans Disbursed(Manual) Today:* ${
          manualDisbursementCount ?? 0
        }` +
        `\n*Backlog Summary:*\nNew Loans: ${backlogNew}\nRepeat Loans: ${backlogRepeat}\nTotal Backlog: ${totalBacklog} (since ${formatBacklogDate})`;

      const messageText = `${headerString}\n${formattedTable || ''}`;

      const slackChannelId = gIsPROD
        ? EnvConfig.slack.qualityParameter
        : EnvConfig.slack.channelId;

      await this.slack.sendMsg({
        text: messageText,
        sourceStr: false,
        channel: slackChannelId,
      });
      return response;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion
  async dailyBalanceAlert() {
    // if (!gIsPROD) return;
    const slackChannelId = gIsPROD
      ? EnvConfig.slack.baTeamChannelId
      : EnvConfig.slack.channelId;
    let msgText = ':memo: *Daily Service Balance Report :*\n';

    ///send msg91 text message balance and send to slack 9AM
    const msg91Text = await this.msg91Balance();
    msgText += msg91Text;

    await this.slack.sendMsg({
      channel: slackChannelId,
      text: msgText,
      sourceStr: false,
    });
    return true;
  }

  //#region check msg91 balance
  async msg91Balance() {
    const lspReqParams = { authkey: kLspMSG91Headers.authkey, type: 0 };
    const nbfcReqParams = { authkey: kMSG91Headers.authkey, type: 0 };
    let lspWaBalance = await this.api.get(MSG91_BALANCE_URL, lspReqParams);
    let nbfcWaBalance = await this.api.get(MSG91_BALANCE_URL, nbfcReqParams);
    if (lspWaBalance != k500Error && !isNaN(lspWaBalance))
      lspWaBalance =
        '₹' + this.typeService.amountNumberWithCommas(lspWaBalance);
    else lspWaBalance = 'Error While Fetching Balance';
    if (nbfcWaBalance != k500Error && !isNaN(nbfcWaBalance))
      nbfcWaBalance =
        '₹' + this.typeService.amountNumberWithCommas(nbfcWaBalance);
    else nbfcWaBalance = 'Error While Fetching Balance';

    const msgText = `*MSG91 (${EnvConfig.nbfc.appName}) : ${lspWaBalance}*\n*MSG91 (${EnvConfig.nbfc.nbfcShortName}) : ${nbfcWaBalance}*\n`;
    return msgText;
  }
  //#endregion

  async fetchUPIPaymentAuditData(query: any) {
    const { fromDate, toDate, sendAllData = false } = query;

    const startingFromDate = fromDate ?? new Date().toISOString();
    const startingToDate = toDate ?? new Date().toISOString();

    const dateRange = this.dateService.utcDateRange(
      startingFromDate,
      startingToDate,
    );

    const options: any = {
      where: {
        apiEndpoint: '/icici/callback',
        createdAt: {
          $gte: new Date(dateRange.minRange),
          $lte: new Date(dateRange.maxRange),
        },
      },
      limit: sendAllData ? 10000 : 10,
    };

    const lists: any = await this.repoManager.fetchDataFromMongoDB(
      APILogger,
      options,
    );
    if (lists === k500Error)
      throw new Error('Error in fetching data from MongoDB');

    if (!lists)
      return k422ErrorMessage(
        'No valid UPI payment logs found in the selected date range.',
      );

    const seenMerchantPaymentPairs = new Set();
    const rows = [];

    for (const entry of lists) {
      let data: any = {};
      try {
        const parsedBody =
          typeof entry.body === 'string'
            ? JSON.parse(entry.body || '{}')
            : entry.body;

        data = parsedBody?.data ?? parsedBody;
      } catch (error) {
        throw k422ErrorMessage('Invalid JSON Format');
      }

      const merchantTranId = data?.merchantTranId ?? '-';
      const paymentStatus = (data?.TxnStatus ?? '-').toUpperCase();
      const uniqueKey = `${merchantTranId}-${paymentStatus}`;

      if (
        seenMerchantPaymentPairs.has(uniqueKey) ||
        !data ||
        Object.keys(data)?.length == 0
      )
        continue;
      seenMerchantPaymentPairs.add(uniqueKey);

      let dateAndTimeOfPayment = '-';
      const originalTxnCompletionDate = data?.TxnCompletionDate;
      if (originalTxnCompletionDate?.length === 14) {
        const year = originalTxnCompletionDate.slice(0, 4);
        const month = originalTxnCompletionDate.slice(4, 6);
        const day = originalTxnCompletionDate.slice(6, 8);
        const hour = originalTxnCompletionDate.slice(8, 10);
        const minute = originalTxnCompletionDate.slice(10, 12);
        const second = originalTxnCompletionDate.slice(12, 14);
        const paymentDate = new Date(
          `${year}-${month}-${day}T${hour}:${minute}:${second}Z`,
        );
        dateAndTimeOfPayment = this.typeService
          .getGlobalDate(paymentDate)
          .toJSON();
      }

      rows.push({
        createdAtTime: (entry as any).createdAt ?? '-',
        transactionId: merchantTranId,
        utr: data?.BankRRN ?? '-',
        amountReceived: data?.PayerAmount ?? '-',
        paymentStatus,
        dateAndTimeOfPayment,
        iciciOrderId: merchantTranId,
      });
    }

    return {
      totalCount: rows.length,
      rows,
    };
  }

  //#region
  async funLenderEmiReminder(query) {
    try {
      let today = query?.date || new Date();
      today = this.typeService.getGlobalDate(today);

      const reminderStart = new Date(today);
      const reminderEnd = new Date(today);

      reminderEnd.setDate(
        reminderEnd.getDate() + LENDER_EMI_REMINDER_WINDOW_DAYS,
      );

      const { fromDate, endDate } = this.typeService.getUTCDateRange(
        reminderStart.toString(),
        reminderEnd.toString(),
      );

      const emiPayments = await this.repoManager.getTableWhereData(
        HypothecationPaymentsEntity,
        ['id', 'emi_date', 'emiAmount', 'emiPaid', 'lender_id'],
        {
          where: {
            [Op.or]: [
              {
                emi_date: {
                  [Op.gte]: fromDate,
                  [Op.lt]: endDate,
                },
                emiPaid: false,
              },
              { emiPaid: false },
            ],
          },
          order: [['emi_date', 'ASC']],
        },
      );

      if (emiPayments == k500Error) return;
      if (!emiPayments?.length) return;
      const lenderIds = emiPayments.map((e) => e.lender_id);

      const lenders = await this.repoManager.getTableWhereData(
        HypothecationEntity,
        ['id', 'lenderName'],
        { where: { id: lenderIds } },
      );
      if (lenders == k500Error || !lenders?.length) return kInternalError;

      const lenderNames = {};
      for (const lender of lenders) {
        lenderNames[lender.id] = lender.lenderName;
      }

      const remindersEmis = [];
      const dueEmis = [];

      const headers = ['Sr No.', 'Lender name', 'EMI Amount', 'EMI Due Date'];

      for (let index = 0; index < emiPayments.length; index++) {
        try {
          const element = emiPayments[index];
          if (new Date(element?.emi_date) < new Date()) {
            dueEmis.push({
              'Sr No.': index + 1,
              'Lender name': lenderNames[element?.lender_id] || '-',
              'EMI Amount': `₹${element?.emiAmount.toLocaleString('en-IN')}`,
              'EMI Due Date': this.dateService.dateToReadableFormat(
                this.typeService.getGlobalDate(element?.emi_date),
                'DD/MM/YYYY',
              ).readableStr,
            });
          } else {
            remindersEmis.push({
              'Sr No.': index + 1,
              'Lender name': lenderNames[element?.lender_id] || '-',
              'EMI Amount': `₹${element?.emiAmount.toLocaleString('en-IN')}`,
              'EMI Due Date': this.dateService.dateToReadableFormat(
                this.typeService.getGlobalDate(element?.emi_date),
                'DD/MM/YYYY',
              ).readableStr,
            });
          }
        } catch (error) {
          console.log('error', error);
        }
      }

      const slackChannelId = gIsPROD
        ? EnvConfig.slack.accountsAlert
        : EnvConfig.slack.testBot;
      if (remindersEmis?.length) {
        const formattedTable = this.commonServices.formatSlackMesseageTable({
          headers,
          callData: remindersEmis,
        });
        const headerString = `EMI Reminder – Upcoming Payment Details`;
        const messageText = `${headerString}\n\n${formattedTable}`;

        await this.slack.sendMsg({
          text: messageText,
          sourceStr: false,
          channel: slackChannelId,
        });
      }

      if (dueEmis?.length) {
        const formattedTable = this.commonServices.formatSlackMesseageTable({
          headers,
          callData: dueEmis,
        });
        const headerString = `EMI Due – Due Payment Details`;
        const messageText = `${headerString}\n\n${formattedTable}`;

        await this.slack.sendMsg({
          text: messageText,
          sourceStr: false,
          channel: slackChannelId,
        });
      }

      return kSuccessData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion
}

// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  GlobalServices,
  NAME_VALIDATE,
  SYSTEM_ADMIN_ID,
  GL<PERSON><PERSON>L_RANGES,
  manualBanksList,
  GL<PERSON><PERSON>L_CHARGES,
  MAX_INQUIRY_PAST_30_DAYS,
  GLOBAL_FLOW,
  gIsPROD,
  CSE_ROLE_ID,
  BAD_CIBIL_SCORE_REASON_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import {
  APPROVED_SALARY_IS_NOT_MACTCH_WITH_STATE_SALARY,
  kFinalLoanAcceptBody,
  kFinalLoanAcceptTitle,
  kGlobalTrail,
  kHighRisk,
  kInternalPolicy,
  kKeyScoreData,
  kLoanDeclined,
  kModerateRisk,
  kNoActiveScoreFound,
  kNoDataFound,
  kNoTemplateFound,
  kUsrCategories,
  kSelectedDateShouldNotbe,
  BAD_CIBIL_SCORE_MSG,
  APPROVED_SALARY_IS_NOT_MATCH_WITH_AREA,
  COMPANY_BLACKLIST,
  kErrorMsgs,
  MIN_SCORE_MESS,
  COMPANY_STATUS_IS_NOT_ACTIVE,
  ALREADY_LOAN_ACTIVE,
  kYouHaveAccess,
  OnHoldNotification,
  OnHoldFVNotificationBody,
  kDashboardRoute,
  kSelectLoanAmountRoute,
} from 'src/constants/strings';
import { MasterEntity } from 'src/entities/master.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { BlackListCompanyRepository } from 'src/repositories/blacklistCompanies.repository';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { StateEligibilityRepository } from 'src/repositories/stateEligibility.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { TypeService } from 'src/utils/type.service';
import { EmiSharedService } from './emi.service';
import { SharedNotificationService } from './services/notification.service';
import { loanTransaction } from 'src/entities/loan.entity';
import { BlockUserHistoryRepository } from 'src/repositories/user.blockHistory.repository';
import { KYCEntity } from 'src/entities/kyc.entity';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { BankingRepository } from 'src/repositories/banking.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { PredictionService } from 'src/admin/eligibility/prediction.service';
import { BankingEntity } from 'src/entities/banking.entity';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import { MandateSharedService } from './mandate.service';
import { TemplateRepository } from 'src/repositories/template.repository';
import { employmentDetails } from 'src/entities/employment.entity';
import { AuthAiService } from 'src/thirdParty/authAi/authAi.service';
import { CommonSharedService } from './common.shared.service';
import { AdminRepository } from 'src/repositories/admin.repository';
import { RedisService } from 'src/redis/redis.service';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { PredictionRepository } from 'src/repositories/prediction.repository';
import { StaticConfigRepository } from 'src/repositories/static.config.repository';
import { EmploymentHistoryRepository } from 'src/repositories/employmentHistory.repository';
import { ReasonRepository } from 'src/repositories/reasons.repository';
import { APIService } from 'src/utils/api.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { AdminService } from 'src/admin/admin/admin.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ILoanEligibility } from './interfaces/loan.eligibility.interface';
import { IsUUID, isUUID } from 'class-validator';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { SequelOptions } from 'src/interfaces/include.options';
import { EmiEntity } from 'src/entities/emi.entity';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { AssignmentSharedService } from 'src/shared/assignment.service';
import { UserSelfieRepository } from 'src/repositories/user.selfie.repository';
import { EnvConfig } from 'src/configs/env.config';
import { KycServiceV4 } from 'src/v4/kyc/kyc.service.v4';
import {
  kVerificationAccessStatus,
  REASON_ID,
  REASON_REMARK,
  EXCLUDE_CSE_LOAN_REJECT_REASON,
  LOAN_STATUS,
  UserStage,
  ON_HOLD_DOCUMENT_REQUIREMENTS,
} from 'src/constants/objects';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { SubscriptionRepository } from 'src/repositories/subscription.repository';
import { ESignRepository } from 'src/repositories/esign.repository';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { NUMBERS } from 'src/constants/numbers';
import { ICibilEligibility } from 'src/interfaces/cibil.eligibility';
import { CAAssignmentService } from './assignment/caAssignCase.service';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { DateService } from 'src/utils/date.service';
import { nAdminBackend } from 'src/constants/network';
import { PredictionEntity } from 'src/entities/prediction.entity';
@Injectable()
export class EligibilitySharedService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly authAi: AuthAiService,
    private readonly blackListedCompanyRepo: BlackListCompanyRepository,
    @Inject(forwardRef(() => EmiSharedService))
    private readonly sharedEMI: EmiSharedService,
    private readonly empRepo: EmploymentRepository,
    private readonly loanRepo: LoanRepository,
    private readonly masterRepo: MasterRepository,
    private readonly stateRepo: StateEligibilityRepository,
    private readonly typeService: TypeService,
    private readonly userRepo: UserRepository,
    private readonly sharedNotificationService: SharedNotificationService,
    private readonly userBlockHistoryRepo: BlockUserHistoryRepository,
    private readonly salaryRepo: SalarySlipRepository,
    private readonly bankRepo: BankingRepository,
    private readonly workMailRepo: WorkMailRepository,
    private readonly predictionService: PredictionService,
    private readonly redisService: RedisService,
    private readonly razorpayService: RazorpoayService,
    private readonly mandateService: MandateSharedService,
    @Inject(forwardRef(() => AdminService))
    private readonly adminService: AdminService,
    private readonly predictionRepo: PredictionRepository,
    private readonly staticRepo: StaticConfigRepository,
    private readonly templateRepo: TemplateRepository,
    private readonly commonService: CommonSharedService,
    private readonly empHistoryRepo: EmploymentHistoryRepository,
    private readonly apiService: APIService,
    private readonly assignmentService: AssignmentSharedService,
    private readonly caAssignmentService: CAAssignmentService,
    // Database
    private readonly repoManager: RepositoryManager,
    // Repositories
    private readonly reasonRepo: ReasonRepository,
    private readonly kycRepo: KYCRepository,
    private readonly selfieRepo: UserSelfieRepository,
    // v3 services
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly kycService: KycServiceV4,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
    private readonly subscriptionRepo: SubscriptionRepository,
    private readonly eSignRepo: ESignRepository,
    private readonly disbursmentRepo: DisbursmentRepository,
    private readonly dateService: DateService,
  ) {}

  // Checks User's Eligibility
  async checkUserEligiblity(id) {
    try {
      if (EnvConfig.otherNBFCUrl.otherNbfcBaseUrl.length == 0) return true;
      const validUrls = (EnvConfig.otherNBFCUrl.otherNbfcBaseUrl ?? []).filter(
        (el) => el != undefined && el && el?.trim()?.length > 1,
      );
      if (validUrls.length == 0) return true;

      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = ['aadhaarNo'];
      let include = [kycInclude];
      const option = { include, where: { id } };
      const userData: any = await this.userRepo.getRowWhereData(
        ['phone'],
        option,
      );
      if (userData == k500Error) return kInternalError;
      const params = {
        number: userData?.phone,
        aadhaarNo: userData?.kycData?.aadhaarNo,
      };
      const url = EnvConfig.otherNBFCUrl.otherNbfcBaseUrl;
      for (let i = 0; i < url.length; i++) {
        const element = url[i];
        if (!element || element == undefined) continue;
        let URL = element + '/v4/user/checkUserHistory';
        const userEligiblityData = await this.apiService.get(URL, params);
        if (userEligiblityData?.message != 'SUCCESS') continue;
        var userCurrentEligibility = await this.blockUser(
          userEligiblityData?.data,
          id,
        );
        if (userCurrentEligibility == false) return userCurrentEligibility;
      }
      return userCurrentEligibility;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Blocks the User By Cooling Off and Blacklisting
  async blockUser(data, userId) {
    try {
      const coolOffData = data.coolOffData;
      const blacklistData = data.blacklistedData;
      const isLoanActive = data.loanData?.isLoanActive;
      const isLoanAccepted = data?.loanData?.isLoanAccepted;
      // CoolOff User
      const today = this.typeService.getGlobalDate(new Date());
      if (
        coolOffData?.isCoolOff &&
        coolOffData?.coolOffEndsOn &&
        coolOffData?.coolOffEndsOn > today.toJSON()
      ) {
        const coolOff: any = await this.adminService.changeBlacklistUser({
          userId,
          adminId: SYSTEM_ADMIN_ID,
          type: '2',
          reason: coolOffData?.reason,
          status: '0',
          nextApplyDate: coolOffData?.coolOffEndsOn,
          reasonId: coolOffData?.reasonId,
        });
        if (coolOff.message) return kInternalError;
      }
      //Blacklist User
      else if (blacklistData?.isBlacklisted) {
        const blackListAndRejectLoan: any =
          await this.adminService.changeBlacklistUser({
            userId,
            adminId: SYSTEM_ADMIN_ID,
            type: '1',
            reason: blacklistData?.reason ?? REASON_REMARK.USER_NOT_ELIGIBLE,
            status: '1',
            nextApplyDate: null,
            reasonId: blacklistData?.reasonId ?? REASON_ID.USER_NOT_ELIGIBLE,
          });
        if (blackListAndRejectLoan.message) return kInternalError;
      }
      // Blacklist User(When Already Loan Active in Different NBFC)
      else if (isLoanActive) {
        const blackListAndRejectLoan: any =
          await this.adminService.changeBlacklistUser({
            userId,
            adminId: SYSTEM_ADMIN_ID,
            type: '1',
            reason: ALREADY_LOAN_ACTIVE,
            status: '1',
            nextApplyDate: null,
            reasonId: 62,
          });
        if (blackListAndRejectLoan.message) return kInternalError;
      } else if (isLoanAccepted) {
        const blackListAndRejectLoan: any =
          await this.adminService.changeBlacklistUser({
            userId,
            adminId: SYSTEM_ADMIN_ID,
            type: '1',
            reason: REASON_REMARK.LOAN_ALREADY_ACCEPTED,
            status: '1',
            nextApplyDate: null,
            reasonId: REASON_ID.LOAN_ALREADY_ACCEPTED,
          });
        if (blackListAndRejectLoan.message) return kInternalError;
      } else return true;

      return false;
    } catch (error) {
      return kInternalError;
    }
  }

  async validateStateWiseEligibility(reqData) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    let verifiedSalary = null;
    if (reqData?.salary) verifiedSalary = reqData?.salary;

    // Get target user data
    const userData = await this.getDataForStateWiseEligiblity(reqData);
    if (userData?.message) return userData;
    const appType = userData?.appType;

    // Get state
    const kycData = userData?.kycData ?? {};
    const kycEligibilityDetails = kycData.eligibilityDetails ?? {};
    kycEligibilityDetails.stateCheckedOn = new Date().toJSON();
    let aadhaarState;
    if (kycData.aadhaarState) aadhaarState = kycData.aadhaarState;
    else if (kycData.aadhaarAddress) {
      if (typeof kycData.aadhaarAddress === 'string') {
        const aadhaarAddress = JSON.parse(kycData.aadhaarAddress);
        if (aadhaarAddress?.state) aadhaarState = aadhaarAddress.state;
      }
    }
    let lastState = userData?.state;
    if (!lastState) lastState = aadhaarState;
    if (!aadhaarState || !lastState) return kParamMissing('state');
    kycEligibilityDetails.aadhaarState = aadhaarState;
    kycEligibilityDetails.lastState = lastState;
    // SKIP STEP -> OLD DEFAULTER
    if ((userData?.isRedProfile ?? 0) === 2) return {};
    const loanData = userData?.masterData?.loanData;
    const bankingData = loanData?.bankingData;
    const status = userData?.masterData?.status;
    if (
      status.loan != 2 &&
      (bankingData?.salaryVerification == '1' ||
        bankingData?.salaryVerification == '3')
    )
      verifiedSalary = bankingData?.adminSalary ?? bankingData?.salary;
    let userEnteredSalary = userData?.masterData?.otherInfo?.salaryInfo;
    if (!userEnteredSalary)
      userEnteredSalary = userData.masterData?.empData?.salary;
    // code comment due to missing repeater proffetional salaryInfo
    // if (!userEnteredSalary) return k422ErrorMessage('salaryInfo not found');

    // Validate state wise eligibility
    const userSalary = verifiedSalary ?? userEnteredSalary;
    const stateEligible: any = await this.isEligibleAsPerState(
      userSalary,
      aadhaarState,
      lastState,
      userData.completedLoans > 0,
      appType,
    );
    if (stateEligible?.message) return stateEligible;
    const isEligible =
      !userSalary || userSalary == '0' ? true : stateEligible.isEligible;
    kycEligibilityDetails.salary = userSalary;
    kycEligibilityDetails.isEligible = isEligible;
    kycEligibilityDetails.checkVia = stateEligible?.checkVia;
    if (!userSalary || userSalary == '0')
      kycEligibilityDetails.logicBypass = true;

    // Update state selection
    const kycUpdatedData = { eligibilityDetails: kycEligibilityDetails };
    const kycUpdateResult = await this.repoManager.updateRowData(
      KYCEntity,
      kycUpdatedData,
      kycData.id,
      true,
    );
    if (kycUpdateResult === k500Error) throw new Error();

    if (isEligible) return {};

    const coolOffData = userData?.masterData?.coolOffData ?? {};
    const count = +(coolOffData.count ?? 0);
    // Update user data
    let targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 1);
    if (count == 0 || !count) targetDate.setDate(targetDate.getDate() + 30);
    else targetDate.setDate(targetDate.getDate() + 90);
    targetDate = this.typeService.getGlobalDate(targetDate);

    const updatedData: any = {};
    const remark = APPROVED_SALARY_IS_NOT_MACTCH_WITH_STATE_SALARY;
    if (status?.company == -1) {
      const toDay = this.typeService.getGlobalDate(new Date());
      coolOffData.count = count;
      coolOffData.coolOffStartedOn = toDay.toJSON();
      coolOffData.coolOffEndsOn = targetDate.toJSON();
      coolOffData.reason = remark;

      updatedData.status = status;
      updatedData.coolOffData = coolOffData;
    }
    const updateResult = await this.masterRepo.updateRowData(
      updatedData,
      userData.masterId,
    );
    if (updateResult === k500Error) return kInternalError;
    if (loanData?.id) {
      const adminId = reqData?.adminId ?? SYSTEM_ADMIN_ID;
      const loanRejectRes = await this.rejectLoan(
        adminId,
        loanData?.id,
        remark,
        userId,
        targetDate,
      );
      await this.checkAndRejectSteps(userId, remark);
      if (loanRejectRes === true) {
        // Create UserBlockHistory record
        await this.userBlockHistoryRepo.createRowData({
          reasonId: 73,
          reason: remark,
          isBlacklist: '0',
          coolOfDate: targetDate.toJSON(),
          userId,
          blockedBy: SYSTEM_ADMIN_ID,
        });
      }
    }

    return {};
  }

  private async getDataForStateWiseEligiblity(reqData) {
    let verifiedSalary = null;
    if (reqData?.salary) verifiedSalary = reqData?.salary;
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');

    // Validate user data
    const bankInclude: any = { model: BankingEntity };
    bankInclude.attributes = ['salary', 'salaryVerification', 'adminSalary'];
    bankInclude.required = false;
    const loanInclude: any = { model: loanTransaction };
    loanInclude.attributes = ['id'];
    const empInclude: any = { model: employmentDetails };
    empInclude.attributes = ['salary'];
    empInclude.required = false;
    if (!verifiedSalary) loanInclude.include = [bankInclude];
    loanInclude.required = false;
    const masterInclude: any = { model: MasterEntity };
    masterInclude.attributes = ['status', 'coolOffData', 'otherInfo'];
    masterInclude.include = [empInclude, loanInclude];
    const kycInclude: any = { model: KYCEntity };
    kycInclude.attributes = [
      'aadhaarAddress',
      'aadhaarState',
      'eligibilityDetails',
      'id',
    ];
    const include = [masterInclude, kycInclude];
    const attributes = [
      'completedLoans',
      'masterId',
      'state',
      'isRedProfile',
      'maybeGoodCibil',
      'appType',
    ];
    const options = { include, where: { id: userId } };
    const userData = await this.userRepo.getRowWhereData(attributes, options);
    if (userData === k500Error) throw Error();
    if (!userData) return k422ErrorMessage(kNoDataFound);

    return userData;
  }

  async isEligibleCompany(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const companyName = reqData.companyName;
      if (!companyName) return kParamMissing('companyName');
      const attributes = ['id', 'blockedBy'];
      const options = { where: { companyName } };
      const blackListedData = await this.blackListedCompanyRepo.getRowWhereData(
        attributes,
        options,
      );
      if (blackListedData == k500Error) return kInternalError;
      if (!blackListedData) return true;

      // Blacklist the user
      const data = {
        userId,
        reason: COMPANY_BLACKLIST,
        reasonId: 53,
        type: '1',
        status: '1',
        adminId: SYSTEM_ADMIN_ID,
      };
      if (!(blackListedData.blockedBy == 'SYSTEM')) {
        const userBloack = await this.adminService.changeBlacklistUser(data);
        if (userBloack == k500Error) return kInternalError;
      } else {
        let nextApplyDate = new Date();
        nextApplyDate.setDate(nextApplyDate.getDate() + 60);
        let coolOffData: any = {
          userId,
          type: '2',
          nextApplyDate,
          adminId: SYSTEM_ADMIN_ID,
          status: '0',
          reason: COMPANY_STATUS_IS_NOT_ACTIVE,
          reasonId: 55,
        };
        await this.adminService.changeBlacklistUser(coolOffData);
      }

      return false;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async isEligibleAsPerState(
    salary,
    aadhaarState,
    lastState,
    isRepeater,
    apptype,
  ) {
    try {
      if (!aadhaarState || !lastState) return kParamMissing('state');
      // SKIP_SALARY due to missing repeater proffetional salaryInfo
      if (!salary || salary == '0')
        return { isEligible: true, checkVia: 'SKIP_SALARY' };

      aadhaarState = aadhaarState.toLowerCase();
      lastState = lastState.toLowerCase();
      const stateName = [aadhaarState, lastState];
      const attr = [
        'stateName',
        'isActive',
        'eligibility_new',
        'eligibility_repeat',
      ];
      const options = { where: { stateName } };
      const stateData = await this.stateRepo.getTableWhereData(attr, options);
      if (stateData === k500Error) return kInternalError;
      if (!stateData)
        return {
          isEligible: salary >= GLOBAL_RANGES.MIN_SALARY_AMOUNT,
          checkVia: 'MIN SALARY',
        };

      const inactiveState = stateData.find((f) => f.isActive == '0');
      if (inactiveState)
        return { isEligible: false, checkVia: 'INACTIVE STATE' };
      let activeState = stateData.find(
        (f) => f.isActive == '1' && f.stateName == aadhaarState,
      );
      if (!activeState)
        activeState = stateData.find(
          (f) => f.isActive == '1' && f.stateName == lastState,
        );
      const minAmount = isRepeater
        ? (activeState?.eligibility_repeat ?? 0)
        : (activeState?.eligibility_new ?? 0);

      if (!GLOBAL_FLOW.SALARY_COOLOFF_BEFORE_STATEMENT && apptype == 0) {
        if (salary >= 10000 && salary < 20000) {
          return { isEligible: true, checkVia: 'STATE SALARY' };
        }
      }
      return { isEligible: salary >= minAmount, checkVia: 'STATE SALARY' };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Total #4 steps
  async checkLoanEligibility(reqData: ILoanEligibility, isPreApprovUser?) {
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;
    reqData.readOnly = isReadOnly;
    // #01 - Get valid loan data
    const loanData = await this.getLoanDataForEligibility(reqData);
    if (loanData?.message) return loanData;
    // #02 - Validate eligibility
    if (isPreApprovUser || reqData.isPreApprovUser)
      loanData.isPreApprovUser = isPreApprovUser ?? reqData.isPreApprovUser;
    let baseEligibility: any;
    let nbfc = EnvConfig.nbfc.nbfcType;
    if (nbfc == '0')
      baseEligibility = await this.nbfcFirstvalidateEligiblityForLoan(loanData);
    else if (nbfc == '1')
      baseEligibility =
        await this.nbfcSecondvalidateEligiblityForLoan(loanData);
    else {
      return 'NBFC is not found';
    }
    // #04 - Mark eligibility process as complete
    baseEligibility.isReadOnly = isReadOnly;
    if (!baseEligibility?.loanId || baseEligibility?.message) {
      console.log('ERROR baseEligibility', nbfc, baseEligibility);
    }
    return await this.markEligibilityAsComplete(baseEligibility);
  }

  // #01 - Get valid loan data
  private async getLoanDataForEligibility(
    reqData: ILoanEligibility,
  ): Promise<any> {
    // Params validation
    if (!reqData.loanId) return kParamMissing('loanId');
    if (!reqData.userId) return kParamMissing('userId');
    if (typeof reqData.loanId != 'number') return kInvalidParamValue('loanId');
    if (!isUUID(reqData.userId)) return kInvalidParamValue('userId');

    // Query preparation
    const loanAttr = ['completedLoan', 'loanStatus', 'userId'];
    const bankInclude: SequelOptions = {
      attributes: ['adminSalary', 'salary', 'otherDetails'],
      model: BankingEntity,
    };
    const predictionInclude = {
      model: PredictionEntity,
      attributes: ['CFLScore'],
    };
    const userInclude: SequelOptions = {
      model: registeredUsers,
      attributes: ['typeOfDevice', 'email'],
      include: [
        {
          model: KYCEntity,
          attributes: [
            'aadhaarDOB',
            'aadhaarState',
            'aadhaarAddress',
            'aadhaarAddressResponse',
          ],
        },
      ],
    };
    const masterInclude = {
      model: MasterEntity,
      attributes: ['loanId', 'pre_approve_amount'],
    };
    const include = [
      bankInclude,
      userInclude,
      predictionInclude,
      masterInclude,
    ];
    const loanOptions = { include, where: { id: reqData.loanId } };

    // Query
    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );

    // Validate query data
    if (!loanData) return k422ErrorMessage(kNoDataFound);
    if (loanData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    if (loanData.loanStatus == 'Rejected' && !reqData.readOnly)
      return k422ErrorMessage('Loan is rejected, try after sometime !');
    else if (loanData.loanStatus == 'Active' && !reqData.readOnly)
      return k422ErrorMessage('Loan is active, try after sometime !');
    else if (reqData.userId != loanData.userId)
      return kInvalidParamValue('userId');

    // Get cibil data
    // Query preparation
    const cibilAttr = [
      'cibilScore',
      'currentBalance',
      'highCreditAmount',
      'overdueAccounts',
      'overdueBalance',
      'plScore',
      'totalOverdueDays',
      'PLAccounts',
      'inquiryPast30Days',
      'inquiryPast12Months',
      'accounts',
      'PLOutstanding',
      'zeroBalanceAccounts',
      'past6MonthDelay',
      'currentBalance',
      'totalAccounts',
    ];
    const cibilOptions = {
      order: [['id', 'DESC']],
      where: { loanId: reqData.loanId, userId: reqData.userId },
    };

    // Query
    const cibilData = await this.repoManager.getRowWhereData(
      CibilScoreEntity,
      cibilAttr,
      cibilOptions,
    );

    // Validate query data
    if (cibilData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    else if (!cibilData) return k422ErrorMessage(kNoDataFound);

    const userData = loanData?.registeredUsers;
    let aadhaarState = userData?.kycData?.aadhaarState;
    if (!aadhaarState) {
      const aadhaarAddress = await this.typeService.getAadhaarAddress(
        userData?.kycData,
      );
      aadhaarState = aadhaarAddress?.state;
    }

    const aadhaarDOB = await this.typeService.getDateAsPerAadhaarDOB(
      userData?.kycData?.aadhaarDOB,
    );
    const today = this.typeService.getGlobalDate(new Date());
    const age = this.typeService.dateDifference(aadhaarDOB, today, 'Years');

    // const experianData = await this.repoManager.getRowWhereData(
    //   ExperianScoreEntity,
    //   ['id', 'overdueAccounts'],
    //   { where: { userId: reqData.userId }, order: [['id', 'DESC']] },
    // );
    // if (experianData == k500Error) throw new Error();

    return {
      ...cibilData,
      loanId: reqData.loanId,
      userId: reqData.userId,
      completedLoans: loanData.completedLoan ?? 0,
      salary: loanData?.bankingData?.salary ?? reqData?.approvedSalary,
      typeOfDevice: userData?.typeOfDevice,
      email: userData?.email,
      ecsDetails: loanData?.bankingData?.otherDetails?.ecsDetails,
      CFLScore: loanData?.predictionData?.CFLScore,
      aadhaarState,
      age,
      pre_approve_amount: loanData?.masterData?.pre_approve_amount,
      // experianData,
    };
  }

  // #04 - Mark eligibility process as complete (either offer the loan or reject the process)
  async markEligibilityAsComplete(baseEligibility, isQaTest = false) {
    if (baseEligibility?.isReadOnly) return baseEligibility;

    // Offer the loan
    if (baseEligibility.isEligible === true) {
      baseEligibility.calculatedOn = new Date().toJSON();
      // Calculate charges, emi and insurance
      return await this.sharedEMI.refreshCalculation(baseEligibility.loanId, {
        netApprovedAmount: baseEligibility.eligibleAmount,
        interestRate: baseEligibility?.interestRate,
        eligibilityDetails: baseEligibility,
        isQaTest,
      });
    }
    // Decline the loan process for not eligible
    else {
      const remark = BAD_CIBIL_SCORE_MSG ?? MIN_SCORE_MESS;
      // Update loan data for eligiblity traces
      if (!isNaN(+baseEligibility.loanId)) {
        const updatedData = { eligibilityDetails: baseEligibility };
        const updateResponse = await this.repoManager.updateRowData(
          loanTransaction,
          updatedData,
          +baseEligibility.loanId,
        );
        if (updateResponse === k500Error) throw new Error();
      }

      const nextApplyDate = new Date();
      nextApplyDate.setDate(
        nextApplyDate.getDate() +
          GLOBAL_RANGES.LOAN_REJECTION_WITH_LOW_SCORE_COOL_OFF,
      );
      const loanRejectRes = await this.rejectLoan(
        SYSTEM_ADMIN_ID,
        baseEligibility.loanId,
        remark,
        baseEligibility.userId,
        nextApplyDate,
      );
      await this.checkAndRejectSteps(baseEligibility?.userId, remark);
      if (loanRejectRes === true) {
        //manage history
        const historyData = {
          reasonId: BAD_CIBIL_SCORE_REASON_ID,
          userId: baseEligibility.userId,
          reason: remark,
          isBlacklist: '0',
          blockedBy: SYSTEM_ADMIN_ID,
          coolOfDate: nextApplyDate?.toJSON(),
        };
        await this.userBlockHistoryRepo.createRowData(historyData);
      }
      return { isEligible: false };
    }
  }

  private async lastLoanStatus(userId) {
    if (!userId) return kParamMissing('userId');
    if (!IsUUID(userId)) return kInvalidParamValue('userId');

    // Query preparation
    const emiInclude: SequelOptions = { model: EmiEntity };
    emiInclude.attributes = ['penalty_days'];
    const include = [emiInclude];
    const loanAttr = [
      'id',
      'loanAmount',
      'netApprovedAmount',
      'eligibilityDetails',
    ];
    const loanOptions = {
      include,
      order: [['id', 'DESC']],
      where: { loanStatus: 'Complete', userId },
    };
    // Query data
    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    if (!loanData) return 'LOAN_NOT_TAKEN';
    let status: any;
    const emiList = loanData.emiData ?? [];
    const delayDays = emiList.reduce(
      (prev, curr) => prev + (curr.penalty_days ?? 0),
      0,
    );
    if (delayDays > 0) status = 'LOAN_DELAYED';
    else status = 'LOAN_ON_TIME';
    return {
      status,
      loanAmount: +(loanData?.loanAmount ?? 0),
      previousPortion: loanData?.eligibilityDetails?.incPortion,
      annum:
        loanData?.eligibilityDetails?.anummInterest ??
        loanData?.eligibilityDetails?.anummIntrest,
      delayDays,
    };
  }

  // Pass emi date only if eligible user wants to change the emi date
  async getAmountRange(loanId, statusData) {
    const attributes = ['loanAmount', 'loanStatus'];
    const options = { where: { id: loanId } };
    const loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (loanData == k500Error) return kInternalError;
    if (!loanData) return k422ErrorMessage(kNoDataFound);
    if (loanData?.loanStatus == 'Accepted' && statusData?.loan != -1) {
      return k422ErrorMessage('Loan amount can not change after acceptance');
    }
    if (loanData?.loanStatus == 'Accepted' && statusData?.loan == -1) {
      const updateRes = await this.loanRepo.updateRowData(
        { loanStatus: 'InProcess' },
        loanId,
      );
      if (updateRes === k500Error) return kInternalError;
    }
    if (loanData.loanStatus == 'Complete')
      return k422ErrorMessage('Loan already completed');
    if (loanData.loanStatus == 'Active')
      return k422ErrorMessage('Loan already active');
    if (loanData.loanStatus == 'Rejected')
      return k422ErrorMessage('Loan amount can not change after rejection');

    // Pass salary date only if user wants to change the salary date
    const calculation = await this.sharedEMI.refreshCalculation(loanId, {});
    if (calculation.message) return calculation;

    const loanApproval: any = {
      isEligible: true,
      approvedAmount: +(
        calculation?.approvedLoanAmount ?? calculation.loanAmount
      ),
      emiDays: calculation.emiDays,
      emiDates: calculation.emiDates.map((el) => new Date(el).toJSON()),
      totalDays: calculation.totalDays,
      interestRate: +calculation.interestRate,
      minAmount: GLOBAL_RANGES.MIN_LOAN_AMOUNT,
      stampFees: calculation.stampFees,
      delayInterestRate: calculation?.delayInterestRate,
      canSelectEmiDate: calculation.canSelectEmiDate ?? false,
      eligibleEmiDates: calculation.eligibleEmiDates ?? [],
      emiSelectedDate: calculation.emiSelectedDate,
      forcefullEmiSelection: false,
      calenderData: { month: null, year: null },
      // Dynamic insurance flow for user
      processingFeesWithInsurance:
        GLOBAL_CHARGES.WITH_INSURANCE_PROCESSING_FEES,
      processingFeesWithoutInsurance:
        GLOBAL_CHARGES.WITHOUT_INSURANCE_PROCESSING_FEES,
      insuranceOptValue: GlobalServices.INSURANCE_OPT_VALUE ?? false,
      chargesWithInsurance: calculation.chargesWithInsurance ?? {},
      chargesWithoutInsurance: calculation.chargesWithoutInsurance ?? {},
    };
    // Get month and year for calender selection in frontend (App)
    const emiSelectedDate =
      loanApproval.emiSelectedDate ?? calculation.salaryDate;
    if (emiSelectedDate) {
      const today = new Date();
      const currentMonth = today.getMonth();
      const emiDate = new Date();
      emiDate.setDate(emiSelectedDate);
      // EMI date falls this month
      if (emiDate.getMonth() == currentMonth) {
        // Date is in past
        if (today.getDate() >= emiDate.getDate()) {
          emiDate.setMonth(emiDate.getMonth() + 1);
        } else {
          const diffInDays = this.typeService.dateDifference(emiDate, today);
          // EMI should start from minimum 13 days of gap from today
          if (diffInDays <= 11) {
            emiDate.setMonth(emiDate.getMonth() + 1);
          }
        }
      }
      loanApproval.calenderData.month = emiDate.getMonth();
      loanApproval.calenderData.year = emiDate.getFullYear();
    }
    // Default emi date
    if (!emiSelectedDate) loanApproval.emiSelectedDate = calculation.salaryDate;
    if (
      GlobalServices.INSURANCE_SERVICE &&
      GlobalServices.INSURANCE_SERVICE != 'NONE'
    ) {
      loanApproval.insurance = calculation.insurance;
      loanApproval.isInsurance = true;
    }

    return loanApproval;
  }

  async rejectLoan(
    adminId: number,
    loanId: number,
    remark: string,
    userId: string,
    nextApplyDate?,
    declineId = null,
    isForcefully = false,
    isBlacklist = false,
    withOutUserStuck = false,
  ) {
    try {
      const loanInclude = {
        model: loanTransaction,
        attributes: ['id', 'loanStatus'],
      };
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fcmToken', 'phone', 'stage'],
      };
      const masterData = await this.masterRepo.getRowWhereData(
        ['id', 'status', 'dates', 'rejection', 'coolOffData'],
        { where: { loanId }, include: [loanInclude, userInclude] },
      );

      if (!masterData || masterData == k500Error) return kInternalError;

      ///need to check if loan is rejected due to user in activity reason then no need to delete it
      if (
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
        remark &&
        adminId &&
        adminId != SYSTEM_ADMIN_ID &&
        EXCLUDE_CSE_LOAN_REJECT_REASON.includes(
          remark?.toLowerCase()?.trim(),
        ) &&
        (masterData?.loanData?.loanStatus == LOAN_STATUS.Accepted ||
          (masterData?.loanData?.loanStatus == LOAN_STATUS.InProcess &&
            masterData?.userData?.stage == UserStage.LOAN_ACCEPT))
      ) {
        ///update loan data
        await this.loanRepo.updateWhere(
          { cseRejectBy: adminId, remark },
          loanId,
          {},
        );

        //set data in redis for cse list cool-off data
        this.redisService.set(
          'CSE_REJECT_LOAN' + loanId,
          JSON.stringify({ nextApplyDate, adminId, remark }),
          NUMBERS.FIVE_DAYS_IN_SECONDS,
        );
        return true;
      }

      const loanData = masterData.loanData;
      const userData = masterData.userData;
      const statusData = masterData?.status ?? {};
      const dates = masterData?.dates ?? {};
      const rejection = masterData?.rejection ?? {};
      const coolOffData = masterData?.coolOffData ?? {};
      if (
        loanData.loanStatus === 'Active' ||
        loanData.loanStatus === 'Complete'
      )
        return kInternalError;

      const toDay = this.typeService.getGlobalDate(new Date());
      if (nextApplyDate)
        nextApplyDate = this.typeService.getGlobalDate(nextApplyDate);
      else {
        nextApplyDate = this.typeService.getGlobalDate(new Date());
        nextApplyDate.setDate(nextApplyDate.getDate() + 1);
      }
      if (nextApplyDate <= toDay)
        return k422ErrorMessage(kSelectedDateShouldNotbe);
      const loanUpdatedData: any = {
        loanStatus: 'Rejected',
        remark,
        lastStage: (userData?.stage ?? '').toString(),
      };
      if (declineId) loanUpdatedData.declineId = declineId;
      loanUpdatedData.manualVerification = '2';
      statusData.eligibility = 2;
      statusData.loan = 2;

      let loanAcceptStatus = masterData?.loanData?.loanAcceptStatus;
      if (userData?.stage == UserStage.LOAN_ACCEPT) {
        loanAcceptStatus = 2;
        rejection.loanAccept = remark;
        dates.loanAccept = new Date().getTime();
      } else if (userData?.stage === UserStage.MANDATE) {
        statusData.eMandate = 2;
        rejection.eMandate = remark;
        dates.eMandate = new Date().getTime();
      } else if (userData?.stage === UserStage.ESIGN) {
        statusData.eSign = 2;
        rejection.eSign = remark;
        dates.eSign = new Date().getTime();
      } else if (userData?.stage === UserStage.DISBURSEMENT) {
        statusData.disbursement = 2;
        rejection.disbursement = remark;
        dates.disbursement = new Date().getTime();
      } else if (
        userData?.stage === UserStage.BANKING ||
        userData?.stage === UserStage.ON_HOLD
      ) {
        if (userData?.stage === UserStage.ON_HOLD) {
          if (statusData.bank == 8) {
            statusData.bank = 2;
            rejection.banking = remark;
            dates.banking = new Date().getTime();
          }
        } else {
          statusData.bank = 2;
          rejection.banking = remark;
          dates.banking = new Date().getTime();
        }
      } else if (userData?.stage === UserStage.PAN) {
        statusData.pan = 2;
        rejection.pan = remark;
        dates.pan = new Date().getTime();
      }

      if (withOutUserStuck && statusData?.disbursement === 0)
        statusData.disbursement = 2;
      loanUpdatedData.verifiedDate = toDay.toJSON();
      loanUpdatedData.loanRejectDateTime = new Date().toJSON();
      if (userData?.stage === UserStage.FINAL_VERIFICATION)
        dates.eligibility = new Date().getTime();
      dates.loan = new Date().getTime();
      rejection.loan = remark;
      if (remark == BAD_CIBIL_SCORE_MSG) loanUpdatedData.cibilSystemFlag = 2;
      if (adminId != -1) loanUpdatedData.manualVerificationAcceptId = adminId;
      if (adminId == SYSTEM_ADMIN_ID) loanUpdatedData.cseRejectBy = null;
      const where = { esign_id: { [Op.eq]: null } };
      if (isForcefully) delete where.esign_id;
      const loanUpdate = await this.loanRepo.updateWhere(
        loanUpdatedData,
        loanId,
        where,
      );
      await this.loanRepo.updateRowWhereData(
        {
          loanStatus: 'Rejected',
          manualVerification: '2',
          remark,
          loanRejectDateTime: new Date().toJSON(),
        },
        {
          where: {
            userId,
            id: { [Op.ne]: loanId },
            loanStatus: { [Op.notIn]: ['Complete', 'Rejected', 'Active'] },
          },
        },
      );
      if (loanUpdate == k500Error) return kInternalError;
      else if (loanUpdate[0] == 0)
        return k422ErrorMessage('Loan can not rejected!');

      // Update user data

      coolOffData.count = (coolOffData?.count ?? 0) + 1;
      coolOffData.coolOffStartedOn = toDay.toJSON();
      coolOffData.coolOffEndsOn = nextApplyDate.toJSON();
      coolOffData.reason = remark;

      let userUpdatedData: any = { NextDateForApply: nextApplyDate };
      const masterUpdatedData: any = {
        status: statusData,
        dates,
        rejection,
        loanAcceptStatus,
      };
      if (isBlacklist == true) userUpdatedData = { isBlacklist: '1' };
      else masterUpdatedData.coolOffData = coolOffData;
      if (withOutUserStuck) {
        delete masterUpdatedData?.coolOffData;
      } else {
        const userUpdate = await this.userRepo.updateRowData(
          userUpdatedData,
          userId,
        );
        if (userUpdate == k500Error) return kInternalError;
      }

      // Only rejects particular loan with dates
      const updateMaster = await this.masterRepo.updateRowData(
        masterUpdatedData,
        masterData.id,
      );
      if (updateMaster === k500Error) return kInternalError;

      // Rejects all other loans for that user
      await this.checkAndRejectPreviousMaster(userId, loanId);

      // Refresh user stage
      await this.userService.routeDetails({ id: userId });
      await this.sharedNotificationService.sendNotificationToUser({
        userList: [userId],
        title: kLoanDeclined,
        content: kInternalPolicy,
        data: {
          currentRoute: kSelectLoanAmountRoute,
          nextRoute: kDashboardRoute,
        },
      });

      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async checkAndRejectPreviousMaster(userId: string, loanId: number) {
    try {
      const masterData = await this.masterRepo.getTableWhereData(
        ['id', 'status'],
        {
          where: { userId, loanId: { [Op.ne]: loanId } },
          order: [['id', 'desc']],
        },
      );

      if (!masterData || masterData == k500Error) return kInternalError;
      if (masterData.length === 0) return true;

      const inProcessLoanStatus = [0, 1, 3];
      const filterMasters = masterData.filter((item) =>
        inProcessLoanStatus.includes(item?.status?.loan),
      );

      if (filterMasters.length === 0) return true;

      for (let i = 0; i < filterMasters.length; i++) {
        const item = filterMasters[i];
        if (!item?.id || !item.status) continue;
        const masterUpdatedData: any = { status: item.status };
        masterUpdatedData.status.loan = 2;
        masterUpdatedData.status.eligibility = 2;
        await this.masterRepo.updateRowData(masterUpdatedData, item.id);
      }

      return true;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async checkAndRejectSteps(userId, reason, isFromBank = false) {
    try {
      const loanInclude = {
        attributes: [
          'id',
          'loanStatus',
          'bankingId',
          'subscriptionId',
          'esign_id',
          'loan_disbursement_id',
        ],
        model: loanTransaction,
      };
      const masterInclude = {
        model: MasterEntity,
        attributes: [
          'id',
          'rejection',
          'dates',
          'coolOffData',
          'status',
          'workMailId',
          'salarySlipId',
          'empId',
        ],
        include: [loanInclude],
      };
      const options = {
        where: { id: userId },
        include: [masterInclude],
      };
      const attributes = [
        'id',
        'homeStatus',
        'quantity_status',
        'kycId',
        'selfieId',
      ];
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (!userData && userData == k500Error) return kInternalError;
      //step rejection
      return await this.stapUpdate(userData, reason, isFromBank);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async stapUpdate(userData, rejectReason, isFromBank = false) {
    try {
      const masterData = userData.masterData;
      const loanData = masterData?.loanData;
      const workMailId = masterData?.workMailId;
      const salarySlipId = masterData?.salarySlipId;
      const empId = masterData?.empId;
      const bankId = loanData?.bankingId;
      const kycId = userData?.kycId;
      const userId = userData?.id;
      const selfieId = userData?.selfieId;
      const subscriptionId = loanData?.subscriptionId;
      const esignId = loanData?.esign_id;
      const disbursementId = loanData?.loan_disbursement_id;
      const statusData = masterData.status;
      const rejection = masterData.rejection ?? {};
      const approvedStatus = [1, 2, 3, 4, 7, 8];
      const status = '2';
      if (!approvedStatus.includes(statusData.company)) {
        if (empId) {
          const update = { companyVerification: status, rejectReason };
          rejection.company = rejectReason;
          statusData.company = +status;
          await this.empRepo.updateRowData(update, empId);
        } else rejection.aadhaar = rejectReason;
      } else if (!approvedStatus.includes(statusData.salarySlip)) {
        const update = { status, rejectReason };
        rejection.salarySlip = rejectReason;
        statusData.salarySlip = +status;
        await this.salaryRepo.updateRowData(update, salarySlipId);
      } else if (!approvedStatus.includes(statusData.workMail)) {
        const update = { status, rejectReason };
        rejection.workMail = rejectReason;
        statusData.workMail = +status;
        await this.workMailRepo.updateRowData(update, workMailId);
      } else if (![1, 3].includes(statusData.bank)) {
        const update = { salaryVerification: status, rejectReason };
        rejection.banking = rejectReason;
        statusData.bank = +status;
        if (bankId) await this.bankRepo.update(update, bankId);
      } else if (!approvedStatus.includes(statusData.pan) && !isFromBank) {
        const update = { panStatus: status, panRejectReason: rejectReason };
        rejection.pan = rejectReason;
        statusData.pan = +status;
        await this.kycRepo.updateRowData(update, kycId);
        //delete kyc data from redis
        await this.redisService.del(`KYC_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);
      } else if (!approvedStatus.includes(statusData.selfie) && !isFromBank) {
        const update = { status, rejectReason };
        statusData.selfie = +status;
        rejection.selfie = rejectReason;
        await this.selfieRepo.updateRowData(update, selfieId);
        await this.redisService.del(`SELFIE_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);
      } else if (
        !approvedStatus.includes(statusData.eMandate) &&
        subscriptionId
      ) {
        const update = { status: 'FAILED' };
        statusData.eMandate = +status;
        rejection.eMandate = rejectReason;
        await this.subscriptionRepo.updateRowData(update, subscriptionId);
      } else if (!approvedStatus.includes(statusData.eSign) && esignId) {
        const update = { status };
        statusData.eSign = +status;
        rejection.eSign = rejectReason;
        await this.eSignRepo.updateRowData(update, esignId);
      } else if (
        !approvedStatus.includes(statusData.disbursement) &&
        disbursementId
      ) {
        const update = { status: 'failed', failure_reason: rejectReason };
        statusData.disbursement = +status;
        rejection.disbursement = rejectReason;
        await this.disbursmentRepo.updateRowData(update, disbursementId);
      }
      await this.masterRepo.updateRowData(
        { status: statusData, rejection },
        masterData.id,
      );

      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region -> Final approval
  // Total -> #05 steps
  async finalApproval(reqData) {
    // Params validation
    const adminId = reqData?.adminId;
    const approvedReason = reqData?.approvedReason;
    const isPreApprovalProcess = reqData?.isPreApprovalProcess ?? false;
    if (reqData?.status == 3 && !approvedReason)
      return kParamMissing('approvedReason');
    const status = +reqData.status;
    const reqFromUser = adminId == undefined || adminId == null;
    if (status == 8) {
      if (!reqData.holdReason) return kParamMissing('holdReason');
      if (reqData?.holdReason) {
        const hold = ON_HOLD_DOCUMENT_REQUIREMENTS[reqData?.holdReason];
        if (hold?.includesTransaction && !reqData?.transactionJson)
          return k422ErrorMessage('Please select transactions to proceed');
      }
    }

    // #01 -> Get target loan data
    const masterData = await this.getDataForFinalApproval(reqData);
    if (masterData?.message) return masterData;
    const bankData = masterData?.loanData?.bankingData;
    // final approval is not approved, whose approved salary verfication (checker maker)
    if (!reqFromUser) {
      const allAdminData = await this.caAssignmentService.getCAData();
      if (allAdminData?.message) return kInternalError;
      const adminData = allAdminData.find((ele) => ele.id == adminId);
      if (
        (bankData?.adminId != SYSTEM_ADMIN_ID &&
          bankData?.adminId == adminId) ||
        (adminData && !adminData?.verificationAccessStatus?.final)
      )
        return k422ErrorMessage(kYouHaveAccess);
    }
    let statusData = masterData?.status;
    const approvedStatus = [1, 3, 4];
    if (
      !approvedStatus.includes(statusData?.bank) &&
      !(
        approvedStatus.includes(statusData?.workEmail) &&
        approvedStatus.includes(statusData?.salarySlip)
      )
    )
      return k422ErrorMessage(kNoDataFound);
    if (
      statusData.selfie != 1 &&
      statusData.selfie != 3 &&
      statusData.selfie != 2
    ) {
      // check selfie
      const selfieStatus: any =
        await this.commonService.validateWithAadhareImage(
          reqData.userId,
          statusData,
        );
      if (selfieStatus.message) return selfieStatus;
      statusData.selfie = selfieStatus;
      await this.masterRepo.updateRowData(
        { status: statusData },
        masterData?.id,
      );

      if (selfieStatus != 1) {
        await this.caAssignmentService.assignCA(
          kVerificationAccessStatus.selfie,
          masterData.loanId,
        );
        return { needUserInfo: true };
      }
    } else if (statusData.selfie == 2) return { needUserInfo: true };

    if (statusData.pan == 6) return { needUserInfo: true };

    // Need to check PAN at this stage for new users
    if (statusData.pan != 1 && statusData.pan != 3) {
      const panResponse: any = await this.kycService.validatePan({
        userId: reqData.userId,
      });
      if (panResponse?.message) return panResponse;
      if (panResponse.statusData) {
        statusData = panResponse.statusData;
        masterData.status = panResponse.statusData;
      }
      if (statusData.pan != 1) {
        await this.caAssignmentService.assignCA(
          kVerificationAccessStatus.kyc,
          masterData.loanId,
        );
        return { needUserInfo: true };
      }
    }
    //get isManual for move final approval
    const eligibilityDetails = masterData?.loanData?.eligibilityDetails;
    let isManual = eligibilityDetails?.isManual ?? true;

    // #02 -> Check automation for user
    let finalApproval = false;
    if (reqFromUser === true && !isPreApprovalProcess) {
      const predictionData = await this.predictionService.predictApproval({
        ...reqData,
        masterData,
        eligibilityDetails,
      });
      if (predictionData.finalApproval === true) finalApproval = true;
    }

    // comment for user go in fv

    // // Instant Cash Flow By pass
    // let fetchDate = new Date(monthlySalaryDetails?.fetchDate);
    // const dateDiff = this.typeService.dateDifference(
    //   fetchDate,
    //   new Date(),
    //   'Days',
    // );
    // const isExpiring = dateDiff > 7;
    // if (monthlySalaryDetails?.isEligible == true && !isExpiring) {
    //   isManual = false;
    //   finalApproval = true;
    // }

    if (reqData?.forceContinue) {
      isManual = false;
      finalApproval = true;
    }

    // #03 -> Proceed for loan approval
    if (
      status === 3 ||
      (finalApproval === true && isManual == false) ||
      isPreApprovalProcess
    ) {
      const fApproval = await this.proceedForApproval({
        ...reqData,
        masterData,
        status: status ?? 1,
        eligibilityDetails,
      });
      if (fApproval?.message) return fApproval;
      await this.redisService.set(
        `CRM_WAIT_LOAN_${masterData.loanId}`,
        JSON.stringify(masterData.loanId),
        NUMBERS.TEN_MINUTES_IN_SECONDS,
      );
    }

    // #04 -> Proceed for loan rejection
    else if (status === 2) {
      const rejectionResult = await this.rejectLoanFromAdmin(reqData);
      if (rejectionResult?.message) return rejectionResult;
    } else if (status === 8) {
      await this.holdFinalVerification(masterData, reqData);
    }

    // #05 -> Proceed for loan updation for manual verification
    else if (
      (finalApproval === false && reqFromUser === true) ||
      isManual == true
    ) {
      const updateResult = await this.proceedForManualVerification({
        ...reqData,
        masterData,
      });

      if (updateResult?.message) return updateResult;
    }

    return { needUserInfo: true };
  }

  // #01 -> Final approval -> Get data
  private async getDataForFinalApproval(reqData) {
    // Params validation
    const loanId = reqData.loanId;
    if (!loanId) return kParamMissing('loanId');
    if (isNaN(+loanId)) return kInvalidParamValue('loanId');
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    if (!IsUUID(userId)) return kInvalidParamValue('userId');

    // Query preparation
    const bankInclude: SequelOptions = { model: BankingEntity };
    bankInclude.attributes = [
      'disbursementBank',
      'mandateAccount',
      'mandateIFSC',
      'adminSalary',
      'salary',
      'adminId',
    ];
    const loanInclude: SequelOptions = { model: loanTransaction };
    const userInclude: SequelOptions = {
      model: registeredUsers,
      attributes: [
        'id',
        'fullName',
        'fcmToken',
        'completedLoans',
        'monthlySalaryDetails',
      ],
      // Prevention
      where: { isBlacklist: { [Op.ne]: '1' } },
    };
    loanInclude.attributes = [
      'completedLoan',
      'eligibilityDetails',
      'id',
      'userId',
      'loan_disbursement_id',
      'netApprovedAmount',
      'assignTo',
    ];
    const cibiInlude = {
      model: CibilScoreEntity,
      attributes: [
        'cibilScore',
        'overdueBalance',
        'plScore',
        'totalOverdueDays',
        'PLAccounts',
        'inquiryPast30Days',
        'inquiryPast12Months',
      ],
    };
    loanInclude.include = [bankInclude, cibiInlude];
    loanInclude.where = { loanStatus: 'InProcess' };
    const include = [loanInclude, userInclude];
    const attributes = ['dates', 'id', 'loanId', 'otherInfo', 'status'];
    const options = {
      include,
      where: { loanId },
      order: [['id', 'desc']],
    };
    // Query
    const masterData = await this.repoManager.getRowWhereData(
      MasterEntity,
      attributes,
      options,
    );

    // Query validation
    if (masterData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    if (masterData?.loanData?.loan_disbursement_id)
      return k422ErrorMessage('Loan disbursement is in progress.');

    return masterData;
  }

  // #02 -> Approve loan application
  private async proceedForApproval(reqData) {
    // Params preparation
    const masterData = reqData.masterData ?? {};
    const userData = masterData.userData;
    const loanData = masterData.loanData ?? {};
    const bankingData = loanData.bankingData ?? {};
    const userId = reqData.userId;
    const approvedReason = reqData?.approvedReason;
    const adminId = reqData.adminId ?? SYSTEM_ADMIN_ID;
    const manualVerification = adminId == SYSTEM_ADMIN_ID ? '1' : '3';
    const manualVerificationAcceptId = adminId;
    const statusData = masterData.status;
    const dates = masterData.dates;
    dates.eligibility = new Date().getTime();
    dates.loan = new Date().getTime();
    const disbBank = bankingData?.disbursementBank;
    const netApprovedAmount = !isNaN(loanData?.netApprovedAmount)
      ? parseInt(loanData?.netApprovedAmount)
      : null;
    // IFSC validation
    const ifscCode = bankingData.mandateIFSC ?? '';
    const ifscDetails = await this.razorpayService.getIFSCDetails({
      ifsc: ifscCode,
    });
    if (ifscDetails.message) return ifscDetails;

    // Bank validation
    // i.e. DEAUCHE BANK, etc which is not available for disbursement
    if (manualBanksList.includes(disbBank) && adminId != SYSTEM_ADMIN_ID)
      return k422ErrorMessage(`Can not proceed with: ${disbBank}`);

    let updatedData: any = {};
    updatedData = {
      manualVerification,
      manualVerificationAcceptId,
      verifiedDate: this.typeService.getGlobalDate(new Date()).toJSON(),
    };
    if (approvedReason) updatedData.approvedReason = approvedReason;

    // Check previous mandate
    const mandateData = await this.mandateService.checkExistingStatus(
      null,
      null,
      bankingData.mandateAccount,
      userId,
      netApprovedAmount > 50000 ? netApprovedAmount : null,
    );
    if (mandateData?.message) return mandateData;

    const statuses = [
      'ACTIVE',
      'BANK_APPROVAL_PENDING',
      'ON_HOLD',
      'Registered',
    ];
    if (
      mandateData &&
      statuses.includes(mandateData.status) &&
      !loanData.subscriptionId
    ) {
      updatedData.subscriptionId = mandateData.id;
      statusData.eMandate = 1;
      if (mandateData.createdAt)
        dates.eMandate = mandateData.createdAt.getTime();
    }

    if (reqData?.isPreApprovalProcess) {
      const eligibilityDetails: any = reqData?.eligibilityDetails;
      eligibilityDetails.isPreApprovalUser = true;
      updatedData.eligibilityDetails = eligibilityDetails;
    }

    // Update loan record
    let updateResponse = await this.repoManager.updateRowData(
      loanTransaction,
      updatedData,
      loanData.id,
    );
    if (updateResponse === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    // Update master record
    statusData.loan = +manualVerification;
    statusData.eligibility = +manualVerification;
    updateResponse = await this.repoManager.updateRowData(
      MasterEntity,
      { status: statusData, dates },
      masterData.id,
    );

    if (updateResponse === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    // Push notification
    const fcmKey = userData.fcmToken;
    const title = kFinalLoanAcceptTitle;
    const body = kFinalLoanAcceptBody;
    this.sharedNotificationService.sendPushNotification(
      fcmKey,
      title,
      body,
      {},
      true,
      adminId,
    );

    return {};
  }

  // #04 -> decline loan application
  async rejectLoanFromAdmin(body): Promise<any> {
    // Params prepare and validation
    const remark = body.remark;
    const adminId = body.adminId;
    const loanId = body?.loanId;
    const userId = body?.userId;
    const nextDateForApply = body?.nextDateForApply;
    const declineId = body.declineId;
    if (!adminId) return kParamMissing('adminId');
    if (!loanId) return kParamMissing('loanId');

    const masterData = await this.getLoanData(body);
    if (!masterData || masterData.message)
      return k422ErrorMessage('Loan data not found!');
    else if (masterData) {
      const loanData = masterData.loanData;
      if (loanData?.loan_disbursement_id)
        return k422ErrorMessage('Disbursement initiated!');
      const loanRejectRes: any = await this.rejectLoan(
        adminId,
        loanId,
        remark,
        userId,
        nextDateForApply,
        declineId,
        false,
      );
      if (loanRejectRes?.message) return loanRejectRes;
      if (loanRejectRes === true) {
        const historyData = {
          userId,
          reason: remark,
          isBlacklist: '0',
          blockedBy: adminId,
          coolOfDate: nextDateForApply
            ? new Date(nextDateForApply).toJSON()
            : nextDateForApply,
        };
        await this.userBlockHistoryRepo.createRowData(historyData);
      }
      const reasonData: any = await this.reasonRepo.getRowWhereData(
        ['id', 'reason'],
        { where: { id: declineId } },
      );
      const reason = reasonData?.reason;
      return await this.checkAndRejectSteps(userId, reason);
    }
    return true;
  }

  // #05 -> set user to on hold
  async holdFinalVerification(masterData, body) {
    const userId = body?.userId;
    const statusData = masterData?.status ?? {};
    const dates = masterData?.dates ?? {};
    const loanData = masterData?.loanData;
    const hold_reason = body.holdReason;
    const transactionJson = body?.transactionJson;

    statusData.loan = +body.status;
    statusData.eligibility = +body.status;
    dates.loan = new Date().getTime();
    dates.eligibility = new Date().getTime();

    const updatedData = { status: statusData, dates };
    const update = await this.repoManager.updateRowData(
      MasterEntity,
      updatedData,
      masterData?.id,
    );
    if (update == k500Error) throw new Error();

    const loanUpdateData: any = {
      hold_stage: ON_HOLD_DOCUMENT_REQUIREMENTS[hold_reason] ? 3 : 4,
      hold_reason,
      hold_by: body?.adminId,
      hold_timestamp: new Date(),
      manualVerification: body.status.toString(),
    };
    if (transactionJson) {
      loanUpdateData.hold_other_details = JSON.stringify(transactionJson);
    }
    const loanUpdate = await this.repoManager.updateRowData(
      loanTransaction,
      loanUpdateData,
      loanData?.id,
    );
    if (loanUpdate == k500Error) throw new Error();

    // Push notification
    if (ON_HOLD_DOCUMENT_REQUIREMENTS[hold_reason]) {
      await this.sharedNotificationService.sendNotificationToUser({
        userList: [userId],
        title: OnHoldNotification,
        content: OnHoldFVNotificationBody,
        adminId: body?.adminId,
      });
    }

    const key = `HOLD_DATA_${body?.loanId}`;
    await this.redisService.set(key, loanUpdateData.hold_other_details);
    return {};
  }

  async removeCoolOffFromHistory(userId: string) {
    try {
      if (!userId) return null;
      // If last entry coolOff then remove cooloff
      const attr = ['id', 'reasonId', 'reason', 'isBlacklist', 'coolOfDate'];
      const options = {
        useMaster: false,
        where: { userId },
        order: [['id', 'DESC']],
      };
      const coolOffData = await this.userBlockHistoryRepo.getRowWhereData(
        attr,
        options,
      );
      if (!coolOffData || coolOffData === k500Error) return null;
      if (
        coolOffData?.isBlacklist === '0' &&
        coolOffData?.coolOfDate !== null
      ) {
        const historyData = {
          reasonId: coolOffData?.reasonId,
          userId,
          reason: coolOffData?.reason,
          isBlacklist: '0',
          blockedBy: SYSTEM_ADMIN_ID,
        };
        await this.userBlockHistoryRepo.createRowData(historyData);
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }

  // #05 -> Proceed for loan updation
  private async proceedForManualVerification(reqData) {
    // Params validation
    const masterData = reqData.masterData;
    const loanId = masterData.loanId;
    if (!loanId) return kParamMissing('loanId');
    const masterId = masterData.id;
    if (!masterId) return kParamMissing('masterId');

    // Update loan record
    let updatedData: any = { manualVerification: '0' };
    let updatedResult = await this.repoManager.updateRowData(
      loanTransaction,
      updatedData,
      loanId,
    );
    if (updatedResult === k500Error) return kInternalError;

    // Update master record
    const statusData = masterData?.status;
    statusData.loan = 0;
    statusData.eligibility = 0;
    updatedResult = await this.repoManager.updateRowData(
      MasterEntity,
      { status: statusData },
      masterId,
    );
    if (updatedResult === k500Error) return kInternalError;

    await this.caAssignmentService.assignCA(
      kVerificationAccessStatus.final,
      loanId,
    );
    return statusData;
  }
  //#endregion -> Final approval

  private async getLoanData(reqData) {
    try {
      const loanId = reqData.loanId;
      const loanInclude: any = { model: loanTransaction };
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fullName', 'fcmToken'],
      };
      loanInclude.attributes = ['id', 'userId', 'loan_disbursement_id'];
      const include = [loanInclude, userInclude];
      const attributes = ['dates', 'id', 'loanId', 'status'];
      const options: any = {
        include,
        where: {
          loanId,
          'status.loan': { [Op.notIn]: [6, 7] },
        },
      };
      const masterList = await this.masterRepo.getTableWhereData(
        attributes,
        options,
      );
      if (masterList == k500Error) return kInternalError;
      return masterList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async autoDeclineLoanAfter7Day(query) {
    try {
      const type = query?.type;
      //considering current day
      const days = query?.days ?? 6;
      const attributes = [
        'id',
        'manualVerificationAcceptId',
        'userId',
        'appType',
      ];
      const toDay = new Date();
      // 1. You want finalDate = toDay - X days
      const rawFinalDate = new Date(new Date().setDate(toDay.getDate() - days));
      const finalDate = this.dateService.getEndOfDay(rawFinalDate);
      // 2. You also want afterKFSDate = toDay - 3 days considering current day
      const rawKFSDate = new Date(new Date().setDate(toDay.getDate() - 2));
      const afterKFSDate = this.dateService.getEndOfDay(rawKFSDate);

      // Get in-process loans (cool-off)
      const options = {
        where: {
          loanStatus: 'InProcess',
          createdAt: {
            [Op.lte]: finalDate.endOfDay,
          },
        },
      };
      const loanData = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );

      if (!loanData || loanData === k500Error) return kInternalError;
      const userIds = loanData.map((l) => l.userId);
      const userDataList = await this.userRepo.getTableWhereData(
        ['id', 'stage'],
        {
          where: { id: userIds },
        },
      );
      const allUserData = {};
      userDataList.forEach((user) => {
        allUserData[user.id] = user;
      });

      // Get accepted loans (already filtered by KFS date)
      const options2 = {
        where: {
          loanStatus: 'Accepted',
        },
        include: [
          {
            model: MasterEntity,
            attributes: ['id', 'status', 'kfsAcceptDate'],
            where: {
              kfsAcceptDate: {
                [Op.lte]: afterKFSDate.endOfDay,
              },
            },
          },
        ],
      };
      const loanData2 = await this.loanRepo.getTableWhereData(
        attributes,
        options2,
      );
      if (!loanData2 || loanData2 === k500Error) return kInternalError;

      let loanRejectCount = 0;
      const combinedLoanData = [...loanData, ...loanData2];
      if (type !== 'NOTIFICATION') {
        const rejectCount1 = await this.processDataLoanData(
          loanData,
          allUserData,
        ); // Cool-off
        const rejectCount2 = await this.processDataLoanData2(loanData2); // No cool-off
        loanRejectCount = rejectCount1 + rejectCount2;
      } else {
        const tempOpt = { where: { subType: 'BEFORE_LOAN_DECLINE' } };
        const template = await this.templateRepo.getRowWhereData(
          ['id'],
          tempOpt,
        );
        if (template === k500Error) return kInternalError;
        if (!template) return k422ErrorMessage(kNoTemplateFound);

        const data = {
          userData: combinedLoanData,
          id: template?.id,
          isMsgSent: true,
        };
        return await this.sharedNotificationService.sendNotificationToUser(
          data,
        );
      }

      this.slackService.sendSlackCronAlert({
        url: 'admin/loan/autoDeclineLoanAfter7Day',
        fieldObj: { loanData: combinedLoanData, loanRejectCount },
      });

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async processDataLoanData(loanData, allUserData) {
    const nextApplyDate = this.typeService.getGlobalDate(new Date());
    nextApplyDate.setDate(nextApplyDate.getDate() + 1);
    let rejectCount = 0;

    for (const loan of loanData) {
      try {
        const { id, userId } = loan;
        const adminId = SYSTEM_ADMIN_ID;
        let remark = 'Auto-Rejected - Loan not Accepted';
        const stage = allUserData[userId]?.stage;

        if (stage === UserStage.PAN) {
          remark = 'Auto-Rejected - PAN verification not completed';
        } else if (stage === UserStage.BANKING) {
          remark = 'Auto-Rejected - Bank verification not completed';
        }
        const res = await this.rejectLoan(adminId, id, remark, userId);
        if (res === true) {
          rejectCount++;
          await this.userBlockHistoryRepo.createRowData({
            userId,
            reason: remark,
            isBlacklist: '0',
            blockedBy: adminId,
            coolOfDate: nextApplyDate?.toJSON(),
          });
          await this.checkAndRejectSteps(userId, remark);
        }
      } catch (error) {}
    }

    return rejectCount;
  }

  async processDataLoanData2(loanData2) {
    let rejectCount = 0;

    for (const loan of loanData2) {
      try {
        const { id, userId } = loan;
        const adminId = SYSTEM_ADMIN_ID;
        const eMandate = loan?.masterData?.status?.eMandate;
        const eSign = loan?.masterData?.status?.eSign;
        const disbursement = loan?.masterData?.status?.disbursement;
        if (disbursement != -1) continue;

        let remark = null;
        if (eMandate !== 1) remark = 'Auto-Rejected - eMandate not Completed';
        else if (eSign !== 1) remark = 'Auto-Rejected - eSign not Completed';
        if (!remark) continue;

        const res = await this.rejectLoan(
          adminId,
          id,
          remark,
          userId,
          null,
          null,
          true,
          false,
          true, // No cool-off
        );
        if (res === true) rejectCount++;
        await this.checkAndRejectSteps(userId, remark);
      } catch (error) {}
    }

    return rejectCount;
  }

  //#region Scoring
  async calculateScore(reqData) {
    try {
      // Params validation
      const type = reqData.type;
      if (!type) return kParamMissing('type');
      if (type != kUsrCategories) return kInvalidParamValue('type');

      let scoreData;
      // User categorization
      if (type == kUsrCategories)
        scoreData = await this.getUserCategorizationScore(reqData);
      if (scoreData?.message) return scoreData;

      return scoreData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getUserCategorizationScore(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      const migrate = reqData?.migrate == true ? true : false;
      if (loanId) {
        // Table joins
        const kycInclude: any = { model: KYCEntity };
        kycInclude.attributes = ['aadhaarDOB'];
        const workMailInclude: any = { model: WorkMailEntity };
        workMailInclude.attributes = ['status'];
        const empInclude: any = { model: employmentDetails };
        empInclude.attributes = ['startDate', 'companyName'];
        empInclude.include = [workMailInclude];
        const userInclude: any = { model: registeredUsers };
        userInclude.attributes = ['id'];
        userInclude.include = [empInclude, kycInclude];
        const bankingInclude: any = { model: BankingEntity };
        bankingInclude.attributes = ['accountNumber', 'salary'];
        const include = [bankingInclude, userInclude];
        const attributes = ['loanStatus', 'netScore'];
        const options = { include, where: { id: loanId } };
        const loanData = await this.loanRepo.getRowWhereData(
          attributes,
          options,
        );
        if (loanData == k500Error) return kInternalError;
        if (loanData.loanStatus == 'Rejected')
          return k422ErrorMessage(
            'Request can not be completed due to loan is rejected',
          );
        if (loanData.loanStatus == 'Active' && !migrate)
          return k422ErrorMessage(
            'Request can not be completed due to loan is active',
          );

        const userData = loanData.registeredUsers ?? {};
        const userId = userData.id;
        // Age
        const kycData = userData.kycData ?? {};
        let dob = await this.typeService.getDateAsPerAadhaarDOB(
          kycData.aadhaarDOB,
        );
        if (!dob) return kParamMissing('age');

        dob += kGlobalTrail;
        reqData.age = this.typeService.dateDifference(
          new Date(dob),
          new Date(),
          'Years',
        );
        // Net score
        reqData.netScore = loanData.netScore;
        // Banking params -> AvgBalance, Ecs, LoanApps, Salary
        const bankingData = loanData.bankingData ?? {};
        const accNumber = bankingData.accountNumber;
        if (!accNumber) return kParamMissing('bankingData');
        // Find query for banking data
        const url = await this.commonService.getTansactionQueryParams(loanId);
        if (url?.message) return url;
        const transData = await this.authAi.getCompareAccounts(url);
        if (!transData.valid) return kParamMissing('bankingData');

        const transList = transData.transactionJson;
        if (!transList) return kParamMissing('bankingData');
        // AvgBalance
        const avgBalance = transData.summary?.monthlyAvgBal?.average;
        if (!avgBalance) return kParamMissing('avgBalance');
        reqData.avgBalance = avgBalance;
        // Ecs
        reqData.ecs = transList.filter(
          (el) => el.category == 'ECS/CHQ RETURN CHARGES',
        ).length;
        // LoanApps
        const loanApps = ['bajaj', 'aditya', 'hdb', 'incred', 'early', 'fibe'];
        const totalLoans = transList.filter((el) => {
          try {
            if (el.type == 'CREDIT') {
              let loanFound = false;
              for (let index = 0; index < loanApps.length; index++) {
                const text = loanApps[index];
                if (el.description?.toLowerCase().includes(text)) {
                  loanFound = true;
                  break;
                }
              }
              if (loanFound) return true;
            }
          } catch (e) {}
        }).length;
        reqData.loanApps = totalLoans > 0;
        // Salary
        if (bankingData.salary === undefined) return kParamMissing('salary');
        reqData.salary = Math.floor(bankingData.salary);
        // Employment params -> Employment tenure, Work mail status
        const empData = userData?.employmentData;
        let empStartDate = empData?.startDate;
        if (!empStartDate) {
          const companyName = empData?.companyName;
          const ops = {
            where: { companyName, userId, startDate: { [Op.ne]: null } },
          };
          const empmt = await this.empRepo.getRowWhereData(['startDate'], ops);
          if (empmt === k500Error) return kInternalError;
          if (empmt) empStartDate = empmt?.startDate;
          if (!empStartDate) {
            const empHis = await this.empHistoryRepo.getRowWhereData(
              ['startDate'],
              ops,
            );
            if (empHis === k500Error) return kInternalError;
            if (empHis) empStartDate = empHis?.startDate;
          }
          if (!empStartDate) {
            const crOps = { where: { companyName, userId } };
            const empCrt = await this.empRepo.getRowWhereData(
              ['createdAt'],
              crOps,
            );
            if (empCrt === k500Error) return kInternalError;
            if (empCrt) empStartDate = empCrt?.createdAt;
          }
        }
        if (!empStartDate) return kParamMissing('employmentTenure');
        reqData.employmentTenure = this.typeService.dateDifference(
          empStartDate,
          new Date(),
          'Years',
        );
        const workMailData = empData.workMail ?? {};
        if (!workMailData.status) return kParamMissing('workMailVerification');
        reqData.workMailVerification =
          workMailData.status == '1' || workMailData.status == '3';
      }

      if (reqData.age === undefined) return kParamMissing('age');
      if (reqData.netScore === undefined) return kParamMissing('netScore');
      if (reqData.avgBalance === undefined) return kParamMissing('avgBalance');
      if (reqData.ecs === undefined) return kParamMissing('ecs');
      if (reqData.loanApps === undefined) return kParamMissing('loanApps');
      if (reqData.salary === undefined) return kParamMissing('salary');
      if (reqData.employmentTenure === undefined)
        return kParamMissing('employmentTenure');
      if (reqData.workMailVerification === undefined)
        return kParamMissing('workMailVerification');

      const scoreData: any = await this.getScoreData(reqData);
      if (scoreData?.message) return scoreData;

      let changeReason = '';
      let categoryScoreData = await this.getScoreData({
        userCategorization: scoreData.totalScore,
      });
      if (categoryScoreData.message) return categoryScoreData;
      if (categoryScoreData?.userCategorization?.score == kHighRisk) {
        let salary = reqData.salary;
        if (typeof salary == 'string') salary = +salary;
        let avgBalance = reqData.avgBalance;
        if (typeof avgBalance == 'string') avgBalance = +avgBalance;
        if (salary >= 70000 || avgBalance >= 10000) {
          categoryScoreData = {
            userCategorization: {
              score: kModerateRisk,
              value: categoryScoreData?.userCategorization?.value,
            },
            totalScore: categoryScoreData.totalScore,
          };
          if (salary >= 70000) changeReason = 'Salary more than 70k';
          if (avgBalance >= 10000) changeReason = 'Avg balance more than 10k';
        }
      }

      const finalizedData = {
        ...scoreData,
        ...categoryScoreData,
        changeReason,
      };
      finalizedData.category = finalizedData.totalScore;
      delete finalizedData.totalScore;
      finalizedData.totalValue = finalizedData.userCategorization?.value;
      delete finalizedData.userCategorization;
      const updateScore = reqData.updateScore?.toString() != 'false';

      // Update prediction data
      if (updateScore && reqData.loanId) {
        const attributes = ['id'];
        const options = { where: { loanId: reqData.loanId } };
        const predictionData = await this.predictionRepo.getRowWhereData(
          attributes,
          options,
        );
        if (predictionData == k500Error) return kInternalError;
        if (!predictionData) return k422ErrorMessage(kNoDataFound);

        const updatedData = {
          categorizationTag: finalizedData.category,
          categorizationScore: finalizedData.totalValue,
          categorizationDetails: finalizedData,
        };
        const updateResponse = await this.predictionRepo.updateRowData(
          updatedData,
          predictionData.id,
        );
        if (updateResponse == k500Error) return kInternalError;
        return finalizedData;
      } else
        return {
          ...finalizedData,
          categorizationTag: finalizedData.category,
          categorizationScore: finalizedData.totalValue,
          categorizationDetails: finalizedData,
        };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getScoreData(reqData) {
    try {
      let rawJsonData = await this.redisService.get(kKeyScoreData);
      if (!rawJsonData) {
        const staticData = await this.staticRepo.getRowWhereData(['data'], {
          where: { type: kKeyScoreData },
        });
        if (staticData == k500Error) return kInternalError;
        if (!staticData) return k422ErrorMessage(kNoActiveScoreFound);
        const data = staticData.data ?? [];
        if (data?.length == 0) return k422ErrorMessage(kNoActiveScoreFound);
        rawJsonData = data[0];
        await this.redisService.set(kKeyScoreData, rawJsonData);
      }
      const jsonData = JSON.parse(rawJsonData);

      // Get active score
      let activeScoreData;
      for (const key in jsonData) {
        try {
          if (jsonData[key]['active'] == true) {
            delete jsonData[key]['active'];
            activeScoreData = jsonData[key];
            break;
          }
        } catch (error) {}
      }
      if (!activeScoreData) return k422ErrorMessage(kNoActiveScoreFound);

      // Calculate required score
      let totalScore: number | string = 0;
      const scoreInsights: any = {};
      for (const key in reqData) {
        try {
          const scoreRange = activeScoreData[key];
          if (!scoreRange) continue;

          let value = reqData[key];
          const ranges = scoreRange.ranges;
          const scores = scoreRange.scores;
          if (!ranges || !scores) continue;
          if (ranges.length != scores.length) continue;
          const fallbackScore = scoreRange.fallbackScore;

          for (let index = 0; index < ranges.length; index++) {
            try {
              const rangeData = ranges[index];
              // Number range
              if (typeof rangeData == 'object' && rangeData.length == 2) {
                const minScore = rangeData[0];
                const maxScore = rangeData[1];

                // For number range
                if (
                  typeof minScore == 'number' &&
                  typeof maxScore == 'number'
                ) {
                  if (typeof value == 'string') value = +value;
                  if (value >= minScore && value <= maxScore) {
                    const score = scores[index];
                    if (typeof score == 'string') totalScore = score;
                    else totalScore += score;
                    scoreInsights[key] = { score, value };
                    break;
                  }
                }
              }
              // Boolean range
              else if (typeof rangeData == 'boolean') {
                if (rangeData == value || rangeData.toString() == value) {
                  totalScore += scores[index];
                  scoreInsights[key] = {
                    score: scores[index],
                    value: rangeData,
                  };
                  break;
                }
              }
            } catch (error) {}
          }

          // Fallback scenario
          if (!scoreInsights[key]) {
            if (typeof fallbackScore == 'string') totalScore = fallbackScore;
            else totalScore += fallbackScore;
            scoreInsights[key] = { score: fallbackScore, value };
          }
        } catch (error) {}
      }

      scoreInsights.totalScore = totalScore;
      return scoreInsights;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async changeScoreJson(reqData) {
    try {
      // Params validation
      const adminId = reqData.adminId;
      if (!adminId) return kParamMissing('adminId');
      const targetData = reqData.scoreData;
      if (!targetData) return kParamMissing('scoreData');

      const attributes = ['fullName'];
      const options = { where: { id: adminId } };
      const adminData = await this.adminRepo.getRowWhereData(
        attributes,
        options,
      );
      if (!adminData) return k422ErrorMessage(kNoDataFound);
      if (adminData == k500Error) return kInternalError;

      const staticData = await this.staticRepo.getRowWhereData(['data'], {
        where: { type: kKeyScoreData },
      });
      if (staticData == k500Error) return kInternalError;
      if (!staticData) return k422ErrorMessage(kNoActiveScoreFound);
      const data = staticData.data ?? [];
      if (data?.length == 0) return k422ErrorMessage(kNoActiveScoreFound);
      let scoreData = data[0];
      if (scoreData == null) scoreData = {};
      else scoreData = JSON.parse(scoreData);
      let targetIndex = 0;
      for (const key in scoreData) {
        try {
          targetIndex = +key;
          if (reqData.addScore == true) scoreData[key].active = false;
        } catch (error) {}
      }

      if (reqData.addScore == true) {
        targetIndex += 1;
        targetData.adminId = adminId;
        targetData.lastUpdatedAt = this.typeService.getGlobalDate(new Date());
        targetData.active = true;
        scoreData[targetIndex] = targetData;
      }

      await this.redisService.set(kKeyScoreData, JSON.stringify(scoreData));
      const updateResponse = await this.staticRepo.updateRowWhereData(
        { data: [JSON.stringify(scoreData)] },
        { where: { type: kKeyScoreData } },
      );
      if (updateResponse == k500Error) return kInternalError;
      // No score data exists in database, Need to create one
      if (updateResponse.toString() == '0') {
        const createdResponse = await this.staticRepo.createRowWhereData({
          type: kKeyScoreData,
          data: [JSON.stringify(scoreData)],
        });
        if (createdResponse == k500Error) return kInternalError;
      }
      return scoreData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion Scoring

  async nameValidation(userId) {
    // find user
    try {
      let checkNameValidation = await this.redisService.get('NAME_VALIDATION');
      if (checkNameValidation == false || checkNameValidation == 'false')
        return true;
      const att = ['id', 'completedLoans', 'fullName', 'masterId', 'appType'];
      const options = { where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(att, options);
      if (!userData) return k422ErrorMessage('User not found');
      /// if user if new then we check other skip this condition
      if ((userData?.completedLoans ?? 0) > 0) return true;

      const body = { name: userData.fullName };
      const appType = userData?.appType;
      const baseUrl = this.commonService.getPythonBaseUrl(appType);
      const url = baseUrl + 'v2/address/names';
      const nameOutput = await this.apiService.requestPost(url, body);
      if (nameOutput) {
        if (nameOutput?.valid == true) {
          if (
            nameOutput?.data.type == 'M' &&
            nameOutput?.data.prob > NAME_VALIDATE
          ) {
            // return k422ErrorMessage('Name not valid');
            const adminId = SYSTEM_ADMIN_ID;
            const remark = APPROVED_SALARY_IS_NOT_MATCH_WITH_AREA;
            const masterData = await this.masterRepo.getRowWhereData(
              ['loanId', 'rejection'],
              { where: { id: userData.masterId } },
            );
            const updateRejection = {
              rejection: {
                ...(masterData.rejection ?? {}),
                nameValidType: nameOutput?.data.type,
                nameValidProb: nameOutput?.data.prob,
              },
            };
            await this.masterRepo.updateRowData(
              updateRejection,
              userData.masterId,
            );
            if (masterData?.loanId)
              await this.rejectLoan(
                adminId,
                masterData?.loanId,
                remark,
                userId,
              );
            await this.checkAndRejectSteps(userId, remark);
            const update = { isBlacklist: '1' };
            await this.userRepo.updateRowData(update, userId);
            // Create UserBlockHistory record
            const historyData = {
              userId,
              reason: remark,
              isBlacklist: '1',
              blockedBy: adminId,
            };
            await this.userBlockHistoryRepo.createRowData(historyData);
            return false;
          }
        }
      }
      return true;
    } catch (error) {
      return true;
    }
  }

  async nbfcFirstvalidateEligiblityForLoan(reqData) {
    try {
      let {
        loanId,
        userId,
        salary,
        email,
        cibilScore,
        plScore,
        pre_approve_amount,
        completedLoans,
        //  experianData
      } = reqData;
      let MAX_LOAN_AMOUNT = GLOBAL_RANGES.MAX_LOAN_AMOUNT;
      let MIN_LOAN_AMOUNT = GLOBAL_RANGES.MIN_LOAN_AMOUNT;

      const year = 365;
      const version = 'V2';
      const skipUpdate = reqData?.skipUpdate ?? false;
      let delayDays = 0;
      let lastLoanData: any = {};
      if (completedLoans > 0) {
        lastLoanData = await this.lastLoanStatus(userId);
        delayDays = lastLoanData?.delayDays;
        reqData.lastLoanData = lastLoanData;
      }

      // Get loan type
      const loanType = this.nbfcFirstcheckLoanType(reqData);
      reqData.loanType = loanType.type;

      const highRiskType = this.nbfcHighRiskType({
        age: reqData.age,
        currentBalance: reqData.currentBalance,
        completedLoan: reqData.completedLoans,
        aadhaarState: reqData.aadhaarState,
        cibilScore,
        highCreditAmount: reqData.highCreditAmount,
        inqLast30Days: reqData.inquiryPast30Days,
        overDueDays: reqData.totalOverdueDays,
        plScore,
        salary,
        typeOfDevice: reqData.typeOfDevice,
        zeroBalanceAccounts: reqData.zeroBalanceAccounts,
      });
      reqData.highRiskType = highRiskType.type;

      // check function user eligible or not
      const checkEligible = this.nbfcFirstIsEligibleOrNotLogic(reqData);

      // check interest rate according condtion
      const checkInterestRate = await this.nbfcFirstInterestRateLogic(reqData);

      //if is pre approval user eligible amount is salary and portion is 0
      let checkSalaryPortion: any = {
        eligibleAmount: pre_approve_amount,
        portion: 0,
        message: 'This is pre approval user',
      };
      //check salary portion according condtion, if user is pre approval user then we skip this logic

      if (!reqData?.isPreApprovUser)
        checkSalaryPortion = await this.nbfcFirstMaxSalaryPortionLogic(reqData);

      // check users verified by manual or not through cibil
      const checkCibilManualVerifiedOrNot =
        this.nbfcFirstCibilManualVerificationLogic(reqData);

      // check users verified by manual or not through experian
      // const checkExperianManualVerifiedOrNot =
      //   this.nbfcFirstExperianManualVerificationLogic(experianData);

      // this is exception case of eligibilty
      const exceptionCase: any = await this.exceptionCaseOfEligibilty(reqData);

      if (exceptionCase?.isEligible == true && delayDays < 5) {
        if (salary < MAX_LOAN_AMOUNT) {
          checkSalaryPortion.eligibleAmount = 250000;
          checkSalaryPortion.portion = 100;
          checkSalaryPortion.message = `This is exceptionCase with salary above 2L and fall in ${exceptionCase?.message}`;
          MAX_LOAN_AMOUNT = 250000;
        } else if (salary >= MAX_LOAN_AMOUNT) {
          checkSalaryPortion.eligibleAmount = 300000;
          checkSalaryPortion.portion = 100;
          checkSalaryPortion.message = `This is exceptionCase with salary above 3L and fall in ${exceptionCase?.message}`;
          MAX_LOAN_AMOUNT = 300000;
        }
      }

      // Extract the annum interest, portion, isEligible, isManual from funtion
      const anumm = checkInterestRate.annum;
      const portion = checkSalaryPortion.portion;
      const isEligible = checkEligible.isEligible;
      const isCibilManual = checkCibilManualVerifiedOrNot.isManual;
      // const isExperianManual = checkExperianManualVerifiedOrNot.isManual;
      // const isManual = isCibilManual || isExperianManual;
      const isManual = isCibilManual;

      // Calculate values based on responses
      const interestRate = +(anumm / year).toFixed(3);
      let eligibleAmount = (salary * portion) / 100;

      //  if last loan is ontime then we get previous loan amount with 10% increment
      if (checkSalaryPortion.eligibleAmount > 0)
        eligibleAmount = checkSalaryPortion.eligibleAmount;

      // if eligibleAmount is check from nbfc MAX_LOAN_AMOUNT
      if (eligibleAmount > MAX_LOAN_AMOUNT) eligibleAmount = MAX_LOAN_AMOUNT;
      if (eligibleAmount < MIN_LOAN_AMOUNT) eligibleAmount = MIN_LOAN_AMOUNT;

      //calculate interest rate per annum
      let intrestPerAnnum = Math.round(eligibleAmount * interestRate);
      //if email is company email then eligible amount is 10000
      if (
        (email.includes(EnvConfig.emailDomain.companyEmailDomain1) ||
          email.includes(EnvConfig.emailDomain.companyEmailDomain2)) &&
        gIsPROD
      )
        eligibleAmount = GLOBAL_RANGES.COMPANY_USER_ELIGIBILE_AMOUNT;
      // eligibleAmount set accordint slab
      eligibleAmount =
        Math.round(eligibleAmount / GLOBAL_RANGES.SLIDER_AMOUNT_SLAB) *
        GLOBAL_RANGES.SLIDER_AMOUNT_SLAB;

      // Prepare eligibility details and messages
      const messages = {
        loanTypeMessage: isCibilManual ? loanType.message : '',
        checkEligible: checkEligible.message,
        checkInterestRate: checkInterestRate.message,
        checkSalaryPortion: checkSalaryPortion.message,
        checkCibilManualVerifiedOrNot: checkCibilManualVerifiedOrNot.message,
        // checkExperianManualVerifiedOrNot:
        //   checkExperianManualVerifiedOrNot.message,
      };
      let categoryTag =
        loanType.type == 'LOW RISKY' ? 0 : loanType.type == 'RISKY' ? 2 : -1;

      let isEligibleForCollateral = false;

      if (
        loanType.type == 'LOW RISKY' &&
        cibilScore > GLOBAL_RANGES.MAX_PREMIUM_CIBIL_SCORE &&
        plScore > GLOBAL_RANGES.MAX_PREMIUM_PL_SCORE &&
        salary > GLOBAL_RANGES.MAX_SALARY_AMOUNT &&
        GLOBAL_FLOW.COLLATERAL_FLOW
      )
        isEligibleForCollateral = false;

      if (skipUpdate == false) {
        const options = {
          where: { id: loanId },
        };
        //get loan data for update data
        const loanData = await this.loanRepo.getRowWhereData(
          ['id', 'eligibilityDetails'],
          options,
        );
        if (!loanData) return k422ErrorMessage('No data found');
        if (loanData == k500Error) throw new Error();

        // prepaer eligibilityDetails
        let eligibilityDetails: any = loanData?.eligibilityDetails;
        eligibilityDetails.isManual = isManual;
        eligibilityDetails.anummIntrest = anumm;
        eligibilityDetails.portion = portion;
        eligibilityDetails.messages = messages;
        eligibilityDetails.version = version;
        eligibilityDetails.loanType = loanType.type;
        eligibilityDetails.salary = salary;
        if (GLOBAL_FLOW.COLLATERAL_FLOW)
          eligibilityDetails.isEligibleForCollateral = isEligibleForCollateral;

        // update loan entity
        const updateLoanData = {
          interestRate,
          eligibilityDetails,
          categoryTag,
        };
        const updateData = await this.loanRepo.updateRowData(
          updateLoanData,
          loanId,
        );
        if (updateData == k500Error) throw new Error();
      }
      return {
        highRiskType,
        cibilScore,
        plScore,
        loanId,
        userId,
        eligibleAmount,
        interestRate,
        salary,
        isEligible,
        anummInterest: anumm,
        intrestPerAnnum,
        isManual,
        portion,
        messages,
        version,
        loanType: loanType.type,
        categoryTag,
        isEligibleForCollateral,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async nbfcSecondvalidateEligiblityForLoan(reqData) {
    try {
      // Extracting relevant data from the request
      let {
        loanId,
        userId,
        salary,
        completedLoans,
        email,
        cibilScore,
        plScore,
        experianData,
      } = reqData;
      const skipUpdate = reqData?.skipUpdate ?? false;
      const MAX_LOAN_AMOUNT = GLOBAL_RANGES.MAX_LOAN_AMOUNT;
      const MIN_LOAN_AMOUNT = GLOBAL_RANGES.MIN_LOAN_AMOUNT;

      const year = 365;
      const version = 'V2';

      // Fetch last loan status if there are completed loans
      let previousloanAmt = 0;
      let isAmtIncrement = false;
      let lastLoanStatus: any;

      if (completedLoans > 0) {
        const userLoanStatus: any = await this.lastLoanStatus(userId);
        previousloanAmt = userLoanStatus?.loanAmount ?? 0;
        lastLoanStatus = userLoanStatus?.status;
        isAmtIncrement = lastLoanStatus === 'LOAN_ON_TIME';
        reqData.lastLoanStatus = lastLoanStatus;
        reqData.previousloanAmt = previousloanAmt;
      }
      // Get loan type
      const loanType = this.nbfcSecondCheckLoanType(reqData);
      reqData.loanType = loanType.type;

      // check function user eligible or not
      const checkEligible = this.nbfcSecondIsEligibleOrNotLogic(reqData);

      // check interest rate according condtion
      const checkInterestRate = this.nbfcSecondInterestRateLogic(reqData);

      //check salary portion according condtion
      const checkSalaryPortion = this.nbfcSecondCheckSalaryPortion(reqData);

      // check users verified by manual or not through cibil
      const checkCibilManualVerifiedOrNot =
        this.nbfcSecondCibilManualVerificationLogic(reqData);

      // check users verified by manual or not through experian
      const checkExperianManualVerifiedOrNot =
        this.nbfcSecondExperianManualVerificationLogic(experianData);

      // Extract the annum interest, portion, isEligible, isManual from funtion
      const anumm = checkInterestRate.annum;
      const portion = checkSalaryPortion.portion;
      const isEligible = checkEligible.isEligible;
      const isCibilManual = checkCibilManualVerifiedOrNot.isManual;
      const isExperianManual = checkExperianManualVerifiedOrNot.isManual;
      const isManual = isCibilManual && isExperianManual;

      // Calculate values based on responses
      const interestRate = +(anumm / year).toFixed(3);
      let eligibleAmount = (salary * portion) / 100;

      //  if last loan is ontime then we get previous loan amount with 10% increment
      if (checkSalaryPortion.eligibleAmount > 0)
        eligibleAmount = checkSalaryPortion.eligibleAmount;

      // if eligibleAmount is check from nbfc MAX_LOAN_AMOUNT
      if (eligibleAmount > MAX_LOAN_AMOUNT) eligibleAmount = MAX_LOAN_AMOUNT;
      if (eligibleAmount < MIN_LOAN_AMOUNT) eligibleAmount = MIN_LOAN_AMOUNT;

      //calculate interest rate per annum
      let intrestPerAnnum = Math.round(eligibleAmount * interestRate);

      //if email is company email then eligible amount is 10000
      if (
        email.includes(EnvConfig.emailDomain.companyEmailDomain1) ||
        email.includes(EnvConfig.emailDomain.companyEmailDomain2)
      )
        eligibleAmount = GLOBAL_RANGES.COMPANY_USER_ELIGIBILE_AMOUNT;

      // eligibleAmount set accordint slab
      eligibleAmount =
        Math.round(eligibleAmount / GLOBAL_RANGES.SLIDER_AMOUNT_SLAB) *
        GLOBAL_RANGES.SLIDER_AMOUNT_SLAB;

      // Prepare eligibility details and messages
      const messages = {
        loanTypeMessage: loanType.message,
        checkEligible: checkEligible.message,
        checkInterestRate: checkInterestRate.message,
        checkSalaryPortion: checkSalaryPortion.message,
        checkCibilManualVerifiedOrNot: checkCibilManualVerifiedOrNot.message,
        checkExperianManualVerifiedOrNot:
          checkExperianManualVerifiedOrNot.message,
      };

      let categoryTag =
        loanType.type == 'LOW RISKY' ? 0 : loanType.type == 'RISKY' ? 2 : -1;
      if (skipUpdate == false) {
        const options = {
          where: { id: loanId },
        };
        //get loan data for update data
        const loanData = await this.loanRepo.getRowWhereData(
          ['id', 'eligibilityDetails'],
          options,
        );
        if (!loanData) return k422ErrorMessage('No data found');
        if (loanData == k500Error) throw new Error();

        // prepaer eligibilityDetails
        let eligibilityDetails: any = loanData?.eligibilityDetails;
        eligibilityDetails.isManual = isManual;
        eligibilityDetails.anummIntrest = anumm;
        eligibilityDetails.portion = portion;
        eligibilityDetails.messages = messages;
        eligibilityDetails.version = version;
        eligibilityDetails.loanType = loanType.type;
        eligibilityDetails.salary = salary;

        // update loan entity
        const updateLoanData = {
          interestRate,
          eligibilityDetails,
          categoryTag,
        };
        const updateData = await this.loanRepo.updateRowData(
          updateLoanData,
          loanId,
        );
        if (updateData == k500Error) throw new Error();
      }
      return {
        cibilScore,
        plScore,
        loanId,
        userId,
        eligibleAmount,
        interestRate,
        isEligible,
        salary,
        anummInterest: anumm,
        intrestPerAnnum,
        isManual,
        portion,
        messages,
        version,
        loanType: loanType.type,
        categoryTag,
      };
    } catch (error) {}
  }

  async assignToCSE(masterIds) {
    const cseAdmins = await this.assignmentService.fetchadmins(
      masterIds,
      'CSE',
      `SHIFTPLAN_${CSE_ROLE_ID}_LASTINDEX`,
    );
    if (!cseAdmins) return k500Error;
    const query = cseAdmins
      .map(
        (entry) =>
          `UPDATE public."MasterEntities" SET "assignedCSE" = ${entry.assignTo} WHERE "id" = ${entry.id};`,
      )
      .join('');
    const queryData = await this.repoManager.injectRawQuery(
      MasterEntity,
      query,
    );
    if (queryData == k500Error) return k500Error;
    return true;
  }

  //for first nbfc
  async nbfcFirstInterestRateLogic(reqData) {
    const {
      highRiskType,
      userId,
      salary,
      cibilScore,
      plScore,
      overdueBalance,
      completedLoans,
      loanType,
      lastLoanData,
    } = reqData;

    let annum = 36.5;
    let message = 'Default rate applied';

    const MAX_CIBIL_SCORE = 749;
    const MAX_PL_SCORE = 749;

    // Fetch last loan status if there are completed loans
    let lastLoanStatus = lastLoanData?.status;

    // Decision Logic
    if (highRiskType == 'Risk_01') {
      annum = 36.5;
      message = 'Risk Category - Risk_01';
    } else if (cibilScore == -1 || plScore == -1) {
      annum = 36.5;
      message = 'NTC - New to CIBIL user';
    } else if (completedLoans === 0 && loanType === 'RISKY') {
      annum = 36.5;
      message = 'No loans and risky loan type';
    } else if (lastLoanStatus === 'LOAN_DELAYED') {
      annum = 36.5;
      message = 'Last loan was delayed';
    } else if (overdueBalance > 0) {
      annum = 36.5;
      message = 'Overdue balance present';
    } else if (cibilScore <= MAX_CIBIL_SCORE || plScore <= MAX_PL_SCORE) {
      annum = 36.5;
      message = 'Low CIBIL score or Low PL score';
    } else if (
      cibilScore >= 750 &&
      plScore >= 750 &&
      (cibilScore <= 764 || plScore <= 764)
    ) {
      if (salary < 50000) {
        annum = 35;
        message = 'Salary below 50,000';
      } else if (salary <= 75000) {
        annum = 34;
        message = 'Salary between 50,000 to 75,000';
      } else if (salary <= GLOBAL_RANGES.MAX_SALARY_AMOUNT) {
        annum = 33;
        message = `Salary between  75,000 to ${GLOBAL_RANGES.MAX_SALARY_AMOUNT} `;
      } else if (salary > GLOBAL_RANGES.MAX_SALARY_AMOUNT) {
        annum = 32;
        message = `Salary between  ${GLOBAL_RANGES.MAX_SALARY_AMOUNT} and max range`;
      } else {
        annum = 36.5;
        message = 'Scores between 750-764';
      }
    } else if (
      cibilScore >= 765 &&
      plScore >= 765 &&
      (cibilScore < GLOBAL_RANGES.MAX_PREMIUM_CIBIL_SCORE ||
        plScore < GLOBAL_RANGES.MAX_PREMIUM_PL_SCORE)
    ) {
      annum = 31;
      message = 'Scores between 765 and 830';
    } else if (
      cibilScore >= GLOBAL_RANGES.MAX_PREMIUM_CIBIL_SCORE &&
      plScore >= GLOBAL_RANGES.MAX_PREMIUM_PL_SCORE
    ) {
      annum = 28;
      message = 'Scores above 830';
    } else {
      annum = 36.5;
      message = 'No interest rate scenario match';
    }

    return { annum, message };
  }

  async nbfcFirstMaxSalaryPortionLogic(reqData) {
    const {
      highRiskType,
      userId,
      cibilScore,
      plScore,
      completedLoans,
      typeOfDevice,
      loanType,
      salary,
      overdueBalance,
      lastLoanData,
    } = reqData;

    let portion = 75;
    let eligibleAmount = 0;
    let message = 'Base portion applied'; // Default message
    let lastLoanStatus;
    let isAmtIncrement = false;
    let previousloanAmt = 0;
    let previousPortion: any = 0;
    let maxAmount = 0;
    let netApprovedAmount = 0;
    if (completedLoans > 0) {
      const userLoanStatus: any = lastLoanData;
      lastLoanStatus = userLoanStatus?.status;
      netApprovedAmount = userLoanStatus.netApprovedAmount;
      isAmtIncrement = lastLoanStatus === 'LOAN_ON_TIME';
      previousPortion = userLoanStatus?.previousPortion;
      previousloanAmt = userLoanStatus?.loanAmount ?? 0;
    }

    // Decision Logic with Messages
    if (plScore == -1 || cibilScore == -1) {
      portion = 50;
      message = 'NTC - New to CIBIL user';
    } else if (
      loanType === 'NOT DECIDED' &&
      salary < 40000 &&
      completedLoans === 0
    ) {
      portion = 50;
      eligibleAmount = (salary * portion) / 100;
      message = 'Loan Amount Half to salary for NOT DECIDED loan type';
    } else if (typeOfDevice == '1' && cibilScore < 700) {
      portion = 75;
      message = 'IOS Device and CIBIL score less than 700';
    } else if (completedLoans === 0 && loanType === 'RISKY') {
      portion = 50;
      message = 'No completed loans and risky loan type';
    } else if (lastLoanStatus === 'LOAN_DELAYED' && loanType === 'RISKY') {
      eligibleAmount = 10000;
      message = 'Last loan delayed and risky loan type';
    } else if (lastLoanStatus === 'LOAN_DELAYED' && loanType === 'LOW RISKY') {
      portion = 50;
      message = 'Last loan delayed and low risk loan type';
    } else if (completedLoans > 0 && loanType === 'RISKY') {
      portion = 75;
      message = 'Completed loans and risky loan type';
    } else if (completedLoans > 0 && loanType === 'LOW RISKY') {
      portion = 120;
      message = 'Completed loans and low risk loan type';
    } else if (cibilScore >= 750 && plScore >= 750) {
      portion = 100;
      message = 'High CIBIL and PL score';
    } else if (
      loanType === 'LOW RISKY' &&
      cibilScore >= 750 &&
      plScore >= 700
    ) {
      portion = 100;
      message = 'LOW RISKY & ONE OF THE SCORE IS BELOW 750';
    } else if (
      loanType === 'LOW RISKY' &&
      plScore >= 750 &&
      cibilScore >= 700
    ) {
      portion = 100;
      message = 'LOW RISKY & ONE OF THE SCORE IS BELOW 750';
    } else if (loanType === 'LOW RISKY' && typeOfDevice == '1') {
      portion = 100;
      message = 'LOW RISKY & IOS Device';
    } else {
      portion = 75;
      message = 'No salary portion match';
    }

    if (portion > 75) {
      if (highRiskType == 'Risk_01') {
        portion = 75;
        message = 'Max portion capped at 75 due to Risk_01';
      }
    }
    // If the loan was paid on time, increment the previous loan amount by 10%
    if (isAmtIncrement) {
      // if overdueBalance is zero then increment 10%
      if (overdueBalance == 0) previousloanAmt += (10 / 100) * previousloanAmt;
      message =
        overdueBalance == 0
          ? message + ' and 10% increment'
          : message + ' but is not increment 10% due to overdueBalance';

      // Adjust eligible amount based on the increment
      eligibleAmount = previousloanAmt;

      // maxLoanAmount not over max salary portion
      if (loanType === 'RISKY' || loanType === 'NOT DECIDED')
        maxAmount = (salary * portion) / 100;
      else if (loanType === 'LOW RISKY') {
        maxAmount = (salary * portion) / 100;
        if (eligibleAmount < maxAmount) eligibleAmount = maxAmount;
      } else maxAmount = (salary * 120) / 100;

      //this condtion for if eligibleAmount is check from nbfc MAX_LOAN_AMOUNT
      if (eligibleAmount < GLOBAL_RANGES.MIN_LOAN_AMOUNT)
        eligibleAmount = maxAmount;

      // If the calculated eligible amount exceeds the maximum loan amount, cap it at the maximum
      if (eligibleAmount > maxAmount) eligibleAmount = maxAmount;

      if (eligibleAmount > GLOBAL_RANGES.MAX_LOAN_AMOUNT)
        eligibleAmount = GLOBAL_RANGES.MAX_LOAN_AMOUNT;
    }

    // NTC user
    if (cibilScore == -1 || plScore == -1) {
      portion = 50;
      eligibleAmount = 10000;
      message = 'NTC - New to CIBIL user';
    }

    if (eligibleAmount > GLOBAL_RANGES.MAX_LOAN_AMOUNT)
      eligibleAmount = GLOBAL_RANGES.MAX_LOAN_AMOUNT;

    return { eligibleAmount, portion, message };
  }

  nbfcFirstCibilManualVerificationLogic(reqData) {
    const {
      highRiskType,
      overdueBalance,
      totalOverdueDays,
      completedLoans,
      cibilScore,
      plScore,
      ecsDetails,
      inquiryPast30Days,
      PLAccounts,
      loanType,
    } = reqData;

    let isManual = false;
    let message = 'No manual verification required'; // Default message

    const isPLExceptionUser =
      completedLoans >= GLOBAL_RANGES.PL_EXCEPTION_MIN_COMPLETED_LOANS;

    // Decision Logic with Messages Including "Manual"
    if (highRiskType == 'Risk_01') {
      isManual = true;
      message = 'Category - Risk_01';
    } else if (cibilScore == -1 || plScore == -1) {
      isManual = true;
      message = 'NTC - New to CIBIL user';
    } else if (overdueBalance > 0) {
      isManual = true;
      message = 'Manual check: overdue balance';
    } else if (totalOverdueDays > 0) {
      isManual = true;
      message = 'Manual check: overdue days';
    } else if (isPLExceptionUser && plScore < 700) {
      isManual = true;
      message = 'Manual check: PL exception user';
    } else if (ecsDetails?.ecsBounceCount >= 3) {
      isManual = true;
      message = 'Manual check: ECS bounces';
    } else if (inquiryPast30Days > MAX_INQUIRY_PAST_30_DAYS) {
      isManual = true;
      message = 'Manual check: high inquiries';
    } else if (PLAccounts === 0) {
      isManual = true;
      message = 'Manual check: no PL accounts';
    } else if (loanType === 'RISKY') {
      isManual = true;
      message = 'Manual check: risky loan type';
    } else if (cibilScore < 700) {
      isManual = true;
      message = 'CIBIL score below 700';
    } else if (plScore < 700) {
      isManual = true;
      message = 'PL score below 700';
    }

    return { isManual, message };
  }

  nbfcFirstIsEligibleOrNotLogic(reqData) {
    const {
      aadhaarState,
      currentBalance,
      cibilScore,
      plScore,
      overdueAccounts,
      overdueBalance,
      completedLoans,
      PLAccounts,
      accounts,
      inquiryPast30Days,
      salary,
      ecsDetails,
      typeOfDevice,
      zeroBalanceAccounts,
    } = reqData;

    const overduePast12Months = accounts?.filter((el) => {
      const dateReported =
        this.typeService.strDateToDate(el?.dateReported) ?? '-';

      const twelveMonthsAgo = new Date();
      twelveMonthsAgo.setFullYear(twelveMonthsAgo.getFullYear() - 1);
      const isWithinLast12Months =
        new Date(dateReported).getTime() > twelveMonthsAgo.getTime();

      if (isWithinLast12Months && el.lastDelayDays > 0) {
        return el;
      }
    });

    const isPLExceptionUser =
      completedLoans >= GLOBAL_RANGES.PL_EXCEPTION_MIN_COMPLETED_LOANS;
    let isEligible = true;
    let message = 'Eligible';

    if (cibilScore == -1 || plScore == -1) {
      if (salary >= 25000 && (typeOfDevice == '1' || salary > 60000)) {
        isEligible = true;
        message = 'NTC - New to CIBIL user';
      } else {
        isEligible = false;
        message = 'NTC - New to CIBIL user and Salary below 60k or Non IOS';
      }
    } else if (cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE) {
      isEligible = false;
      message = `CIBIL score is below ${GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE}.`;
    } else if (
      plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE &&
      !isPLExceptionUser
    ) {
      isEligible = false;
      message =
        'PL score is below 700 and Repeat user with less than 3 completed loans';
    } else if (
      cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE &&
      plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE &&
      overdueBalance > 0
    ) {
      isEligible = false;
      message =
        'CIBIL and PL scores are below 700, and there is an overdue balance.';
    } else if (
      (cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE ||
        plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE) &&
      completedLoans <= 0
    ) {
      isEligible = false;
      message = 'CIBIL or PL score is below 700 and no loan history.';
    } else if (PLAccounts === 0 && overduePast12Months.length > 0) {
      isEligible = false;
      message = 'No active PL accounts and overdue in the last 12 months.';
    } else if (
      cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE &&
      plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE &&
      inquiryPast30Days > MAX_INQUIRY_PAST_30_DAYS
    ) {
      isEligible = false;
      message = 'Inquiry past 30 days is greater 10.';
    } else if (
      isPLExceptionUser &&
      plScore < 650 &&
      inquiryPast30Days >= MAX_INQUIRY_PAST_30_DAYS
    ) {
      isEligible = false;
      message =
        'PL exception user with score below 650 and inquiry past 30 days is greater 10.';
    } else if (
      inquiryPast30Days >= MAX_INQUIRY_PAST_30_DAYS &&
      salary <= 35000
    ) {
      isEligible = false;
      message =
        'Inquiry past 30 days is greater or equal 10 and salary equal or less 35000';
    } else if (inquiryPast30Days >= 12 && salary < 60000) {
      isEligible = false;
      message =
        'Inquiry past 30 days is greater or equal 12 and salary is less 60000';
    } else if (salary < 25000 && completedLoans == 0) {
      isEligible = false;
      message = 'salary is less 25000 and new user';
    } else if (salary < 50000 && overdueBalance > 1000) {
      isEligible = false;
      message = 'salary is less 50000 and over due balance is greater 1000';
    }
    // else if (
    //   // ecsDetails.ecsBounceCount > 2 &&
    //   salary < 60000 &&
    //   completedLoans <= 10
    // ) {
    //   isEligible = false;
    //   // message =
    //   //   'salary is less 60000, new user and ecsBounceCount is greater 2';
    //   message = 'salary is less 60000 and new user';
    // }
    else if (
      PLAccounts <= 7 &&
      (salary < 60000 || inquiryPast30Days > 4) &&
      completedLoans <= 5
    ) {
      isEligible = false;
      message =
        'Pl accounts is less or equal 10, salary is less 60000 or inquiry past 30 days is greater 4 and new user';
    } else if (typeOfDevice != '1' && cibilScore < 700) {
      isEligible = false;
      message = 'CIBIL score is below 700 and Non IOS device';
    } else if (typeOfDevice != '1' && !isPLExceptionUser && plScore < 700) {
      isEligible = false;
      message =
        'PL score is below 700 and Non IOS device and not PL Exception user';
    } else if ((overdueAccounts ?? 0) > 2 && plScore < 700 && salary < 125000) {
      isEligible = false;
      message =
        'PL score is below 700 and More than 2 overdue accounts found for salary less than 1.25L';
    } // v2.6 - Overdue Accounts more than 3
    else if (overdueAccounts > 3) {
      isEligible = false;
      message = 'v2.6 - Overdue Accounts more than 3';
    }
    // v2.6 - Overdue Accounts more than 2 and salary less than 1L
    else if (overdueAccounts > 2 && salary < 100000) {
      isEligible = false;
      message = 'v2.6 - Overdue Accounts more than 2 and salary less than 1L';
    }
    //  v2.6 - New user with salary less than 50k and inq more than or eq to 12
    else if (completedLoans == 0 && salary < 50000 && inquiryPast30Days >= 12) {
      isEligible = false;
      message =
        'v2.6 - New user with salary less than 50k and inq more than or eq to 12';
    }
    // v2.6 - New user having Non IOS device with salary less than 50k and inq more than or eq to 4 having aadhaar state Bihar
    else if (
      completedLoans == 0 &&
      salary < 50000 &&
      aadhaarState == 'Bihar' &&
      inquiryPast30Days >= 4 &&
      typeOfDevice != '1'
    ) {
      isEligible = false;
      message =
        'v2.6 - New user having Non IOS device with salary less than 50k and inq more than or eq to 4 having aadhaar state Bihar';
    }
    // v2.6 - New user having Non IOS device with salary less than 50k and inq more than 5 having aadhaar state Odisha
    else if (
      completedLoans == 0 &&
      salary < 50000 &&
      aadhaarState == 'Odisha' &&
      inquiryPast30Days > 5 &&
      typeOfDevice != '1'
    ) {
      isEligible = false;
      message =
        'v2.6 - New user having Non IOS device with salary less than 50k and inq more than 5 having aadhaar state Odisha';
    }
    // v2.6 - New user having Non IOS device with salary less than 50k and CIBIL current balance is less than 50k
    else if (
      salary < 50000 &&
      typeOfDevice != '1' &&
      completedLoans == 0 &&
      currentBalance < 50000
    ) {
      isEligible = false;
      message =
        'v2.6 - New user having Non IOS device with salary less than 50k and CIBIL current balance is less than 50k';
    }
    // v2.6 - New user having Non IOS device with salary less than 50k and cibil score < 730 having aadhaar state Delhi
    else if (
      salary < 50000 &&
      completedLoans == 0 &&
      aadhaarState == 'Delhi' &&
      cibilScore < 730 &&
      typeOfDevice != '1'
    ) {
      isEligible = false;
      message =
        'v2.6 - New user having Non IOS device with salary less than 50k and cibil score < 730 having aadhaar state Delhi';
    }
    // v2.6 - New user with salary less than 50k and cibil zeroBalanceAccounts <= 15 and inq > 8
    else if (
      salary < 50000 &&
      completedLoans == 0 &&
      zeroBalanceAccounts <= 15 &&
      inquiryPast30Days > 8
    ) {
      isEligible = false;
      message =
        'v2.6 - New user with salary less than 50k and cibil zeroBalanceAccounts <= 15 and inq > 8';
    }
    // v2.6 - New user having Non IOS device with salary less than 50k and inq > 4 having aadhaar state Madhya Pradesh
    else if (
      salary < 50000 &&
      completedLoans == 0 &&
      inquiryPast30Days > 4 &&
      aadhaarState == 'Madhya Pradesh' &&
      typeOfDevice != '1'
    ) {
      isEligible = false;
      message =
        'v2.6 - New user having Non IOS device with salary less than 50k and inq > 4 having aadhaar state Madhya Pradesh';
    }

    return { isEligible, message };
  }

  nbfcFirstcheckLoanType(reqData) {
    const {
      cibilScore,
      plScore,
      overdueBalance,
      inquiryPast30Days,
      salary,
      aadhaarState,
      PLOutstanding,
      typeOfDevice,
      PLAccounts,
      ecsDetails,
    } = reqData;

    const checkRisky = () => {
      if (salary < 25000) return 'Salary below 25,000.';
      if (ecsDetails?.ecsBounceCount > 2 && salary < 60000)
        return 'More than 2 ECS bounces with salary below 60,000.';
      if (salary <= 35000 && inquiryPast30Days >= 10)
        return 'Over 10 inquiries in the past 30 days with salary at or below 35,000.';
      if (overdueBalance > 1000 && salary < 50000)
        return 'Overdue balance exceeds 1,000 with salary below 50,000.';
      if (salary < 60000 && inquiryPast30Days >= 12)
        return 'Over 12 inquiries in the past 30 days with salary below 60,000.';
      if (salary <= 25000 && inquiryPast30Days > 4)
        return 'More than 4 inquiries in the past 30 days with salary at or below 25,000.';
      if (PLAccounts <= 7 && (salary < 60000 || inquiryPast30Days > 4))
        return 'Less than or equal to 10 PL accounts with salary below 60,000 or more than 4 inquiries.';
      if (cibilScore == -1 || plScore == -1) return 'NTC - New to CIBIL user';

      return null;
    };

    const checkLowRisky = () => {
      if (inquiryPast30Days <= 2)
        return 'Low inquiries less or eqaul to 2 in the past 30 days.';
      if (typeOfDevice == '1') return 'Device type indicates low risk.';
      if (['Karnataka', 'Tamil Nadu', 'Telangana'].includes(aadhaarState))
        return 'Residence in Karnataka, Tamil Nadu, or Telangana.';
      if (plScore >= 800) return 'PL Score is greater or equal to 800.';
      if (cibilScore >= 795) return 'CIBIL Score greater or equal to 795.';
      if (salary >= 60000) return 'Salary above 60,000.';
      if (salary >= 26000 && inquiryPast30Days <= 4)
        return 'Salary above 26,000 with less than or eqaul 4 inquiries.';
      if (PLOutstanding >= 2000000) return 'PL Outstanding is above 20 lakhs.';
      if (cibilScore > 770 && plScore > 700 && inquiryPast30Days <= 5)
        return 'CIBIL above 770 and PL Score above 700 with less than or eqaul 5 inquiries.';
      if (
        salary >= 35000 &&
        ['Maharashtra', 'Kerala', 'Andhra Pradesh'].includes(aadhaarState)
      )
        return 'Salary ≥ 35,000 and residence in Maharashtra, Kerala, or Andhra Pradesh.';

      return null;
    };

    // Check if loan is risky
    const riskyReason = checkRisky();
    if (riskyReason) {
      return { type: 'RISKY', message: riskyReason };
    }

    // Check if loan is low risky
    const lowRiskyReason = checkLowRisky();
    if (lowRiskyReason) {
      return { type: 'LOW RISKY', message: lowRiskyReason };
    }

    // If neither condition is met
    return {
      type: 'NOT DECIDED',
      message: 'Loan type cannot be determined based on current data.',
    };
  }

  nbfcHighRiskType(reqData: ICibilEligibility): {
    type: 'None' | 'Risk_01';
    message?: string;
  } {
    if (reqData.salary >= 50000) return { type: 'None' };
    if (reqData.completedLoan != 0) return { type: 'None' };

    const isIos = reqData.typeOfDevice == '1';

    if (
      !isIos &&
      [
        'Bihar',
        'Delhi',
        'Odisha',
        'Orissa',
        'Madhya Pradesh',
        'Uttar Pradesh',
      ].includes(reqData.aadhaarState)
    ) {
      return { type: 'Risk_01', message: 'Non IOS, High risk Aadhaar state' };
    }

    if (!isIos && reqData.salary <= 30000) {
      return { type: 'Risk_01', message: 'Non IOS, Salary less than 30k' };
    }

    if (!isIos && reqData.cibilScore < 750 && reqData.plScore < 750) {
      return { type: 'Risk_01', message: 'Non IOS, CIBIL & PL below 750' };
    }

    if (!isIos && reqData.age < 25) {
      return { type: 'Risk_01', message: 'Non IOS, Age less than 25 years' };
    }

    if (!isIos && reqData.inqLast30Days >= 12) {
      return {
        type: 'Risk_01',
        message: 'Non IOS, Inq last 30 days more than 12',
      };
    }

    if (!isIos && reqData.zeroBalanceAccounts <= 15) {
      return {
        type: 'Risk_01',
        message: 'Non IOS, Zero balance accounts less than 15',
      };
    }

    if (!isIos && reqData.overDueDays > 0) {
      return { type: 'Risk_01', message: 'Non IOS, Overdue days more than 0' };
    }

    if (reqData.currentBalance < 50000) {
      return {
        type: 'Risk_01',
        message: 'Current balance less than 50k',
      };
    }

    if (!isIos && reqData.highCreditAmount < 1000000) {
      return {
        type: 'Risk_01',
        message: 'Non IOS, High credit amount less than 10L',
      };
    }

    return { type: 'None' };
  }

  // for second nbfc
  nbfcSecondCheckLoanType(reqData) {
    const {
      cibilScore,
      plScore,
      overdueBalance,
      inquiryPast30Days,
      salary,
      aadhaarState,
      typeOfDevice,
      lastLoanStatus,
    } = reqData;

    const checkRisky = () => {
      if (cibilScore >= GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE && cibilScore < 700)
        return 'CIBIL Score is less 700.';
      if (plScore >= GLOBAL_RANGES.MIN_IDEAL_PL_SCORE && plScore < 700)
        return 'CIBIL Score is less 700.';

      if (overdueBalance > 0) return 'Overdue balance above 0.';
      if (lastLoanStatus === 'LOAN_DELAYED') return `User's Last Loan Delayed`;

      return null;
    };

    const checkLowRisky = () => {
      if (inquiryPast30Days <= 2)
        return 'Low inquiries less or eqaul to 2 in the past 30 days.';
      if (typeOfDevice == '1') return 'Device type indicates low risk.';
      if (['Karnataka', 'Tamil Nadu', 'Telangana'].includes(aadhaarState))
        return 'Residence in Karnataka, Tamil Nadu, or Telangana.';
      if (plScore >= 810) return 'PL Score is greater or equal to 810.';

      if (salary >= 30000) return 'Salary above 30,000.';
      if (salary >= 25000 && inquiryPast30Days <= 4)
        return 'Salary above 25,000 with less than or eqaul 4 inquiries.';

      return null;
    };

    // Check if loan is risky
    const riskyReason = checkRisky();
    if (riskyReason) {
      return { type: 'RISKY', message: riskyReason };
    }

    // Check if loan is low risky
    const lowRiskyReason = checkLowRisky();
    if (lowRiskyReason) {
      return { type: 'LOW RISKY', message: lowRiskyReason };
    }

    // If neither condition is met
    return {
      type: 'NOT DECIDED',
      message: 'Loan type cannot be determined based on current data.',
    };
  }

  nbfcSecondIsEligibleOrNotLogic(reqData) {
    const {
      cibilScore,
      plScore,
      overdueBalance,
      completedLoans,
      PLAccounts,
      inquiryPast30Days,
      salary,
      ecsDetails,
    } = reqData;

    const isPLExceptionUser =
      completedLoans >= GLOBAL_RANGES.PL_EXCEPTION_MIN_COMPLETED_LOANS;
    let isEligible = true;
    let message = 'Eligible';

    if (cibilScore < 700 && plScore < 700) {
      isEligible = false;
      message = 'CIBIL and pl score is below 700.';
    } else if (cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE) {
      isEligible = false;
      message = 'CIBIL score is below 650.';
    } else if (
      plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE &&
      !isPLExceptionUser
    ) {
      isEligible = false;
      message =
        'PL score is below 650 and Repeat user with less than 3 completed loans';
    } else if (salary < 20000 && completedLoans > 0) {
      isEligible = false;
      message = 'Salary is less 20000';
    } else if (salary < 25000 && completedLoans == 0) {
      isEligible = false;
      message = 'Salary is less 20000';
    } else if (overdueBalance > 1000 && salary < 30000) {
      isEligible = false;
      message = 'Salary is less 20000 and over due balance is greater 1000';
    }
    // else if (ecsDetails.ecsBounceCount > 2) {
    //   isEligible = false;
    //   message = 'EcsBounceCount is greater 2';
    // }
    else if (inquiryPast30Days >= 12) {
      isEligible = false;
      message = 'Inquiry past 30 days is greater or equal 12';
    } else if (PLAccounts <= 10 && salary < 30000) {
      isEligible = false;
      message = 'Pl accounts is less or equal 10 and salary is less 30000';
    }

    return { isEligible, message };
  }

  nbfcSecondCibilManualVerificationLogic(reqData) {
    const { ecsDetails, loanType } = reqData;
    let isManual = false;
    let message = 'No manual verification required'; // Default message

    if (loanType === 'RISKY') {
      isManual = true;
      message = 'User is Risky';
    } else if (ecsDetails?.ecsBounceCount > 2) {
      isManual = true;
      message = 'EcsBounceCount is greater 2';
    }
    return { isManual, message };
  }

  nbfcSecondCheckSalaryPortion(reqData) {
    const {
      overdueBalance,
      inquiryPast30Days,
      salary,
      aadhaarState,
      typeOfDevice,
      lastLoanStatus,
      previousloanAmt,
      loanType,
    } = reqData;
    let portion = 30;
    let eligibleAmount = previousloanAmt ?? 0;
    let maxAmount = 0;
    let message = 'Base portion applied'; // Default message

    let isAmtIncrement = false;
    isAmtIncrement = lastLoanStatus === 'LOAN_ON_TIME';
    if (typeOfDevice == '1') {
      portion = 50;
      message = `User's ios device`;
    } else if (salary >= 30000) {
      portion = 50;
      message = `Salary is greater or equal 30000`;
    } else if (
      ['Karnataka', 'Tamil Nadu', 'Telangana'].includes(aadhaarState)
    ) {
      portion = 50;
      message = `User's state is ${aadhaarState} `;
    } else if (inquiryPast30Days <= 4 && salary >= 25) {
      portion = 50;
      message =
        'Inquiry past 30 days is less or equal 4 and salary is greater or equal 25000';
    }

    // 10% increment creteria
    if (isAmtIncrement) {
      if (overdueBalance == 0) {
        eligibleAmount = previousloanAmt + (10 / 100) * previousloanAmt;
        message += ' and 10% increment';
      } else {
        message += ' but is not increment 10% due to overdueBalance';
      }

      // maxLoanAmount not over max salary portion
      if (loanType === 'RISKY') portion = 50;
      else if (loanType === 'LOW RISKY') portion = 120;
      else if (loanType === 'NOT DECIDED') portion = 75;
    } else if (lastLoanStatus === 'LOAN_DELAYED') {
      // if user's last loan is delay and isExist on risky category
      if (loanType === 'RISKY') {
        eligibleAmount = 7500;
        message = `User is risky that's by, user eligible only for 7500`;
      }
    }

    //check not get over max loan amount
    maxAmount = (salary * portion) / 100;
    if (eligibleAmount > maxAmount) eligibleAmount = maxAmount;

    return { eligibleAmount, portion, message };
  }

  nbfcSecondInterestRateLogic(reqData) {
    const { cibilScore, plScore, overdueBalance, loanType, lastLoanStatus } =
      reqData;

    let annum = 108;
    let message = 'Default rate applied';
    const MAX_CIBIL_SCORE = 749;
    const MAX_PL_SCORE = 749;

    // Decision Logic
    if (loanType === 'RISKY') {
      annum = 108;
      message = 'Risky loan type';
    } else if (lastLoanStatus === 'LOAN_DELAYED') {
      annum = 108;
      message = 'Last loan was delayed';
    } else if (overdueBalance > 0) {
      annum = 108;
      message = 'Overdue balance present';
    } else if (cibilScore <= MAX_CIBIL_SCORE || plScore <= MAX_PL_SCORE) {
      annum = 108;
      message = 'Low CIBIL score or Low PL score';
    } else if (
      cibilScore >= 750 &&
      plScore >= 750 &&
      (cibilScore <= 799 || plScore <= 799)
    ) {
      annum = 72;
      message = 'Cibil or pl scores greater 750 and less 799';
    } else if (
      cibilScore >= 800 &&
      plScore >= 800 &&
      (cibilScore < GLOBAL_RANGES.MAX_PREMIUM_CIBIL_SCORE ||
        plScore < GLOBAL_RANGES.MAX_PREMIUM_PL_SCORE)
    ) {
      annum = 36;
      message = 'Scores between 800 and 829';
    } else if (
      cibilScore >= GLOBAL_RANGES.MAX_PREMIUM_CIBIL_SCORE &&
      plScore >= GLOBAL_RANGES.MAX_PREMIUM_PL_SCORE
    ) {
      annum = 28;
      message = 'Scores above or equal 830';
    } else {
      annum = 108;
      message = 'No interest rate scenario match';
    }

    return { annum, message };
  }

  nbfcFirstExperianManualVerificationLogic(reqData) {
    const overdueAccounts = reqData?.overdueAccounts;

    let isManual = false;
    let message = 'No manual verification required'; // Default message

    // Decision Logic with Messages Including "Manual"
    if (overdueAccounts && overdueAccounts > 0) {
      isManual = true;
      message = 'Overdue account detected in Experian data';
    }

    return { isManual, message };
  }

  nbfcSecondExperianManualVerificationLogic(reqData) {
    const { overdueAccounts } = reqData;

    let isManual = false;
    let message = 'No manual verification required'; // Default message

    // Decision Logic with Messages Including "Manual"
    if (overdueAccounts > 0) {
      isManual = true;
      message = 'Overdue account detected in Experian data';
    }

    return { isManual, message };
  }

  async CFLScoreForApproval(query) {
    try {
      // if (!gIsPROD) return {};
      const loanId = query?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const url =
        EnvConfig.network.nAdminBackendDataUrl +
        'data-codes/calculateInternalScore';
      const response = await this.apiService.requestPost(url, query);
      if (response == k500Error) return kInternalError;
      return response;
    } catch (error) {
      return kInternalError;
    }
  }

  async calculateLeadScore(query) {
    try {
      if (!gIsPROD) return {};
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const url =
        EnvConfig.network.nAdminBackendDataUrl +
        'admin/user/calculateLeadScore';
      const response = await this.apiService.requestPost(url, query);
      if (response == k500Error) return kInternalError;
      return response;
    } catch (error) {
      return kInternalError;
    }
  }

  async checkPreApproval(user_id: string) {
    try {
      console.log(
        'Hitting checkPreApproval -> ',
        user_id,
        nAdminBackend.eligibility.checkPreApprovalStatus,
      );
      const preApprove = await this.apiService
        .requestPost(nAdminBackend.eligibility.checkPreApprovalStatus, {
          user_id,
        })
        .catch((err) => {
          console.log({ err });
        });

      return preApprove;
    } catch (error) {
      console.log(error);
    }
  }

  async exceptionCaseOfEligibilty(reqData) {
    const {
      cibilScore,
      plScore,
      overdueBalance,
      inquiryPast30Days,
      salary,
      aadhaarState,
      typeOfDevice,
      PLAccounts,
      loanType,
      completedLoans,
      CFLScore,
      past6MonthDelay,
      PLOutstanding,
      currentBalance,
      totalAccounts,
    } = reqData;

    if (loanType != 'LOW RISKY')
      return { message: 'Not matching exception case', isEligible: false };
    else if (salary < 120000)
      return { message: 'Not matching exception case', isEligible: false };
    else if (inquiryPast30Days <= 1 && typeOfDevice == '1' && salary >= 150000)
      return { message: 'exception case 1', isEligible: true };
    else if (plScore > 800 && typeOfDevice == '1' && salary >= 120000)
      return { message: 'exception case 2', isEligible: true };
    else if (
      cibilScore >= 760 &&
      plScore >= 760 &&
      typeOfDevice == '1' &&
      inquiryPast30Days <= 10 &&
      salary >= 150000
    )
      return { message: 'exception case 3', isEligible: true };
    else if (
      cibilScore >= 750 &&
      plScore >= 750 &&
      PLAccounts >= 30 &&
      inquiryPast30Days <= 8 &&
      salary >= 200000
    )
      return { message: 'exception case 4', isEligible: true };
    else if (
      cibilScore >= 750 &&
      plScore >= 750 &&
      PLAccounts >= 30 &&
      inquiryPast30Days <= 4 &&
      salary >= 120000 &&
      PLOutstanding >= 2000000
    )
      return { message: 'exception case 5', isEligible: true };
    else if (
      cibilScore >= 750 &&
      plScore >= 750 &&
      inquiryPast30Days <= 12 &&
      typeOfDevice == '1' &&
      salary > 120000
    )
      return { message: 'exception case 6', isEligible: true };
    else if (
      cibilScore >= 760 &&
      plScore >= 760 &&
      PLAccounts >= 30 &&
      inquiryPast30Days <= 8 &&
      ['Maharashtra', 'Karnataka', 'Telangana'].includes(aadhaarState) &&
      salary > 150000
    )
      return { message: 'exception case 7', isEligible: true };
    else if (
      salary >= 120000 &&
      cibilScore >= 750 &&
      plScore >= 750 &&
      PLAccounts >= 30 &&
      inquiryPast30Days <= 4
    )
      return { message: 'exception case 8', isEligible: true };
    else if (
      typeOfDevice == '1' &&
      salary >= 120000 &&
      cibilScore >= 750 &&
      plScore >= 750 &&
      completedLoans > 1
    )
      return { message: 'exception case 9', isEligible: true };
    else if (plScore >= 750 && salary >= 120000 && completedLoans > 2)
      return { message: 'exception case 10', isEligible: true };
    else if (plScore >= 700 && salary >= 200000 && completedLoans > 2)
      return { message: 'exception case 11', isEligible: true };
    else if (
      salary >= 120000 &&
      cibilScore >= 750 &&
      plScore >= 750 &&
      completedLoans > 2 &&
      currentBalance > 50000
    )
      return { message: 'exception case 12', isEligible: true };
    else if (
      salary >= 120000 &&
      totalAccounts > 110 &&
      CFLScore > 120 &&
      past6MonthDelay === 0
    )
      return { message: 'exception case 13', isEligible: true };
    else if (salary >= 120000 && CFLScore > 140 && completedLoans >= 2)
      return { message: 'exception case 14', isEligible: true };
    else return { message: 'Not matching exception case', isEligible: false };
  }

  async isEligibleForPreApproval(reqData) {
    if (!reqData.userId) return kParamMissing('userId');
    if (!isUUID(reqData.userId)) return kInvalidParamValue('userId');

    const masterInclude = {
      attributes: ['otherInfo'],
      model: MasterEntity,
    };
    const kycInclude = {
      attributes: [
        'id',
        'aadhaarDOB',
        'aadhaarState',
        'aadhaarAddress',
        'aadhaarAddressResponse',
      ],
      model: KYCEntity,
    };
    const include = [kycInclude, masterInclude];

    const userOptions = { include, where: { id: reqData.userId } };

    const userData = await this.userRepo.getRowWhereData(
      ['id', 'typeOfDevice', 'email', 'completedLoans', 'kycId'],
      userOptions,
    );

    if (!userData) return k422ErrorMessage(kNoDataFound);
    if (userData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    // Get cibil data
    // Query preparation
    const cibilAttr = [
      'cibilScore',
      'currentBalance',
      'highCreditAmount',
      'overdueAccounts',
      'overdueBalance',
      'plScore',
      'totalOverdueDays',
      'PLAccounts',
      'inquiryPast30Days',
      'inquiryPast12Months',
      'accounts',
      'PLOutstanding',
      'zeroBalanceAccounts',
      'past6MonthDelay',
      'currentBalance',
      'totalAccounts',
    ];
    const cibilOptions = {
      order: [['id', 'DESC']],
      where: { loanId: userData.kycId, userId: reqData.userId },
    };

    // Query
    const cibilData = await this.repoManager.getRowWhereData(
      CibilScoreEntity,
      cibilAttr,
      cibilOptions,
    );

    // Validate query data
    if (cibilData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    else if (!cibilData) return k422ErrorMessage(kNoDataFound);
    let aadhaarState = userData?.kycData?.aadhaarState;
    if (!aadhaarState) {
      const aadhaarAddress = await this.typeService.getAadhaarAddress(
        userData?.kycData,
      );
      aadhaarState = aadhaarAddress?.state;
    }

    const aadhaarDOB = await this.typeService.getDateAsPerAadhaarDOB(
      userData?.kycData?.aadhaarDOB,
    );
    const today = this.typeService.getGlobalDate(new Date());
    const age = this.typeService.dateDifference(aadhaarDOB, today, 'Years');
    console.log({ masterData: userData?.masterData });

    const userEnteredSalary = userData?.masterData?.otherInfo?.salaryInfo;
    const reqCibilData = {
      ...cibilData,
      userId: reqData.userId,
      completedLoans: userData.completedLoans ?? 0,
      salary: userEnteredSalary ?? reqData?.approvedSalary,
      typeOfDevice: userData?.typeOfDevice,
      email: userData?.email,
      aadhaarState,
      age,
      // experianData,
    };

    return this.nbfcFirstIsEligibleOrNotLogic(reqCibilData);
  }
}

import { EnvConfig } from 'src/configs/env.config';
import { HOST_URL } from 'src/constants/globals';

export function kBasicDetailsScreen(
  communicationLanguage: boolean,
  purposeList,
  bankList,
) {
  return {
    showAppBar: true,
    appBarColor: '0xffF1F1F1',
    appBarElevation: 0,
    appBarLeading: {
      type: 9,
      key: 'back',
      iconCodePoint: '62832',
      color: '0x7f040404',
      size: 18,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    appBarAction: [
      {
        type: 10,
        key: 'helpButton',
        padding: { right: 16 },
        svgURL:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/May-2025/*************.svg',
        clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
      },
    ],
    surfaceTintColor: '0xffF1F1F1',
    backgroundColor: '0xffF1F1F1',
    body: {
      type: 17,
      key: 'body',
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 4,
        key: 'view',
        crossAxisAlignment: 2,
        children: [
          {
            type: 19,
            key: 'title',
            padding: { top: 15 },
            texts: [
              {
                text: 'Basic details',
                style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
              },
            ],
          },
          {
            type: 19,
            key: 'description',
            padding: { top: 8 },
            textAlign: 1,
            texts: [
              {
                text: 'Provide the required basic details.',
                style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
              },
            ],
          },
          {
            type: 5,
            key: 'fields',
            color: '0xffffffff',
            borderRadius: {
              bottomLeft: 15,
              bottomRight: 15,
              topLeft: 15,
              topRight: 15,
            },
            padding: { left: 15, top: 15, right: 15, bottom: 15 },
            margin: { top: 25 },
            child: {
              type: 4,
              key: 'widgets',
              children: [
                {
                  type: 11,
                  key: 'email',
                  isEmailPicker: true,
                  labelText: 'Email ID',
                  hintText: 'Enter your valid email address',
                  formatters: [2],
                  keyboardType: 2,
                  infoMsg: 'Verification link will be sent on this email',
                  validations: {
                    regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                    error: 'Please enter a valid email ID',
                  },
                },
                {
                  type: 11,
                  key: 'pan',
                  padding: { top: 8, bottom: 8 },
                  labelText: 'PAN number',
                  hintText: 'Enter your PAN number',
                  maxLength: 10,
                  formatters: [4],
                  keyboardType: 9,
                  textCapitalization: 1,
                  validations: {
                    regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                    error: 'Please enter a valid PAN number',
                  },
                },
                {
                  type: 7,
                  key: 'purposeId',
                  labelText: 'Loan purpose',
                  options: purposeList,
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a loan purpose',
                  },
                },
                ...(!communicationLanguage
                  ? [
                      {
                        type: 7,
                        key: 'communicationLanguage',
                        padding: { top: 8 },
                        labelText: 'Mode of communication',
                        options: [
                          { id: 1, value: 'English' },
                          { id: 2, value: 'Hindi' },
                        ],
                        validations: {
                          regex: '^(?!s*$).+',
                          error: 'Please select your communication language',
                        },
                      },
                    ]
                  : []),
                {
                  type: 7,
                  key: 'politicallyExposed',
                  padding: { top: 8 },
                  labelText: 'Politically exposed',
                  options: [
                    { id: 0, value: 'No' },
                    { id: 1, value: 'Yes' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your political exposition status',
                  },
                },
                {
                  type: 7,
                  key: 'empInfo',
                  padding: { top: 8 },
                  labelText: 'Employment status',
                  options: [
                    { id: 'Salaried', value: 'Salaried' },
                    { id: 'Consultant', value: 'Consultant' },
                    { id: 'Self-Employed', value: 'Self-Employed' },
                    { id: 'Retired', value: 'Retired' },
                    { id: 'Student', value: 'Student' },
                    { id: 'Homemaker', value: 'Homemaker' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select an employment status',
                  },
                },
                {
                  type: 7,
                  key: 'maritalInfo',
                  padding: { top: 8 },
                  labelText: 'Marital status',
                  options: [
                    {
                      id: 'Single',
                      value: 'Single',
                      subFieldKey: 'motherName',
                      subField: {
                        type: 11,
                        key: 'motherName',
                        padding: { top: 8 },
                        labelText: 'Mother name',
                        hintText: 'Enter your mother name',
                        formatters: [7],
                        keyboardType: 4,
                        textCapitalization: 4,
                        validations: { error: 'Please enter mother name' },
                      },
                    },
                    {
                      id: 'Married',
                      value: 'Married',
                      subFieldKey: 'spouseName',
                      subField: {
                        type: 11,
                        key: 'spouseName',
                        padding: { top: 8 },
                        labelText: 'Spouse name',
                        hintText: 'Enter your spouse name',
                        formatters: [7],
                        keyboardType: 4,
                        textCapitalization: 4,
                        validations: { error: 'Please enter spouse name' },
                      },
                    },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your marital status',
                  },
                },
                {
                  type: 7,
                  key: 'vehicleInfo',
                  padding: { top: 8 },
                  labelText: 'Vehicle',
                  options: [
                    { id: 'Two wheeler', value: 'Two wheeler' },
                    { id: 'Four wheeler', value: 'Four wheeler' },
                    {
                      id: 'Two wheeler, Four wheeler',
                      value: 'I own both a 2-wheeler and a 4-wheeler',
                    },
                    { id: 'None', value: 'None' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a vehicle',
                  },
                },
                {
                  type: 7,
                  key: 'educationInfo',
                  padding: { top: 8 },
                  labelText: 'Education',
                  options: [
                    {
                      id: 'Less than High School',
                      value: 'Less than High School',
                    },
                    {
                      id: 'Completed High School',
                      value: 'Completed High School',
                    },
                    { id: 'Diploma', value: 'Diploma' },
                    { id: 'Graduate Degree', value: 'Graduate Degree' },
                    { id: 'Postgraduate Degree', value: 'Postgraduate Degree' },
                    { id: 'Masters Degree', value: 'Masters Degree' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your education',
                  },
                },
                {
                  type: 7,
                  key: 'residentialInfo',
                  padding: { top: 8 },
                  labelText: 'Residential status',
                  options: [
                    { id: 'Owned', value: 'Owned' },
                    { id: 'Parental', value: 'Parental' },
                    { id: 'Company provided', value: 'Company provided' },
                    { id: 'Rented', value: 'Rented' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your education',
                  },
                },
                {
                  type: 1,
                  key: 'bankId',
                  isSearch: true,
                  padding: { top: 8 },
                  labelText: 'Salary bank',
                  title: 'Salary bank account',
                  description:
                    'Select bank where your salary is being deposited',
                  options: bankList,
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a bank',
                  },
                },
              ],
            },
          },
          {
            type: 2,
            key: 'continue',
            title: 'Continue',
            padding: { top: 20 },
            apiData: {
              endpoint: `${HOST_URL}v4/user/submitNewUserDetails`,
              requestType: 'POST',
              ansKeys: [
                {
                  key: 'email',
                  validations: {
                    regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                    error: 'Please enter a valid email ID',
                  },
                },
                {
                  key: 'pan',
                  validations: {
                    regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                    error: 'Please enter a valid PAN number',
                  },
                },
                { key: 'purposeId' },
                ...(!communicationLanguage
                  ? [{ key: 'communicationLanguage' }]
                  : []),
                { key: 'politicallyExposed' },
                { key: 'empInfo' },
                {
                  key: 'maritalInfo',
                  hasSubKey: true,
                  subKeys: [
                    {
                      key: 'motherName',
                      validations: { error: 'Please enter mother name' },
                    },
                    {
                      key: 'spouseName',
                      validations: { error: 'Please enter spouse name' },
                    },
                  ],
                },
                { key: 'vehicleInfo' },
                { key: 'educationInfo' },
                { key: 'residentialInfo' },
                { key: 'bankId' },
                {
                  key: 'bankId',
                  hasSubKey: true,
                  subKeys: [{ key: 'bankStatement' }],
                },
              ],
              syncUserData: true,
              submitCurrentQuestion: true,
              isRedirect: true,
              canClearState: true,
            },
          },
        ],
      },
    },
  };
}

export function kLspBasicDetailsScreen(
  communicationLanguage,
  purposeList,
  bankList,
) {
  return {
    showAppBar: true,
    appBarActions: [
      {
        type: 17,
        key: 'helpButton',
        padding: { right: 16 },
        texts: [
          {
            text: 'HELP',
            style: { color: '0xff128391', fontSize: 14, fontWeight: 5 },
            clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
          },
        ],
      },
    ],
    appBarColor: '0xffFFFFFF',
    appBarElevation: 0,
    appBarLeading: {
      type: 7,
      key: 'back',
      iconCodePoint: '62834',
      color: '0xff020C0D',
      size: 28,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffFFFFFF',
    backgroundColor: '0xffFFFFFF',
    body: {
      type: 15,
      key: 'body',
      physics: 3,
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 3,
        key: 'view',
        crossAxisAlignment: 4,
        children: [
          {
            type: 17,
            key: 'title',
            texts: [
              {
                text: 'Basic details',
                style: { color: '0xff020C0D', fontSize: 26, fontWeight: 6 },
              },
            ],
          },
          {
            type: 17,
            key: 'description',
            texts: [
              {
                text: 'To proceed with your loan application, we need some basic details about you.',
                style: { color: '0xff718589', fontSize: 14, fontWeight: 5 },
              },
            ],
          },
          {
            type: 18,
            key: 'separator',
            padding: { top: 30 },
            physics: 5,
            fields: [
              {
                type: 9,
                key: 'email',
                isEmailPicker: true,
                labelText: 'Email ID',
                hintText: 'Enter your valid email address',
                formatters: [2],
                keyboardType: 2,
                infoMsg:
                  'To confirm your email address, we will send a verification link to this email',
                validations: {
                  regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                  error: 'Please enter a valid email ID',
                },
              },
              {
                type: 9,
                key: 'pan',
                padding: { top: 15 },
                labelText: 'PAN number',
                hintText: 'Enter your PAN number',
                maxLength: 10,
                formatters: [4],
                keyboardType: 9,
                textCapitalization: 1,
                infoField: {
                  type: 8,
                  key: 'panInfo',
                  svgURL:
                    'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Feb-2025/1738732609779.svg',
                  rWidth: 1.0,
                },
                validations: {
                  regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                  error: 'Please enter a valid PAN number',
                },
              },
              {
                type: 10,
                key: 'purposeId',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/loan_purpose.svg',
                labelText: 'Loan purpose',
                title: 'Loan purpose',
                description: 'Select your loan purpose',
                options: purposeList,
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a loan purpose',
                },
              },
              ...(!communicationLanguage
                ? [
                    {
                      type: 10,
                      key: 'communicationLanguage',
                      padding: { top: 15 },
                      svgIcon:
                        'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/mode_of_communication(1).svg',
                      labelText: 'Mode of communication',
                      title: 'Mode of communication',
                      description: 'Language comfortable for you',
                      options: [
                        { id: 1, value: 'English' },
                        { id: 2, value: 'Hindi' },
                      ],
                      validations: {
                        regex: '^(?!s*$).+',
                        error: 'Please select your communication language',
                      },
                    },
                  ]
                : []),
              {
                type: 10,
                key: 'politicallyExposed',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/politically_exposed.svg',
                labelText: 'Politically exposed',
                title: 'Politically exposed?',
                description: 'Are you a politically exposed person?',
                options: [
                  { id: 0, value: 'No' },
                  { id: 1, value: 'Yes' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your political exposition status',
                },
              },
              {
                type: 10,
                key: 'empInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/employment_status.svg',
                labelText: 'Employment status',
                title: 'Your employment status',
                description: 'Little info about your work!',
                options: [
                  { id: 'Salaried', value: 'Salaried' },
                  { id: 'Consultant', value: 'Consultant' },
                  { id: 'Self-Employed', value: 'Self-Employed' },
                  { id: 'Retired', value: 'Retired' },
                  { id: 'Student', value: 'Student' },
                  { id: 'Homemaker', value: 'Homemaker' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select an employment status',
                },
              },
              {
                type: 10,
                key: 'maritalInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/marital_status.svg',
                labelText: 'Marital status',
                title: 'Your marital status',
                description: 'Select your current relationship status',
                options: [
                  {
                    id: 'Single',
                    value: 'Single',
                    subFieldKey: 'motherName',
                    subField: {
                      type: 9,
                      key: 'motherName',
                      padding: { top: 15 },
                      labelText: 'Mother name',
                      hintText: 'Enter your mother name',
                      formatters: [7],
                      keyboardType: 4,
                      textCapitalization: 4,
                      validations: { error: 'Please enter mother name' },
                    },
                  },
                  {
                    id: 'Married',
                    value: 'Married',
                    subFieldKey: 'spouseName',
                    subField: {
                      type: 9,
                      key: 'spouseName',
                      padding: { top: 15 },
                      labelText: 'Spouse name',
                      hintText: 'Enter your spouse name',
                      formatters: [7],
                      keyboardType: 4,
                      textCapitalization: 4,
                      validations: { error: 'Please enter spouse name' },
                    },
                  },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your marital status',
                },
              },
              {
                type: 10,
                key: 'vehicleInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/vehical_ownership.svg',
                labelText: 'Vehicle',
                title: 'Vehicle ownership',
                description: 'Please select your vehicle type',
                options: [
                  { id: 'Two wheeler', value: 'Two wheeler' },
                  { id: 'Four wheeler', value: 'Four wheeler' },
                  {
                    id: 'Two wheeler, Four wheeler',
                    value: 'I own both a 2-wheeler and a 4-wheeler',
                  },
                  { id: 'None', value: 'None' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a vehicle',
                },
              },
              {
                type: 10,
                key: 'educationInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/education_deatils.svg',
                labelText: 'Education',
                title: 'Educational background',
                description: 'What is your highest level of education?',
                options: [
                  {
                    id: 'Less than High School',
                    value: 'Less than High School',
                  },
                  {
                    id: 'Completed High School',
                    value: 'Completed High School',
                  },
                  { id: 'Diploma', value: 'Diploma' },
                  { id: 'Graduate Degree', value: 'Graduate Degree' },
                  { id: 'Postgraduate Degree', value: 'Postgraduate Degree' },
                  { id: 'Masters Degree', value: 'Masters Degree' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your education',
                },
              },
              {
                type: 10,
                key: 'residentialInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/residential_status.svg',
                labelText: 'Residential status',
                title: 'Your residential status',
                description: 'Your primary housing arrangement',
                options: [
                  { id: 'Owned', value: 'Owned' },
                  { id: 'Parental', value: 'Parental' },
                  { id: 'Company provided', value: 'Company provided' },
                  { id: 'Rented', value: 'Rented' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your education',
                },
              },
              {
                type: 10,
                key: 'bankId',
                padding: { top: 15 },
                isSearch: true,
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/salary_bank(2).svg',
                labelText: 'Salary bank',
                title: 'Salary bank account',
                description: 'Select bank where your salary is being deposited',
                options: bankList,
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a bank',
                },
              },
            ],
          },
        ],
      },
    },
    bottomNavigationBar: {
      type: 4,
      key: 'bottomBar',
      height: 85,
      rWidth: 1.0,
      color: '0xffFFFFFF',
      border: {
        top: { color: '0x8095B0B5', width: 0.5 },
      },
      child: {
        type: 1,
        key: 'continue',
        padding: { top: 16, left: 16, right: 16, bottom: 16 },
        title: 'Continue',
        apiData: {
          endpoint: `${EnvConfig.url.lspBaseLink}/v4/user/submitNewUserDetails`,
          requestType: 'POST',
          ansKeys: [
            {
              key: 'email',
              validations: {
                regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                error: 'Please enter a valid email ID',
              },
            },
            {
              key: 'pan',
              validations: {
                regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                error: 'Please enter a valid PAN number',
              },
            },
            { key: 'purposeId' },
            ...(!communicationLanguage
              ? [{ key: 'communicationLanguage' }]
              : []),
            { key: 'politicallyExposed' },
            { key: 'empInfo' },
            {
              key: 'maritalInfo',
              hasSubKey: true,
              subKeys: [
                {
                  key: 'motherName',
                  validations: { error: 'Please enter mother name' },
                },
                {
                  key: 'spouseName',
                  validations: { error: 'Please enter spouse name' },
                },
              ],
            },
            { key: 'vehicleInfo' },
            { key: 'educationInfo' },
            { key: 'residentialInfo' },
            { key: 'bankId' },
            {
              key: 'bankId',
              hasSubKey: true,
              subKeys: [{ key: 'bankStatement' }],
            },
          ],
          syncUserData: true,
          submitCurrentQuestion: true,
          isRedirect: true,
          canClearState: true,
          loaderData: {
            icon: '',
            title: 'Setting Up Your Account',
            description:
              "We're saving your details and preparing your account for the next step",
          },
        },
      },
    },
  };
}

export function KPreApprovedLoanScreenObject(userId, preApprovalAmt) {
  return {
    // MARK: App Bar
    showAppBar: true,
    appBarColor: '0xffF1F1F1',
    appBarElevation: 0,
    appBarLeading: {
      type: 9,
      key: 'backBtn',
      iconCodePoint: '62832',
      color: '0xff000000',
      size: 23,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffF1F1F1',
    backgroundColor: '0xffF1F1F1',
    appBarAction: [
      {
        type: 10,
        key: 'helpButton',
        padding: { right: 16 },
        svgURL:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/May-2025/*************.svg',
        clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
      },
    ],
    // MARK: Body
    body: {
      type: 17,
      key: 'body',
      padding: { left: 20, top: 0, right: 20, bottom: 15 },
      child: {
        type: 4,
        key: 'columnView',
        crossAxisAlignment: 4,
        children: [
          // MARK: Title
          {
            type: 19,
            key: 'title',
            texts: [
              {
                text: 'Curated Loan Options',
                style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
              },
            ],
          },
          // MARK: Description
          {
            type: 19,
            key: 'description',
            padding: { top: 3, bottom: 15 },
            texts: [
              {
                text: 'One decision, two great choices',
                style: { color: '0xff000000', fontSize: 12, fontWeight: 4 },
              },
            ],
          },
          // MARK: Pre Approved Card
          {
            type: 22,
            key: 'preAppStackCard',
            padding: { top: 15, bottom: 15 },
            alignment: 7,
            children: [
              {
                type: 5,
                key: 'preApprovedMainCard',
                margin: { top: 6 },
                padding: { left: 4, top: 4, right: 4, bottom: 4 },
                borderRadius: {
                  bottomLeft: 6,
                  bottomRight: 6,
                  topLeft: 6,
                  topRight: 6,
                },
                boxShadow: {
                  color: '0x26000000',
                  blurRadius: 1,
                  spreadRadius: 0,
                  x: 1,
                  y: 1,
                },
                rWidth: 1,
                child:
                  // MARK: Pre Approved Inner card
                  {
                    type: 5,
                    key: 'preApprovedSubCard',
                    padding: { left: 14, top: 14, right: 14, bottom: 14 },
                    borderRadius: {
                      bottomLeft: 4,
                      bottomRight: 4,
                      topLeft: 4,
                      topRight: 4,
                    },
                    gradient: {
                      gradientColors: ['0xffFFFFFF', '0xffE2EAFF'],
                      begin: 7,
                      end: 1,
                    },
                    rWidth: 1,
                    child: {
                      type: 4,
                      key: 'preApprovedColumn',
                      crossAxisAlignment: 4,
                      children: [
                        // MARK: First Row
                        {
                          type: 15,
                          crossAxisAlignment: 3,
                          mainAxisAlignment: 4,
                          padding: { top: 30 },
                          children: [
                            // Just Tap Chip
                            {
                              type: 5,
                              key: 'justTapChip',
                              margin: { bottom: 5 },
                              padding: {
                                left: 10,
                                top: 8,
                                right: 10,
                                bottom: 8,
                              },
                              borderRadius: {
                                bottomLeft: 20,
                                bottomRight: 20,
                                topLeft: 20,
                                topRight: 20,
                              },
                              gradient: {
                                gradientColors: ['0x1A053ED1', '0x00053ED1'],
                                begin: 5,
                                end: 6,
                              },
                              child: {
                                type: 19,
                                key: 'justTapText',
                                texts: [
                                  {
                                    text: 'Just tap & Cash in',
                                    style: {
                                      color: '0xff053ED1',
                                      fontSize: 10,
                                      fontWeight: 5,
                                    },
                                  },
                                ],
                              },
                            },
                            {
                              type: 10,
                              key: 'preAppImg',
                              height: 76,
                              imageURL:
                                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626206369.webp',
                            },
                          ],
                        },
                        // MARK: Text Column
                        {
                          type: 19,
                          key: 'preAppCardText1',
                          padding: { top: 10 },
                          texts: [
                            {
                              text: 'No waiting, No hassle',
                              style: {
                                color: '0xff000000',
                                fontSize: 12,
                                fontWeight: 4,
                              },
                            },
                          ],
                        },
                        {
                          type: 19,
                          key: 'preAppCardText2',
                          padding: { bottom: 20 },
                          texts: [
                            {
                              text: `Pre-approved loan of ${preApprovalAmt}`,
                              style: {
                                color: '0xff000000',
                                fontSize: 16,
                                fontWeight: 6,
                              },
                            },
                          ],
                        },
                        // MARK: Continue BTN
                        {
                          type: 2,
                          key: 'preApproveApplyNow',
                          title: 'Apply Now',
                          apiData: {
                            endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                            requestType: 'POST',
                            syncUserData: true,
                            isRedirect: true,
                            canClearState: true,
                            extraData: {
                              isPreApproval: true,
                              userId,
                            },
                          },
                        },
                      ],
                    },
                  },
              },
              {
                type: 10,
                key: 'blueTagImg',
                height: 26,
                imageURL:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751627658459.webp',
              },
              {
                type: 19,
                key: 'blueTagText',
                padding: { top: 5 },
                texts: [
                  {
                    text: 'READY TO DISBURSE',
                    style: {
                      color: '0xffFFFFFF',
                      fontSize: 10,
                      fontWeight: 6,
                    },
                  },
                ],
              },
            ],
          },
          // MARK: Upgrade Loan Card
          {
            type: 22,
            key: 'preAppStackCard',
            padding: { top: 15 },
            alignment: 7,
            children: [
              {
                type: 5,
                key: 'upgradeLoanMainCard',
                margin: { top: 6 },
                padding: { left: 4, top: 4, right: 4, bottom: 4 },
                borderRadius: {
                  bottomLeft: 6,
                  bottomRight: 6,
                  topLeft: 6,
                  topRight: 6,
                },
                boxShadow: {
                  color: '0x26000000',
                  blurRadius: 1,
                  spreadRadius: 0,
                  x: 1,
                  y: 1,
                },
                rWidth: 1,
                child:
                  // MARK: Upgrade Loan Inner card
                  {
                    type: 5,
                    key: 'upgradeLoanSubCard',
                    padding: { left: 14, top: 14, right: 14, bottom: 14 },
                    borderRadius: {
                      bottomLeft: 4,
                      bottomRight: 4,
                      topLeft: 4,
                      topRight: 4,
                    },
                    gradient: {
                      gradientColors: ['0xffFFFFFF', '0xffEAFFEA'],
                      begin: 7,
                      end: 1,
                    },
                    rWidth: 1,
                    child: {
                      type: 4,
                      key: 'upgradeLoanColumn',
                      crossAxisAlignment: 4,
                      children: [
                        // MARK: First Row
                        {
                          type: 15,
                          crossAxisAlignment: 3,
                          mainAxisAlignment: 4,
                          padding: { top: 30 },
                          children: [
                            // Just Tap Chip
                            {
                              type: 5,
                              key: 'quickVerifyChip',
                              margin: { bottom: 5 },
                              padding: {
                                left: 10,
                                top: 8,
                                right: 10,
                                bottom: 8,
                              },
                              borderRadius: {
                                bottomLeft: 20,
                                bottomRight: 20,
                                topLeft: 20,
                                topRight: 20,
                              },
                              gradient: {
                                gradientColors: ['0x1A008800', '0x00008800'],
                                begin: 5,
                                end: 6,
                              },
                              child: {
                                type: 19,
                                key: 'quickVerifyText',
                                texts: [
                                  {
                                    text: 'Quick Verification',
                                    style: {
                                      color: '0xff008800',
                                      fontSize: 10,
                                      fontWeight: 5,
                                    },
                                  },
                                ],
                              },
                            },
                            {
                              type: 10,
                              key: 'upgradeLoanImg',
                              height: 80,
                              imageURL:
                                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751627735881.webp',
                            },
                          ],
                        },
                        // MARK: Text Column
                        {
                          type: 19,
                          key: 'upgradeLoanText1',
                          padding: { top: 10 },
                          texts: [
                            {
                              text: 'Validate Employment & Income to',
                              style: {
                                color: '0xff000000',
                                fontSize: 12,
                                fontWeight: 4,
                              },
                            },
                          ],
                        },
                        {
                          type: 19,
                          key: 'upgradeLoanText2',
                          padding: { bottom: 20 },
                          texts: [
                            {
                              text: 'Access up to ₹3,00,000',
                              style: {
                                color: '0xff000000',
                                fontSize: 16,
                                fontWeight: 6,
                              },
                            },
                          ],
                        },
                        // MARK: Continue BTN
                        {
                          type: 2,
                          key: 'upgradeLoanApplyNow',
                          title: 'Apply Now',
                          apiData: {
                            endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                            requestType: 'POST',
                            syncUserData: true,
                            isRedirect: true,
                            canClearState: true,
                            extraData: {
                              isPreApproval: false,
                              userId,
                            },
                          },
                        },
                      ],
                    },
                  },
              },
              {
                type: 10,
                key: 'greenTagImg',
                height: 26,
                imageURL:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626151796.webp',
              },
              {
                type: 19,
                key: 'greenTagText',
                padding: { top: 5 },
                texts: [
                  {
                    text: 'UPGRADE & UNLOCK MORE',
                    style: {
                      color: '0xffFFFFFF',
                      fontSize: 10,
                      fontWeight: 6,
                    },
                  },
                ],
              },
            ],
          },
          // MARK: Safe & Secure
          {
            type: 15,
            mainAxisAlignment: 1,
            mainAxisSize: 1,
            padding: { top: 35, bottom: 10 },
            children: [
              {
                type: 10,
                key: 'safeSecureSvg',
                svgURL:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751626244766.svg',
              },
              {
                type: 19,
                key: 'safeAndSecure',
                padding: { left: 8 },
                texts: [
                  {
                    text: 'Safe & secure',
                    style: {
                      color: '0x66000000',
                      fontSize: 12,
                      fontWeight: 4,
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  };
}

export function KLspPreApprovedLoanScreenObject(userId, preApprovalAmt) {
  return {
    // MARK: App Bar
    showAppBar: true,
    appBarActions: [
      {
        type: 17,
        key: 'helpButton',
        padding: { right: 16 },
        texts: [
          {
            text: 'HELP',
            style: { color: '0xff128391', fontSize: 14, fontWeight: 5 },
            clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
          },
        ],
      },
    ],
    appBarColor: '0xffF1F1F1',
    appBarElevation: 0,
    appBarLeading: {
      type: 7,
      key: 'backBtn',
      iconCodePoint: '62834',
      color: '0xff020C0D',
      size: 28,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffF1F1F1',
    backgroundColor: '0xffF1F1F1',
    // MARK: Body
    body: {
      type: 15,
      key: 'body',
      padding: { left: 20, top: 0, right: 20, bottom: 15 },
      child: {
        type: 3,
        key: 'columnView',
        crossAxisAlignment: 4,
        children: [
          // MARK: Title
          {
            type: 17,
            key: 'title',
            texts: [
              {
                text: 'Exclusive Loan Offers ',
                style: { color: '0xff020C0D', fontSize: 25, fontWeight: 6 },
              },
            ],
          },
          // MARK: Description
          {
            type: 17,
            key: 'description',
            padding: { top: 3, bottom: 15 },
            texts: [
              {
                text: 'Let’s pick your perfect match',
                style: { color: '0xff718589', fontSize: 13, fontWeight: 5 },
              },
            ],
          },
          // MARK: Pre Approved Card
          {
            type: 4,
            key: 'preApprovedMainCard',
            margin: { top: 15, bottom: 15 },
            padding: { left: 5, top: 5, right: 5, bottom: 5 },
            borderRadius: {
              bottomLeft: 15,
              bottomRight: 15,
              topLeft: 15,
              topRight: 15,
            },
            boxShadow: {
              color: '0x1A000000',
              blurRadius: 10,
              spreadRadius: 0,
              x: 1,
              y: 1,
            },
            rWidth: 1,
            child:
              // MARK: Pre Approved Inner card
              {
                type: 4,
                key: 'preApprovedSubCard',
                borderRadius: {
                  bottomLeft: 13,
                  bottomRight: 13,
                  topLeft: 13,
                  topRight: 13,
                },
                gradient: {
                  gradientColors: ['0xff78DAE6', '0xffC9FFF0'],
                  begin: 7,
                  end: 1,
                },
                rWidth: 1,
                child: {
                  type: 3,
                  key: 'preApprovedMainColumn',
                  mainAxisAlignment: 6,
                  crossAxisAlignment: 2,
                  children: [
                    // MARK: Top Banner SVG
                    {
                      type: 8,
                      key: 'preAppTopBanner',
                      rWidth: 1,
                      height: 100,
                      imageURL:
                        'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751632210807.webp',
                    },
                    // MARK: Pre Approve Inner Column
                    {
                      type: 3,
                      key: 'preApprovedInnerColumn',
                      crossAxisAlignment: 2,
                      padding: { left: 15, top: 0, right: 15, bottom: 15 },
                      children: [
                        // MARK: Just Tap Chip
                        {
                          type: 4,
                          key: 'justTapChip',
                          margin: { top: 13 },
                          padding: {
                            left: 15,
                            top: 10,
                            right: 15,
                            bottom: 10,
                          },
                          borderRadius: {
                            bottomLeft: 40,
                            bottomRight: 40,
                            topLeft: 40,
                            topRight: 40,
                          },
                          child: {
                            type: 17,
                            key: 'justTapText',
                            texts: [
                              {
                                text: 'JUST A TAP AWAY',
                                style: {
                                  color: '0xff128391',
                                  fontSize: 10,
                                  fontWeight: 7,
                                },
                              },
                            ],
                          },
                        },
                        // MARK: Text Column
                        {
                          type: 17,
                          key: 'preAppCardText1',
                          padding: { top: 10 },
                          texts: [
                            {
                              text: 'Congratulations!',
                              style: {
                                color: '0xff000000',
                                fontSize: 14,
                                fontWeight: 4,
                              },
                            },
                          ],
                        },
                        {
                          type: 17,
                          key: 'preAppCardText2',
                          texts: [
                            {
                              text: `Pre-approved loan of ${preApprovalAmt}`,
                              style: {
                                color: '0xff000000',
                                fontSize: 16,
                                fontWeight: 7,
                              },
                            },
                          ],
                        },
                        // MARK: Get Now BTN
                        {
                          type: 4,
                          key: 'preAppBtn',
                          margin: { top: 17 },
                          borderRadius: {
                            bottomLeft: 10,
                            bottomRight: 10,
                            topLeft: 10,
                            topRight: 10,
                          },
                          boxShadow: {
                            color: '0x40000000',
                            blurRadius: 4,
                            spreadRadius: 0,
                            x: 0,
                            y: 3,
                          },
                          child: {
                            type: 1,
                            key: 'preApproveGetNow',
                            title: 'Get Now',
                            apiData: {
                              endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                              requestType: 'POST',
                              syncUserData: true,
                              isRedirect: true,
                              canClearState: true,
                              extraData: {
                                isPreApproval: true,
                                userId,
                              },
                            },
                          },
                        },
                      ],
                    },
                  ],
                },
              },
          },

          // MARK: Dotted OR Line
          {
            type: 13,
            key: 'orRow',
            children: [
              {
                type: 22,
                key: 'leftDotted',
                isFlexible: true,
                color: '0xff718589',
                dashPattern: [5, 10],
              },
              {
                type: 17,
                key: 'orText',
                padding: { left: 7, right: 7 },
                texts: [
                  {
                    text: 'OR',
                    style: {
                      color: '0xff718589',
                      fontSize: 13,
                      fontWeight: 4,
                    },
                  },
                ],
              },
              {
                type: 22,
                key: 'rightDotted',
                isFlexible: true,
                color: '0xff718589',
                dashPattern: [5, 10],
              },
            ],
          },
          // MARK: Upgrade Loan Card
          {
            type: 4,
            key: 'upgradeLoanMainCard',
            margin: { top: 15 },
            padding: { left: 5, top: 5, right: 5, bottom: 5 },
            borderRadius: {
              bottomLeft: 15,
              bottomRight: 15,
              topLeft: 15,
              topRight: 15,
            },
            boxShadow: {
              color: '0x1A000000',
              blurRadius: 10,
              spreadRadius: 0,
              x: 1,
              y: 1,
            },
            rWidth: 1,
            child:
              // MARK: Upgrade Loan Inner card
              {
                type: 4,
                key: 'upgradeLoanSubCard',
                borderRadius: {
                  bottomLeft: 13,
                  bottomRight: 13,
                  topLeft: 13,
                  topRight: 13,
                },
                gradient: {
                  gradientColors: ['0xffFFC9F6', '0xffFCD446'],
                  begin: 5,
                  end: 6,
                },
                rWidth: 1,
                child: {
                  type: 3,
                  key: 'upgradeLoanMainColumn',
                  mainAxisAlignment: 6,
                  crossAxisAlignment: 2,
                  children: [
                    // MARK: Top Banner SVG
                    {
                      type: 8,
                      key: 'upgradeLoanTopBanner',
                      rWidth: 1,
                      height: 100,
                      imageURL:
                        'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jul-2025/1751632179510.webp',
                    },
                    // MARK: Upgrade Loan Inner Column
                    {
                      type: 3,
                      key: 'upgradeLoanInnerColumn',
                      crossAxisAlignment: 2,
                      padding: { left: 15, top: 0, right: 15, bottom: 15 },
                      children: [
                        // MARK: Quick Verify Chip
                        {
                          type: 4,
                          key: 'quickVerifyChip',
                          margin: { top: 13 },
                          padding: {
                            left: 20,
                            top: 10,
                            right: 20,
                            bottom: 10,
                          },
                          color: '0x00FFFFFF',
                          borderRadius: {
                            bottomLeft: 40,
                            bottomRight: 40,
                            topLeft: 40,
                            topRight: 40,
                          },
                          border: {
                            left: { color: '0x1A000000', width: 1 },
                            top: { color: '0x1A000000', width: 1 },
                            right: { color: '0x1A000000', width: 1 },
                            bottom: { color: '0x1A000000', width: 1 },
                          },
                          child: {
                            type: 17,
                            key: 'quickVerifyText',
                            texts: [
                              {
                                text: 'QUICK VERIFICATION',
                                style: {
                                  color: '0xff000000',
                                  fontSize: 10,
                                  fontWeight: 7,
                                },
                              },
                            ],
                          },
                        },
                        // MARK: Text Column
                        {
                          type: 17,
                          key: 'upgradeLoanCardText1',
                          padding: { top: 10 },
                          texts: [
                            {
                              text: 'Verify your employment and income to',
                              style: {
                                color: '0xff000000',
                                fontSize: 14,
                                fontWeight: 4,
                              },
                            },
                          ],
                        },
                        {
                          type: 17,
                          key: 'upgradeLoanCardText2',
                          texts: [
                            {
                              text: 'Unlock up to ₹3,00,000',
                              style: {
                                color: '0xff000000',
                                fontSize: 16,
                                fontWeight: 7,
                              },
                            },
                          ],
                        },
                        // MARK: Get Now BTN
                        {
                          type: 4,
                          key: 'upgradeLoanBtn',
                          margin: { top: 17 },
                          borderRadius: {
                            bottomLeft: 10,
                            bottomRight: 10,
                            topLeft: 10,
                            topRight: 10,
                          },
                          boxShadow: {
                            color: '0x40000000',
                            blurRadius: 4,
                            spreadRadius: 0,
                            x: 0,
                            y: 3,
                          },
                          child: {
                            type: 1,
                            key: 'upgradeLoanGetNow',
                            title: 'Get Now',
                            titleColor: '0xff000000',
                            backgroundColor: '0xffFFFFFF',
                            apiData: {
                              endpoint: `${HOST_URL}v4/user/preApproveLoanProcess`,
                              requestType: 'POST',
                              syncUserData: true,
                              isRedirect: true,
                              canClearState: true,
                              extraData: {
                                isPreApproval: false,
                                userId,
                              },
                            },
                          },
                        },
                      ],
                    },
                  ],
                },
              },
          },
        ],
      },
    },
  };
}

export function KPennyDropScreenObject(extraData) {
  return {
    // MARK: App Bar
    autoScroll: false,
    showAppBar: true,
    showHelpButton: true,
    appBarColor: '0xffF1F1F1',
    appBarElevation: 0,
    appBarLeading: {
      type: 9,
      key: 'back',
      iconCodePoint: '62832',
      color: '0x7f040404',
      size: 18,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffF1F1F1',
    backgroundColor: '0xffF1F1F1',
    appBarActions: [
      {
        type: 10,
        key: 'helpButton',
        padding: { right: 16 },
        svgURL:
          'https://firebasestorage.googleapis.com/v0/b/lenditt-2021.appspot.com/o/App-Dynamic-Screen%2Fhelp.svg?alt=media&token=a37c5534-4952-4825-a080-ab1f0bf6b31e',
        clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
      },
    ],
    // MARK: Body
    body: {
      type: 17,
      key: 'body',
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 4,
        key: 'view',
        crossAxisAlignment: 2,
        children: [
          // MARK: Header, Title, Description, Divider
          {
            type: 10,
            key: 'picture',
            height: 90,
            imageURL:
              'https://firebasestorage.googleapis.com/v0/b/lenditt-2021.appspot.com/o/Banner%20sample%2Fpenny_drop_icon.webp?alt=media&token=ca4f72c1-5bf4-4b74-b8d2-de0de489a7b1',
          },
          {
            type: 19,
            key: 'title',
            padding: { top: 15 },
            texts: [
              {
                text: 'Bank Details',
                style: { color: '0xff000000', fontSize: 14, fontWeight: 4 },
              },
            ],
          },
          {
            type: 19,
            key: 'description',
            padding: { top: 5 },
            textAlign: 1,
            texts: [
              {
                text: 'Enter your bank details to get your loan quickly and securely',
                style: { color: '0x80000000', fontSize: 12, fontWeight: 4 },
              },
            ],
          },
          {
            type: 5,
            key: 'divider',
            margin: { top: 20 },
            height: 1,
            rWidth: 1.0,
            color: '0x26000000',
          },
          // MARK: Field Card
          {
            type: 5,
            key: 'fields',
            color: '0xffffffff',
            borderRadius: {
              bottomLeft: 10,
              bottomRight: 10,
              topLeft: 10,
              topRight: 10,
            },
            padding: { left: 10, top: 10, right: 10, bottom: 10 },
            margin: { top: 20 },
            boxShadow: {
              color: '0x33000000',
              blurRadius: 5.0,
              spreadRadius: 0.0,
              x: 0.0,
              y: 4.0,
            },
            child:
              // MARK: Field Column
              {
                type: 4,
                key: 'widgets',
                children: [
                  // Enter Account Number
                  {
                    type: 11,
                    key: 'accountNumber',
                    padding: { top: 10 },
                    labelText: 'Enter account number',
                    titleText: 'Account number',
                    hintText: 'xxxxxxxx123',
                    keyboardType: 6,
                    formatters: [3],
                    compareFieldKey: 'confirmAccountNumber',
                    validations: {
                      regex: '^[0-9]+$',
                      error: 'Please enter a valid account number',
                    },
                  },
                  // Enter confirm account number
                  {
                    type: 11,
                    key: 'confirmAccountNumber',
                    padding: { top: 10 },
                    labelText: 'Enter confirm account number',
                    titleText: 'Confirm account number',
                    hintText: 'xxxxxxxx123',
                    keyboardType: 6,
                    formatters: [3],
                    fieldCompareValidation: {
                      ansKey: 'accountNumber',
                      error: 'Account number not matched',
                    },
                    validations: {
                      regex: '^[0-9]+$',
                      error: 'Please enter a valid account number',
                    },
                  },
                  // Enter IFSC
                  {
                    type: 11,
                    key: 'ifsc',
                    padding: { top: 10, bottom: 10 },
                    labelText: 'Enter IFSC',
                    titleText: 'IFSC',
                    hintText: 'IFSC1234567',
                    maxLength: 11,
                    textCapitalization: 1,
                    formatters: [8],
                    validations: {
                      regex: '^[A-Z]{4}0[A-Z0-9]{6}$',
                      error: 'Please enter a valid IFSC',
                    },
                    suffixIcon: {
                      type: 5,
                      key: 'searchBtn',
                      color: '0xff000000',
                      borderRadius: {
                        bottomLeft: 8,
                        bottomRight: 8,
                        topLeft: 8,
                        topRight: 8,
                      },
                      padding: { left: 13, top: 7, right: 13, bottom: 7 },
                      margin: { top: 8, right: 10, bottom: 8 },
                      clickEvent: {
                        route: 'ifscPickerScreen',
                        routingType: 1,
                        callBackKey: 'ifsc',
                      },
                      child: {
                        type: 19,
                        key: 'searchText',
                        texts: [
                          {
                            text: 'Search',
                            style: {
                              color: '0xffFFFFFF',
                              fontSize: 10,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    },
                  },
                ],
              },
          },
        ],
      },
    },

    // MARK: Bottom Navigation Bar
    bottomNavigationBar: {
      type: 5,
      key: 'bottomNavBar',
      color: '0x00ffffff',
      height: 90,
      padding: { left: 20, top: 10, right: 20, bottom: 30 },
      child: {
        type: 2,
        key: 'continue',
        title: 'Continue',
        apiData: {
          endpoint: `${HOST_URL}v4/banking/checkPennyDrop`,
          requestType: 'POST',
          extraData,
          ansKeys: [
            {
              key: 'accountNumber',
              validations: {
                regex: '^[0-9]+$',
                error: 'Please enter a valid account number',
              },
            },
            {
              key: 'confirmAccountNumber',
              compareValidation: {
                ansKey: 'accountNumber',
                error: 'Account number not matched',
              },
              validations: {
                regex: '^[0-9]+$',
                error: 'Please enter a valid account number',
              },
            },
            {
              key: 'ifsc',
              validations: {
                regex: '^[A-Z]{4}0[A-Z0-9]{6}$',
                error: 'Please enter a valid IFSC',
              },
            },
          ],
          syncUserData: true,
          submitCurrentQuestion: true,
          isRedirect: true,
          canClearState: true,
        },
      },
    },
  };
}

export function KLspPennyDropScreenObject(extraData) {
  return {
    // MARK: App Bar
    autoScroll: false,
    showAppBar: true,
    appBarActions: [
      {
        type: 17,
        key: 'helpButton',
        padding: { right: 16 },
        texts: [
          {
            text: 'HELP',
            style: { color: '0xff128391', fontSize: 14, fontWeight: 5 },
            clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
          },
        ],
      },
    ],
    appBarColor: '0xffFFFFFF',
    appBarElevation: 0,
    appBarLeading: {
      type: 7,
      key: 'back',
      iconCodePoint: '62834',
      color: '0xff020C0D',
      size: 28,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffFFFFFF',
    backgroundColor: '0xffFFFFFF',
    // MARK: Body
    body: {
      type: 15,
      key: 'body',
      physics: 3,
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 3,
        key: 'view',
        crossAxisAlignment: 4,
        children: [
          // MARK: Header
          {
            type: 17,
            key: 'title',
            texts: [
              {
                text: 'Bank Details',
                style: { color: '0xff020C0D', fontSize: 25, fontWeight: 6 },
              },
            ],
          },
          {
            type: 17,
            key: 'description',
            texts: [
              {
                text: 'Provide your bank information below.',
                style: { color: '0xff718589', fontSize: 13, fontWeight: 5 },
              },
            ],
          },
          // MARK: Fields
          {
            type: 3,
            key: 'fieldsColumn',
            padding: { top: 35 },
            children: [
              // Enter Account Number
              {
                type: 9,
                key: 'accountNumber',
                labelText: 'Enter Account Number',
                titleText: 'Account number',
                hintText: 'xxxxxxxx123',
                keyboardType: 6,
                formatters: [3],
                compareFieldKey: 'confirmAccountNumber',
                validations: {
                  regex: '^[0-9]+$',
                  error: 'Please enter a valid account number',
                },
              },
              // Enter confirm account number
              {
                type: 9,
                key: 'confirmAccountNumber',
                labelText: 'Enter confirm account number',
                titleText: 'Confirm account number',
                hintText: 'xxxxxxxx123',
                padding: { top: 25 },
                keyboardType: 6,
                formatters: [3],
                fieldCompareValidation: {
                  ansKey: 'accountNumber',
                  error: 'Account number not matched',
                },
                validations: {
                  regex: '^[0-9]+$',
                  error: 'Please enter a valid account number',
                },
              },
              // Enter IFSC
              {
                type: 9,
                key: 'ifsc',
                labelText: 'Enter IFSC',
                titleText: 'IFSC',
                hintText: 'IFSC1234567',
                padding: { top: 25 },
                textCapitalization: 1,
                maxLength: 11,
                formatters: [8],
                validations: {
                  regex: '^[A-Z]{4}0[A-Z0-9]{6}$',
                  error: 'Please enter a valid IFSC',
                },
                suffixIcon: {
                  type: 4,
                  key: 'searchBtn',
                  color: '0xff128391',
                  borderRadius: {
                    bottomLeft: 8,
                    bottomRight: 8,
                    topLeft: 8,
                    topRight: 8,
                  },
                  padding: { left: 16, top: 4, right: 16, bottom: 5 },
                  margin: { top: 9, right: 15, bottom: 7 },
                  clickEvent: {
                    route: 'openIfscPickerBottomSheet',
                    routingType: 5,
                    callBackKey: 'ifsc',
                  },
                  child: {
                    type: 17,
                    key: 'searchText',
                    texts: [
                      {
                        text: 'Search',
                        style: {
                          color: '0xffFFFFFF',
                          fontSize: 13,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // MARK: Bottom Navigation Bar
    bottomNavigationBar: {
      type: 4,
      key: 'bottomBar',
      height: 90,
      rWidth: 1.0,
      color: '0xffFFFFFF',
      border: {
        top: { color: '0x8095B0B5', width: 0.5 },
      },
      child: {
        type: 1,
        key: 'continue',
        padding: { top: 16, left: 16, right: 16, bottom: 16 },
        title: 'Continue',
        apiData: {
          endpoint: `${HOST_URL}v4/banking/checkPennyDrop`,
          requestType: 'POST',
          extraData,
          ansKeys: [
            {
              key: 'accountNumber',
              validations: {
                regex: '^[0-9]+$',
                error: 'Please enter a valid account number',
              },
            },
            {
              key: 'confirmAccountNumber',
              compareValidation: {
                ansKey: 'accountNumber',
                error: 'Account number not matched',
              },
              validations: {
                regex: '^[0-9]+$',
                error: 'Please enter a valid account number',
              },
            },
            {
              key: 'ifsc',
              validations: {
                regex: '^[A-Z]{4}0[A-Z0-9]{6}$',
                error: 'Please enter a valid IFSC',
              },
            },
          ],
          syncUserData: true,
          submitCurrentQuestion: true,
          isRedirect: true,
          canClearState: true,
        },
      },
    },
  };
}
export const kReapplyBasicDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Welcome back',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Fill the below mentioned details to get a loan',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'basicDetails',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '1',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Basic details',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                type: 5,
                key: 'divider',
                height: 1.5,
                rWidth: 1.0,
                color: '0xffEBEBEB',
              },
              {
                type: 7,
                key: 'purposeId',
                padding: { left: 10, top: 10, right: 10 },
                labelText: 'Loan purpose',
                options: [
                  { id: 1, value: 'Fulfill your business needs' },
                  { id: 2, value: 'Want to gift loved ones' },
                  { id: 3, value: 'Travel deal which can’t wait' },
                  { id: 4, value: 'Upgrade your Mobile Phone' },
                  { id: 5, value: 'Education' },
                  { id: 6, value: 'Home Improvement Needs' },
                  { id: 7, value: 'Two Wheeler Loan' },
                  { id: 8, value: 'Medical' },
                  { id: 10, value: 'Personal Loan' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a loan purpose',
                },
              },
              {
                type: 7,
                key: 'politicallyExposed',
                padding: { left: 10, top: 10, right: 10, bottom: 10 },
                labelText: 'Politically exposed',
                options: [
                  { id: 0, value: 'No' },
                  { id: 1, value: 'Yes' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your political exposition status',
                },
              },
            ],
          },
        },
        {
          type: 5,
          key: 'employment',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 15 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '2',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Employment',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 5,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffF1F1F1',
    child: {
      type: 2,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/user/submitBasicDetails`,
        requestType: 'POST',
        ansKeys: [{ key: 'purposeId' }, { key: 'politicallyExposed' }],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};

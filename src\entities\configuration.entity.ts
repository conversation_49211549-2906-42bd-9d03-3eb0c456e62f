// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';
@Table({})
export class AppConfigurations extends Model<AppConfigurations> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.ARRAY(DataType.TEXT),
    allowNull: true,
  })
  emailDomain: string[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bankingProAppID: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bankingProKeySecret: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  stuckContactUsFlow: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isRazorpay: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  androidAppVersion: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isAndroidAppForcefully: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  androidAppVersionCode: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  iosAppVersion: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isIosAppForcefully: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  iosAppVersionCode: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  nbfcAndroidAppVersion: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isNbfcAndroidAppForcefully: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  nbfcAndroidAppVersionCode: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  nbfcIosAppVersion: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isNbfcIosAppForcefully: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  nbfcIosAppVersionCode: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  updatedBy: number;
}

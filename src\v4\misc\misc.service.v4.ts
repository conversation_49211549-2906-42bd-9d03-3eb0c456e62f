// Imports
import { Injectable } from '@nestjs/common';
import { EnvConfig } from 'src/configs/env.config';
import {
  GLOBAL_RANGES,
  GLOBAL_TEXT,
  GlobalServices,
  ECS_BOUNCE_CHARGE,
  GLOBAL_CHARGES,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  kGetActiveSettings,
  kConfigSteps,
  CLOUD_FOLDER_PATH,
  kDocumentList,
  kCameraList,
} from 'src/constants/objects';
import {
  kInternalError,
  kParamMissing,
  kInvalidParamValue,
  k422ErrorMessage,
} from 'src/constants/responses';
import {
  bCommonRatesURL,
  NBFCPlayStoreLink,
  kAugmontUrl,
  kInsuranceTermCondition,
  kNbfcUrl,
  kPaymentMode,
  kVerificationsMail,
  lspAppStoreLink,
  lspPlaystoreLink,
  shareAppTextNBFC,
  shareAppTextLsp,
  kRuppe,
  kInsuranceFileProcess,
  kLspTermAndCondition,
  kLspPrivacyPolicy,
  kLspBlog,
  kNbfcTermAndCondition,
  kNbfcPrivacyPolicy,
  kNoDataFound,
  SMSConsentMsg,
  kMiscRedisKeys,
} from 'src/constants/strings';
import { BankListRepository } from 'src/repositories/bankList.repository';
import { DownloaAppTrackRepo } from 'src/repositories/downloadTrack.repository';
import { PurposeRepository } from 'src/repositories/purpose.repository';
import { ReasonRepository } from 'src/repositories/reasons.repository';
import { TemplateRepository } from 'src/repositories/template.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { UserLoanDeclineRepository } from 'src/repositories/userLoanDecline.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { TypeService } from 'src/utils/type.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { LoanRepository } from 'src/repositories/loan.repository';

//Importing Tables
import { UserRatingsEntity } from 'src/entities/user_ratings_entity';
import { FileService } from 'src/utils/file.service';
import { StringService } from 'src/utils/string.service';
import { RedisService } from 'src/redis/redis.service';
import { AdminRedisSyncService } from 'src/admin/admin/AdminRedisSync.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import * as moment from 'moment';
import { faqsList } from 'src/constants/faq_details';
import { loanTransaction } from 'src/entities/loan.entity';
import { NUMBERS } from 'src/constants/numbers';

@Injectable()
export class MiscServiceV4 {
  constructor(
    // Shared Services
    private readonly common: CommonSharedService,
    private readonly typeService: TypeService,
    private readonly downloadTrackRepo: DownloaAppTrackRepo,
    private readonly userRepo: UserRepository,
    private readonly purposeRepo: PurposeRepository,
    private readonly bankListRepo: BankListRepository,
    private readonly templateRepo: TemplateRepository,
    private readonly userLoanDeclineRepo: UserLoanDeclineRepository,
    private readonly reasonRepo: ReasonRepository,
    private readonly repoManager: RepositoryManager,
    private readonly loanRepo: LoanRepository,

    // Utils
    private readonly fileService: FileService,
    private readonly strService: StringService,
    private readonly redisService: RedisService,
    private readonly adminRedisSyncService: AdminRedisSyncService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  async getConfigs(query) {
    const appType = query?.apptype;
    const deviceType = query?.typeOfDevice;
    const appVersion = query?.appVersion;

    let configData = await this.redisService.get(kMiscRedisKeys?.configs);

    if (!configData) {
      configData = await this.adminRedisSyncService.storeConfigData();
      if (!configData) return kInternalError;
      configData = await this.redisService.get(kMiscRedisKeys?.configs);
    }
    configData = await JSON.parse(configData);
    await this.storeDeviceIdAtFirstTime(query);

    let configs: any = {};

    const version: any = await this.common.checkAppVersion(
      deviceType,
      appVersion,
      appType,
    );

    const forceFullMap = {
      '0': deviceType == '0' ? 'isAndroidAppForcefully' : 'isIosAppForcefully',
      '1':
        deviceType == '0'
          ? 'isNbfcAndroidAppForcefully'
          : 'isNbfcIosAppForcefully',
    };

    if (query.type == '1') configs.interestRatePerDay = '0.09%';
    else
      configs.interestRatePerDay = `${GLOBAL_RANGES.MIN_PER_DAY_INTEREST_RATE}%`;
    const maxLoanAmount = this.typeService.amountNumberWithCommas(
      GLOBAL_RANGES.MAX_EXCEPTION_LOAN_AMOUNT,
    );
    configs.maxLoanAmount = `up to Rs.${maxLoanAmount}`;
    configs.loanTenureDays = `up to ${GLOBAL_TEXT.MAX_LOAN_TENURE_FOR_TEXT_ONLY} days`;
    configs.noOfEmis = `up to ${GLOBAL_TEXT.NO_OF_EMIS_FOR_TEXT_ONLY}`;

    if (appType == '1') configs.smsConsentMsg = SMSConsentMsg;

    configs.serverTimestamp = this.typeService.getServerTimestamp();

    // Android
    let androidVer = query?.androidVersion;
    androidVer = androidVer ? androidVer.split('.')[0] : 0;
    let isFaceLiveness = androidVer !== 0 && androidVer < 9 ? false : true;
    configs.isFaceLiveness = isFaceLiveness;

    configs.loanMaxAmountWithSign = '₹' + maxLoanAmount;
    configs.loanMaxAmountWithoutSign = maxLoanAmount;

    configs.webApplink =
      appType == 0
        ? EnvConfig.network.flutterWebLenditt
        : EnvConfig.network.flutterWebNbfc1;
    configs.documentListSteps = kConfigSteps;

    // Gmail verification mails
    configs.gmailVerificationMail = kVerificationsMail;

    configs.androidVersion =
      appType == '1'
        ? configData.nbfcAndroidAppVersion
        : configData.androidAppVersion;
    configs.iosVersion =
      appType == '1' ? configData.nbfcIosAppVersion : configData.iosAppVersion;

    version?.continueRoute
      ? (configs.continueRoute = version?.continueRoute)
      : delete configs?.continueRoute;

    configs.isForcefulUpdate = configData?.[forceFullMap[appType]];
    configs.emailDomain = configData?.emailDomain;
    configs.bankingProAppID = configData?.bankingProAppID;
    configs.bankingProKeySecret = configData?.bankingProKeySecret;
    configs.stuckContactUsFlow = configData?.stuckContactUsFlow;
    return configs;
  }

  getTimeStamp() {
    return this.typeService.getServerTimestamp();
  }

  async storeDeviceIdAtFirstTime(query) {
    try {
      if (!query?.deviceId || !query?.type) return false;
      const deviceId = query.deviceId;
      const typeOfDevice = query.type;

      const isAlready = await this.redisService.setIfNotExistsWithNX(
        'firstTimeDevice' + deviceId,
        NUMBERS.FIVE_MINUTES_IN_SECONDS,
      );
      //manage is already called or not with redis
      if (!isAlready) return {};

      //check device Id already exist
      const options = { where: { deviceId } };
      const checkIfUserPresent = await this.downloadTrackRepo.getCountsWhere(
        options,
      );
      //chech device already registred
      const userOptions = { where: {} };
      if (query.typeOfDevice === '2')
        userOptions.where = {
          webRecentDeviceId: deviceId,
        };
      else
        userOptions.where = {
          recentDeviceId: deviceId,
        };
      const checkUserRegisterPresent = await this.userRepo.getCountsWhere(
        userOptions,
      );
      if (checkIfUserPresent > 0 || checkUserRegisterPresent > 0) return false;
      const currDate: any = this.typeService.getGlobalDate(new Date());
      await this.downloadTrackRepo.create({
        deviceId,
        typeOfDevice,
        registeredDate: currDate.toJSON(),
      });
    } catch (error) {
      return false;
    }
  }

  async getLoanPurposeList() {
    try {
      const attributes = [
        'id',
        'purposeName',
        'header',
        'image',
        'primaryColor',
        'primaryAccentColor',
        'purposeStatusVerified',
        'textColor',
      ];
      const options = { where: { purposeStatusVerified: '1' } };

      const purposeList = await this.purposeRepo.getTableWhereData(
        attributes,
        options,
      );
      if (purposeList == k500Error) return kInternalError;
      return purposeList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getAvailableBankList() {
    try {
      const key = 'BANKS_LIST';
      let bankList = await this.redisService.getKeyDetails(key);
      if (bankList) bankList = JSON.parse(bankList);
      else {
        const attributes = [
          'aaService',
          'id',
          'bankCode',
          'bankName',
          'image',
          'pdfService',
          'bankImg',
          'lspBankImg',
        ];
        const options = {
          where: { statusFlag: '1' },
          order: [['bankName', 'ASC']],
        };
        bankList = await this.bankListRepo.getTableWhereData(
          attributes,
          options,
        );
        if (bankList == k500Error) return kInternalError;
      }
      return bankList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getSettingsData(data) {
    try {
      const settingsList = kGetActiveSettings();
      const insuranceData = settingsList.find((el) => el?.title == 'Insurance');
      if (insuranceData) {
        const key = `TEMPLATE_FAQ_INSURANCE`;
        let FAQData = await this.redisService.get(key);

        FAQData = FAQData ?? null;
        FAQData = JSON.parse(FAQData) ?? [];

        if (!FAQData.length) {
          const faqAttr: any = [
            'title',
            'content',
            ['templateId', 'otherLink'],
          ];
          const faqOps = {
            where: { type: 'FAQ', subType: 'INSURANCE' },
            order: [['id', 'ASC']],
          };
          FAQData = await this.templateRepo.getTableWhereData(faqAttr, faqOps);
          if (FAQData === k500Error) throw new Error();
        }
        insuranceData.data.FAQ = FAQData;
        insuranceData.data.termCondition = kInsuranceTermCondition;
        insuranceData.data.processToFile = kInsuranceFileProcess;
      }

      // About company page
      const links = {
        0: {
          termsAndCondition: `${kLspTermAndCondition}?fromApp=true`,
          privacyPolicy: `${kLspPrivacyPolicy}?fromApp=true`,
          blog: `${kLspBlog}?fromApp=true`,
        },
        1: {
          termsAndCondition: `${kNbfcTermAndCondition}?fromApp=true`,
          privacyPolicy: `${kNbfcPrivacyPolicy}?fromApp=true`,
        },
      };

      const appLinks = links[data.appType] || {};
      const termsAndCondition = appLinks.termsAndCondition
        ? {
            title: 'Terms and Condition',
            label: 'Terms and Condition',
            link: appLinks.termsAndCondition,
          }
        : {};
      const privacyPolicy = appLinks.privacyPolicy
        ? {
            title: 'Privacy Policy',
            label: 'Privacy Policy',
            link: appLinks.privacyPolicy,
          }
        : {};
      const Blog =
        data.appType == 0 && appLinks.blog
          ? { title: 'Blog', label: 'Blog', link: appLinks.blog }
          : {};

      //Charges page
      if (data?.userId) {
        const chargesObj = settingsList.find((el) => el?.title == 'Charges');
        if (chargesObj) {
          const userId = data?.userId;
          const lastLoan = await this.loanRepo.getRowWhereData(
            ['charges', 'penaltyCharges'],
            {
              where: { userId, loanStatus: 'Active' },
              order: [['id', 'DESC']],
            },
          );
          if (lastLoan === k500Error) throw new Error();
          const loanCharges = {
            icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/currencyinr-r_360.png',
            title: 'Loan Charges',
            description: 'Applicable on the approved loan amount',
            chargesData: {
              'Processing fees': `${GLOBAL_CHARGES.PROCESSING_FEES}%`,
              'Online convenience fees': `${kRuppe}${GLOBAL_CHARGES.INSURANCE_FEE}`,
              'Document charge': `${GLOBAL_CHARGES.DOC_CHARGE_PER}%`,
            },
          };

          if (GLOBAL_CHARGES.RISK_ASSESSMENT_PER)
            loanCharges.chargesData[
              'Risk assessment charges'
            ] = `${GLOBAL_CHARGES.RISK_ASSESSMENT_PER}%#*\nFor applicable users*#`;

          loanCharges.chargesData[
            'GST#*\nGST is inclusive As specified by Government of India*#'
          ] = `${GLOBAL_CHARGES.GST}%`;

          const penalCharges = {
            icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/percent-r_360.png',
            title: 'Penal Charges',
            description: 'Only applicable on the past due EMI principal amount',
            chargesData: {
              'Days Past Due 1 to 3 days': '5%',
              'Days Past Due 4 to 14 days': '10%',
              'Days Past Due 15 to 30 days': '15%',
              'Days Past Due 31 to 60 days': '20%',
              'Days Past Due 60+ days': '25%',
            },
          };

          const otherCharges = {
            icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/receipt-r_360.png',
            title: 'Other Charges',
            chargesData: {
              'ECS Bounce charges': `${this.strService.readableAmount(
                ECS_BOUNCE_CHARGE,
              )}`,
            },
          };

          if (data.appType == 0) {
            loanCharges.icon =
              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/loan_charges(1).svg';
            penalCharges.icon =
              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/penal_charges.svg';
            otherCharges.icon =
              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/other_charges.svg';
          }

          let forclousePercentage = `#\n\n0%##\n\n4%##*\n(+ GST as applicable)*#`;
          if (data.appType == 0)
            forclousePercentage = `#\n\n0%##\n4%##*\n(+ GST as applicable)*#`;

          otherCharges.chargesData[
            'Foreclose Charges #*(on the remaining principal amount)*##\n• Within 3 Days of Disbursement##\n• After 3 Days of Disbursement#'
          ] = forclousePercentage;

          if (lastLoan && !lastLoan?.penaltyCharges) {
            otherCharges.chargesData[
              'Deferred Interest#*\n0.2% per day, which equals to 73% annually.*#'
            ] = '';
            chargesObj.data = [loanCharges, otherCharges];
          } else {
            otherCharges.chargesData[
              'Deferred Interest#*\nIn the event of delayed EMI Payments, regular interest will accumulate on the outstanding EMI principal amount.*#'
            ] = '';
            chargesObj.data = [loanCharges, penalCharges, otherCharges];
          }
          otherCharges.chargesData['Legal Charge'] = `${
            kRuppe +
            this.typeService.amountNumberWithCommas(GLOBAL_CHARGES.LEGAL_CHARGE)
          }#*\n(+ GST as applicable)*#`;
        }
        const userData = await this.redisService.getKeyDetails(
          `${data.userId}_USER_SETTING_PREFERENCE`,
        );

        let getUserData;
        if (!userData) {
          getUserData = await this.userRepo.getRowWhereData(
            ['id', 'allowedPromotionalContent'],
            { where: { id: data.userId } },
          );
        }
        const userDataToPush = userData ? JSON.parse(userData) : getUserData;
        if (userDataToPush) {
          settingsList.push({
            title: 'promotionalPreferencesList',
            id: userDataToPush.id,
            value: userDataToPush.allowedPromotionalContent,
          });
        }
      }
      settingsList.push(termsAndCondition, privacyPolicy, Blog);
      return settingsList;
    } catch (error) {
      return [];
    }
  }

  async userLoanDeclineReasons() {
    try {
      const attributes = ['id', 'userDeclineReasonTitle'];
      const options = {
        where: { userDeclineStatus: '0' },
      };
      const reasons = await this.userLoanDeclineRepo.getTableWhereData(
        attributes,
        options,
      );
      if (!reasons || reasons == k500Error) return kInternalError;
      return reasons;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getBlackListReason() {
    try {
      const redisKey = 'BLACKLIST_REASON';
      const redisData = await this.redisService.get(redisKey);
      if (redisData) return JSON.parse(redisData);

      const attributes = ['id', 'reason'];
      const options = {
        where: { status: true, type: 'USER_BLACKLIST' },
      };
      const blackListReason = await this.reasonRepo.getTableWhereData(
        attributes,
        options,
      );
      if (blackListReason == k500Error) return kInternalError;

      await this.redisService.set(
        redisKey,
        JSON.stringify(blackListReason),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );

      return blackListReason;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async shareTextApp(headers: Headers) {
    try {
      const appType = headers['apptype'] ?? 0;
      const data: any = {};
      if (appType == 0) {
        data.shareAppText = shareAppTextLsp;
        data.playstoreLink = lspPlaystoreLink;
        data.appStoreLink = lspAppStoreLink;
      } else {
        data.shareAppText = shareAppTextNBFC;
        data.playstoreLink = NBFCPlayStoreLink;
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async storeUserRating(body) {
    const userId = body?.userId;
    const rating = body?.rating ?? '';
    const feedBack = body?.feedBack ?? null;
    if (!userId) return kParamMissing('userId');
    if (!rating) return kParamMissing('rating');
    if (rating < 1 || rating > 5) return kInvalidParamValue('rating');
    const ratingData = { userId, rating, feedBack };
    const createdData = await this.repoManager.createRowData(
      UserRatingsEntity,
      ratingData,
    );

    if (createdData == k500Error) throw new Error();

    return createdData;
  }

  async uploadFile(reqData) {
    const media = reqData.media;
    if (!media) return kParamMissing('media');
    if (
      reqData?.directory == CLOUD_FOLDER_PATH.APP_BANK_STATEMENTS &&
      reqData?.extension?.includes('io/')
    ) {
      reqData.extension = 'pdf';
    }

    let extraParam;
    if (reqData?.password) {
      extraParam = {
        password: reqData?.password,
      };
    }
    return await this.fileService.binaryToFileURL(
      media,
      reqData.extension,
      reqData.directory,
      null,
      true,
      reqData?.checkKey,
      extraParam,
    );
  }

  async getFAQDetails(query) {
    const userId = query?.userId;
    const appType = +query?.appType;
    const typeOfDevice = +query?.typeOfDevice || 0;
    const maxLoanAmount = this.strService.readableAmount(
      GLOBAL_RANGES.MAX_EXCEPTION_LOAN_AMOUNT,
    );
    const faqsDetails = faqsList(maxLoanAmount, appType, typeOfDevice);

    // Prepare Query
    const options = {
      where: { userId },
      order: [['id', 'DESC']],
    };

    //Fetch Loan Data
    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      ['id', 'loanStatus'],
      options,
    );
    if (loanData === k500Error) throw new Error();

    faqsDetails.chatExtraData = await this.common.funManageChatExtraData(
      userId,
    );

    faqsDetails.documentList = kDocumentList;
    faqsDetails.cameraList = kCameraList;

    // Extract Data
    if (loanData?.id) {
      const lastLoanStatus = loanData?.loanStatus ?? '';

      // Determine `type`
      const loanTypeMapping = {
        'Active-0': 'ACTIVELOAN',
        'Active-1': 'ACTIVELOANNBFC1',
        'InProcess-0': 'INPROCESSLOAN',
        'Accepted-0': 'INPROCESSLOAN',
        'InProcess-1': 'INPROCESSLOANNBFC1',
        'Accepted-1': 'INPROCESSLOANNBFC1',
      };

      const type =
        loanTypeMapping[`${lastLoanStatus}-${appType}`] ??
        (lastLoanStatus === 'Rejected' || lastLoanStatus === ''
          ? appType === 1
            ? 'INPROCESSLOANNBFC1'
            : 'INPROCESSLOAN'
          : appType === 1
          ? 'INPROCESSLOANNBFC1'
          : 'INPROCESSLOAN');

      let contactData: any = {};
      if (type) {
        contactData = await this.common.getStaticConfigData(type);
        if (!contactData) return k422ErrorMessage(kNoDataFound);
      }

      // Parse Contact Data
      const contactInfo = contactData?.data
        ? JSON.parse(contactData?.data[0] ?? '{}')
        : {};

      if (Object.keys(contactInfo).length) {
        faqsDetails.contactUs = { ...faqsDetails.contactUs, ...contactInfo };
      }
    } else {
      if (appType === 1) {
        faqsDetails.contactUs.complaintEmails = [EnvConfig.mail.suppportMail];
        faqsDetails.contactUs.queryEmails = [EnvConfig.mail.suppportMail];
        faqsDetails.contactUs.phone = EnvConfig.number.helpContact;
      } else {
        faqsDetails.contactUs.complaintEmails = [
          EnvConfig.lsp.mail.lspSuppportMail,
        ];
        faqsDetails.contactUs.queryEmails = [
          EnvConfig.lsp.mail.lspSuppportMail,
        ];
        faqsDetails.contactUs.phone =
          EnvConfig.lsp.number.lsphelpContactBeforeDisbursement;
      }
    }

    return faqsDetails;
  }
}

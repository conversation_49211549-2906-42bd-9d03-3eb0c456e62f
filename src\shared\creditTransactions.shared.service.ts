// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { fn, literal, Op, Sequelize } from 'sequelize';
import { LegalService } from 'src/admin/legal/legal.service';
import { k500Error } from 'src/constants/misc';
import * as fs from 'fs';

import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  kCalBySystem,
  kCashfree,
  kCompleted,
  kCredit,
  kCreditPay,
  kCreditTransaction,
  kCreditTransactionDirect,
  kDirectBankPay,
  kFullPay,
  kInitiated,
  kPartPay,
  kRazorpay,
  kRefund,
  kSomthinfWentWrong,
  kTransactionIdExists,
  kWrongSourceType,
  kYouReachedAutoDebitLimit,
  kfinvu,
  kCreditRefundEmailSubject,
  kNoReplyMail,
} from 'src/constants/strings';
import {
  HOST_URL,
  ECS_BOUNCE_CHARGE,
  Latest_Version,
  penaltyChargesObj,
  ICICI_CREDS,
  MSG91,
  REKYCDAYS,
  gIsPROD,
} from 'src/constants/globals';
import { EmiEntity } from 'src/entities/emi.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { AdminRepository } from 'src/repositories/admin.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { CrmRepository } from 'src/repositories/crm.repository';
import { EMIRepository } from 'src/repositories/emi.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { StampRepository } from 'src/repositories/stamp.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { UserActivityRepository } from 'src/repositories/user.activity.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { CashFreeService } from 'src/thirdParty/cashfree/cashfree.service';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import { CryptService } from 'src/utils/crypt.service';
import { DateService } from 'src/utils/date.service';
import { RazorpayService } from 'src/utils/razorpay.service';
import { TypeService } from 'src/utils/type.service';
import { CalculationSharedService } from './calculation.service';
import { CommonSharedService } from './common.shared.service';
import { SharedNotificationService } from './services/notification.service';
import { SYSTEM_ADMIN_ID } from 'src/constants/globals';
import { loanTransaction } from 'src/entities/loan.entity';
import { SigndeskService } from 'src/thirdParty/signdesk/signdesk.service';
import { PromoCodeService } from './promo.code.service';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { ICICIThirdParty } from 'src/thirdParty/icici/icici.service';
import { APIService } from 'src/utils/api.service';
import { StringService } from 'src/utils/string.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { LogsSharedService } from './logs.service';
import { UserSharedLogTrackerMiddleware } from './logtracker.middleware';
import { UserLogTrackerRepository } from 'src/repositories/userLogTracker.repository';
import { EnvConfig } from 'src/configs/env.config';
import { FileService } from 'src/utils/file.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { AllsmsService } from 'src/thirdParty/SMS/sms.service';
import { FinvuService } from 'src/thirdParty/finvu/finvu.service';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { BankingSharedService } from './banking.service';
import { YESService } from 'src/thirdParty/yes/yes.service';
import { ReportService } from 'src/admin/report/report.service';
import { RedisService } from 'src/redis/redis.service';
import { CRYPT_PATH } from 'src/constants/paths';
import { SharedTransactionService } from './transaction.service';
import {
  kCreditRefundEmailTemplate,
  kRefundEmailTemplate,
} from 'src/constants/directories';
import { SequelOptions } from 'src/interfaces/include.options';
import { registeredUsers } from 'src/entities/user.entity';
interface CurrentExpected {
  id: number | string;
  emiNumber: number;
  principal: number;
  interest: number;
  deferred: number;
  bounce: number;
  penalty: number;
  penal: number;
  legal: number;
  currentprincipal: number;
  currentinterest: number;
  currentdeferred: number;
  currentbounce: number;
  currentpenalty: number;
  currentpenal: number;
  currentlegal: number;
  isNewUser: boolean;
  rPenalty: number;
}

@Injectable()
export class creditTransactionService {
  constructor(
    // Database
    private readonly repoManager: RepositoryManager,
    private readonly emiRepo: EMIRepository,
    private readonly typeService: TypeService,
    private readonly strService: StringService,
    private readonly crmRepo: CrmRepository,
    private readonly dateService: DateService,
    private readonly emiService: EMIRepository,
    private readonly transactionRepo: TransactionRepository,
    private readonly userRepo: UserRepository,
    private readonly cryptService: CryptService,
    private readonly razorpaySer: RazorpayService,
    private readonly adminRepo: AdminRepository,
    private readonly stampRepo: StampRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly masterRepository: MasterRepository,
    @Inject(forwardRef(() => CalculationSharedService))
    private readonly sharedCalculation: CalculationSharedService,
    private readonly legalService: LegalService,
    private readonly userActivityRepo: UserActivityRepository,
    private readonly sharedNotification: SharedNotificationService,
    private readonly cashFreeService: CashFreeService,
    private readonly loanRepo: LoanRepository,
    private readonly commonSharedService: CommonSharedService,
    private readonly razorpayService: RazorpoayService,
    private readonly signDeskService: SigndeskService,
    private readonly whatsappService: WhatsAppService,
    private readonly apiService: APIService,
    private readonly fileService: FileService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userServiceV4: UserServiceV4,
    // Shared
    private readonly promoCodeService: PromoCodeService,
    @Inject(forwardRef(() => ICICIThirdParty))
    private readonly iciciService: ICICIThirdParty,
    @Inject(forwardRef(() => LogsSharedService))
    private readonly logsSharedService: LogsSharedService,
    @Inject(forwardRef(() => UserSharedLogTrackerMiddleware))
    private readonly userSharedLogTrackerMiddleware: UserSharedLogTrackerMiddleware,
    private readonly userLogTrackerRepository: UserLogTrackerRepository,
    private readonly allsmsService: AllsmsService,
    @Inject(forwardRef(() => FinvuService))
    private readonly finvuService: FinvuService,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly bankingSharedService: BankingSharedService,
    private readonly changeLogRepo: ChangeLogsRepository,
    @Inject(forwardRef(() => YESService))
    private readonly yesUpiService: YESService,
    @Inject(forwardRef(() => ReportService))
    private readonly reportService: ReportService,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => SharedTransactionService))
    private readonly sharedTransaction: SharedTransactionService,
    @Inject(forwardRef(() => CommonSharedService))
    private readonly sharedCommonService: CommonSharedService,
  ) {}

  //#region creditAmountBifurcationForDefaulter
  async creditAmountBifurcationForDefaulter() {
    return {};
    let loanWherePayemntAfter31st = [
      '0482c0ba-1f46-46c6-8836-9e5f7f4632b0',
      '********-eef8-483f-8a82-5098f7e342d3',
      '0ed3486e-c7dd-46d7-81ef-573fe2484ec2',
      '17e82d81-d618-45ca-b4d2-c259b4aca0fe',
      '2184980b-8d72-48b8-b5a6-0418212348b0',
      '221b0753-f707-4160-9acd-2c3613a68e83',
      '2db8741a-b199-4d39-ac04-66af6d348eb4',
      '33d834d0-2ae1-4123-8b59-2350473dabd9',
      '37649f35-51e2-4f06-a66e-d832a82a6229',
      '383b7a37-b347-498d-967e-96d5d072524d',
      '3bf90b2a-c594-4cc9-b278-09e40baf06c4',
      '3d5c4a0f-fec4-4a0d-88d4-c25fb593f054',
      '3ed6b6d7-f790-4c50-bfc1-a4e41530c23a',
      '423aff0d-3a2e-491c-8df2-0a7f625a8966',
      '495702ef-45ad-4348-8deb-b9238229f64f',
      '49976e2e-97a6-4c97-aa7d-968fac9ac5bf',
      '588ff3e2-eeae-43fa-a77c-9171500e2f02',
      '626c4ce6-21f4-4859-8c56-a49cc6abadd5',
      '6cee4c55-ae4c-4ece-8b9f-b1f5a54e78bf',
      '776afe6d-b7e8-434d-b73f-1662232823d8',
      '77f1bfed-1d44-4ea6-bfe3-dd0c042b5248',
      '7f5b0b74-5748-4b8e-86e2-a8a682892d39',
      '81450230-ae86-4c55-8998-cdbc9a79c101',
      '8146a6af-a046-4b85-a0a2-eab465251bef',
      '833eda52-0462-4f44-bf8c-f774f439434c',
      '85a7638e-08f1-4c61-86f7-7b86f270c479',
      '9003fe5a-5f40-4630-81b6-23a1027470a7',
      '9cf05f3b-3de3-4d96-8a4b-efb9c8baefe0',
      'a04d4d1a-143f-496c-a882-d2beed2f710a',
      'a3520478-f15f-45e3-ab44-e29645d8649c',
      'a755be37-57c6-44d1-b36d-380abcd1ed95',
      'abc11e13-984c-4b91-b259-a9d6d7bd130d',
      'b35b5224-9bca-4b53-8e1e-5c56eebeca98',
      'ba7b5043-11b8-4462-87d2-66e0a6ef6395',
      'be569661-3488-4dc4-8784-0052b9067cce',
      'c12010c3-445b-4522-88b1-e3a26e600a07',
      'c1805936-0022-42f6-a832-3c120002ccfe',
      'c1d12b2a-1c2b-48f3-a0bd-e5dcf648825f',
      'c388e905-2dc3-4867-bd61-bc9382deff1a',
      'cba88b1a-06cf-41b7-a5c6-27cb994d0864',
      'd9b4ce74-8d01-431a-9baa-a26c167859d9',
      'db5c4e90-d7aa-49f7-8440-12b93d486834',
      'df9534b5-2917-4c83-9560-1155da043fa2',
      'e36236e7-6aa1-44ce-aee8-ae4fcc739eb1',
      'e3edef3f-d3c3-4db6-9938-3425d32a8c40',
      'e99f53e7-090e-49b2-ad2f-15bcbe39d27a',
      'ec47458a-73ea-4e4d-af01-8d2a23dde50c',
      'f35f748f-8e42-423e-81e0-9091bcd1480f',
      '22a0b298-b821-4bcc-8885-d9b7be00b95c',
      'fa76f896-3f65-46bc-93f6-32ae80fc6291',
    ];
    let transAttr = [
      'principalAmount',
      'interestAmount',
      'regInterestAmount',
      'penaltyAmount',
      'bounceCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',
      'penalCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
      'loanId',
      'emiId',
      'type',
      'status',
      'subSource',
    ];

    let loanAttr = [
      'id',
      'penaltyCharges',
      'interestRate',
      'followerId',
      'netApprovedAmount',
      'feesIncome',
    ];
    let emiAttr = [
      'id',
      'principalCovered',
      'interestCalculate',
      'regInterestAmount',
      'bounceCharge',
      'totalPenalty',
      'dpdAmount',
      'penaltyChargesGST',
      'gstOnBounceCharge',
      'legalCharge',
      'legalChargeGST',
      'emiNumber',
      'emiDate',
      'paid_principal',
      'paid_interest',
      'paidRegInterestAmount',
      'paidBounceCharge',
      'paid_penalty',
      'paidPenalCharge',
      'paidLegalCharge',
      'penalty_days',
    ];

    let user = JSON.parse(fs.readFileSync(CRYPT_PATH.defaulters, 'utf8'));
    // let loans = JSON.parse(fs.readFileSync(CRYPT_PATH.loanData, 'utf8'));
    let emiData = JSON.parse(fs.readFileSync(CRYPT_PATH.emiData, 'utf8'));

    let userNotFound = [];
    let trans = [];
    let emiNotFound = [];
    let successData = [];
    let amtWhereUserNotFound = 0;
    for (let index = 0; index < user.length; index++) {
      try {
        const ele = user[index];
        const userId = ele?.userId;
        console.log(userId);
        const amount = ele?.amount;

        if (index % 100 === 0)
          console.log(index, `Total -------------------> ${user.length}`);

        // Loan Data
        let loan = await this.loanRepo.getRowWhereData(
          [
            'id',
            'userId',
            'penaltyCharges',
            'interestRate',
            'followerId',
            'netApprovedAmount',
            'feesIncome',
          ],
          {
            where: {
              userId,
              loanStatus: 'Active',
            },
          },
        );
        if (loan == k500Error)
          return { message: 'Error in Getting LOAN DATA.', successData };

        if (!loan || loanWherePayemntAfter31st.indexOf(userId) != -1) {
          userNotFound.push(userId);
          amtWhereUserNotFound += amount;
          continue;
        }
        const loanId = loan?.id;

        // EMI Data
        let emis = emiData.find((obj) => obj.loanId == loanId).emi;
        if (!emis) {
          emiNotFound.push(userId);
          return {
            message: 'Error in Getting EMIs.',
            successData,
            userNotFound,
          };
        }

        // // Trans Data
        let transData = await this.transactionRepo.getTableWhereData(
          transAttr,
          {
            where: {
              loanId,
              status: { [Op.in]: [kCompleted] },
              type: { [Op.ne]: kRefund },
            },
          },
        );
        if (transData === k500Error) {
          successData.push(loanId);
          return {
            message: 'Error in Getting Trans.',
            successData,
            userNotFound,
          };
        }

        let expectData = this.getExpected(
          loan,
          emis,
          transData,
          successData,
          loanId,
        );
        if (expectData?.message)
          return {
            message: 'Error in Getting getExpected.',
            successData,
            userNotFound,
          };
        const { expectedCharges, expectedPI } = expectData;

        const bifurcationData = this.bifurcationforCreditTrans(
          {
            expectedCharges,
            expectedPI,
            amount,
            loanId,
            userId,
          },
          successData,
        );

        if (bifurcationData?.message)
          return {
            message: 'Error in Getting bifurcationforCreditTrans.',
            successData,
            userNotFound,
          };

        const { eBifurcation, tBifurcation, atPI } = bifurcationData;
        const remainingAmount = atPI?.remainingAmount ?? 0;

        const transactions: any = this.prepareTransactionForCreditPay(
          tBifurcation,
          loan,
          emis,
          userId,
          trans,
          remainingAmount,
          amount,
          successData,
        );
        if (transactions?.message)
          return {
            message: 'Error in prepareTransactionForCreditPay.',
            successData,
            userNotFound,
          };

        const completeTrans: any = await this.markCreditTransAsComplete(
          transactions,
          eBifurcation,
          amount,
          remainingAmount,
          userId,
          loanId,
          loan,
          successData,
        );
        if (completeTrans?.message)
          return {
            message: 'Error in markCreditTransAsComplete.',
            successData,
            userNotFound,
          };
        successData.push(loanId);
        // console.log('__________End of One User___________');
      } catch (error) {
        console.log({ error });
        break;
      }
    }
    const rawExcelData = {
      sheets: ['Bifurcation Data'],
      data: [trans],
      sheetName: 'refundBifurcationData.xlsx',
    };
    const url = await this.fileService.objectToExcel(rawExcelData);
    console.log({ url });
    return {
      userNotFound,
      emiNotFound,
      amtWhereUserNotFound,
      successData,
    };
  }
  //#endregion

  //#region getExpected
  getExpected(loan, emis, transData, successData, loanId) {
    let isNewUser = loan?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;

    let expectedPI = [];
    let expectedCharges = [];

    // Loop to Calculate Expected
    for (let index = 0; index < emis.length; index++) {
      try {
        const emi: EmiEntity = emis[index];
        let paidprincipal = 0;
        let paidinterest = 0;
        let paiddeferred = 0;
        let paidbounce = 0;
        let paidpenalty = 0;
        let paidpenal = 0;
        let paidlegal = 0;
        // Calculating Paid from Transactions
        let thisEmiTrans = transData.filter((t) => t.emiId == emi.id) ?? [];
        thisEmiTrans.forEach((t: TransactionEntity) => {
          paidprincipal += t.principalAmount ?? 0;
          paidinterest += t.interestAmount ?? 0;
          paiddeferred += t.regInterestAmount ?? 0;
          paidbounce +=
            (t.bounceCharge ?? 0) +
            (t.sgstOnBounceCharge ?? 0) +
            (t.cgstOnBounceCharge ?? 0) +
            (t.igstOnBounceCharge ?? 0);
          paidpenalty += t.penaltyAmount ?? 0;
          paidpenal +=
            (t.penalCharge ?? 0) +
            (t.cgstOnPenalCharge ?? 0) +
            (t.sgstOnPenalCharge ?? 0) +
            (t.igstOnPenalCharge ?? 0);
          paidlegal +=
            (t.legalCharge ?? 0) +
            (t.cgstOnLegalCharge ?? 0) +
            (t.sgstOnLegalCharge ?? 0) +
            (t.igstOnLegalCharge ?? 0);
        });

        // Expecteds
        let principal = 0;
        let interest = 0;
        let deferred = 0;
        let bounce = 0;
        let penalty = 0;
        let penal = 0;
        let legal = 0;
        principal += emi.principalCovered ?? 0;
        interest += emi.interestCalculate ?? 0;
        bounce += (emi.bounceCharge ?? 0) + (emi.gstOnBounceCharge ?? 0);
        // if (isEmiBounce) bounce = 500;
        if (isNewUser) {
          bounce += emi.gstOnBounceCharge ?? 0;
          deferred += emi.regInterestAmount ?? 0;
          penal += (emi.dpdAmount ?? 0) + (emi?.penaltyChargesGST ?? 0);
          legal += (emi.legalCharge ?? 0) + (emi.legalChargeGST ?? 0);
        } else {
          penalty += emi.totalPenalty ?? 0;
          // if (isEmiBounce && !isNewUser) penalty += 500;
        }

        if (
          (emi?.penaltyChargesGST ?? 0) > 0 ||
          (emi.gstOnBounceCharge ?? 0) > 0
        )
          console.log('GST FOUND', { emi });

        // currentExpected After Paid
        expectedCharges.push({
          id: emi.id,
          emiNumber: emi.emiNumber,
          deferred: deferred - paiddeferred,
          bounce: bounce - paidbounce,
          penalty: penalty - paidpenalty,
          rPenalty: penalty,
          penal: penal - paidpenal,
          legal: legal - paidlegal,
          currentdeferred: emi.paidRegInterestAmount,
          currentbounce: emi.paidBounceCharge,
          currentpenalty: emi.paid_penalty,
          currentpenal: emi.paidPenalCharge,
          currentlegal: emi.paidLegalCharge,
          isNewUser,
        });
        expectedPI.push({
          id: emi.id,
          emiNumber: emi.emiNumber,
          principal: principal - paidprincipal,
          interest: interest - paidinterest,
          currentprincipal: emi.paid_principal,
          currentinterest: emi.paid_interest,
        });
      } catch (error) {
        console.log({ error });
        successData.push(loanId);
        return { message: 'Error in Catch getExpected.', successData };
        break;
      }
    }
    for (let i = 0; i < expectedCharges.length; i++) {
      for (let key in expectedCharges[i]) {
        if (
          typeof expectedCharges[i][key] === 'number' &&
          expectedCharges[i][key] < 0
        ) {
          expectedCharges[i][key] = 0;
        }
        if (typeof expectedCharges[i][key] === 'number')
          expectedCharges[i][key] = Math.round(expectedCharges[i][key]);
      }
    }

    for (let i = 0; i < expectedPI.length; i++) {
      for (let key in expectedPI[i]) {
        if (typeof expectedPI[i][key] === 'number' && expectedPI[i][key] < 0) {
          expectedPI[i][key] = 0;
        }
      }
    }
    return { expectedCharges, expectedPI };
  }
  //#region bifurcationforCreditTrans
  private bifurcationforCreditTrans(body, successData) {
    const expectedCharges = body.expectedCharges;
    const expectedPI = body.expectedPI;
    let paidAmount = body.amount;
    let loanId = body.loanId;
    let userId = body.userId;
    let atPI;
    let eBifurcation = [];
    let tBifurcation = [];

    // #Covering Charges
    for (let index = 0; index < expectedCharges.length; index++) {
      try {
        const ele: CurrentExpected = expectedCharges[index];
        let emiObj: any = {
          id: ele?.id,
        };
        let transObj: any = {
          id: ele?.id,
        };

        if (Math.round(paidAmount) > 0) {
          // #2 -> Penal Charges(For New Users)
          if (paidAmount > 0 && ele?.penal > 0 && paidAmount > ele?.penal) {
            emiObj.paidPenalCharge = +(ele?.penal + ele?.currentpenal).toFixed(
              2,
            );
            transObj.paidPenalCharge = +(ele?.penal).toFixed(2);
            paidAmount -= ele?.penal;
          } else if (
            paidAmount > 0 &&
            ele?.penal > 0 &&
            paidAmount < ele?.penal
          ) {
            emiObj.paidPenalCharge = +(paidAmount + ele?.currentpenal).toFixed(
              2,
            );
            transObj.paidPenalCharge = +paidAmount.toFixed(2);
            paidAmount = 0;
          }

          // #2 -> Penalty (For Old Users)
          if (!ele?.isNewUser) {
            if (
              paidAmount > 0 &&
              ele?.penalty > 0 &&
              paidAmount > ele?.penalty
            ) {
              emiObj.paid_penalty = +(
                ele?.penalty + ele?.currentpenalty
              ).toFixed(2);
              emiObj.penalty =
                (ele?.rPenalty ?? 0) - (ele?.penalty + ele?.currentpenalty);
              transObj.paid_penalty = +(ele?.penalty).toFixed(2);
              paidAmount -= ele?.penalty;
            } else if (
              paidAmount > 0 &&
              ele?.penalty > 0 &&
              paidAmount < ele?.penalty
            ) {
              emiObj.paid_penalty = +(paidAmount + ele?.currentpenalty).toFixed(
                2,
              );
              emiObj.penalty =
                (ele?.rPenalty ?? 0) -
                +(paidAmount + ele?.currentpenalty).toFixed(2);
              transObj.paid_penalty = +paidAmount.toFixed(2);
              paidAmount = 0;
            }
          }

          // #3 -> Bounce Charge
          if (ele?.isNewUser) {
            if (paidAmount > 0 && ele?.bounce > 0 && paidAmount > ele?.bounce) {
              emiObj.paidBounceCharge = +(
                ele?.bounce + ele?.currentbounce
              ).toFixed(2);
              transObj.paidBounceCharge = +(ele?.bounce).toFixed(2);
              paidAmount -= ele?.bounce;
            } else if (
              paidAmount > 0 &&
              ele?.bounce > 0 &&
              paidAmount < ele?.bounce
            ) {
              emiObj.paidBounceCharge = +(
                paidAmount + ele?.currentbounce
              ).toFixed(2);
              transObj.paidBounceCharge = +paidAmount.toFixed(2);
              paidAmount = 0;
            }
          }

          // #4 -> Deferred Int.
          if (
            paidAmount > 0 &&
            ele?.deferred > 0 &&
            paidAmount > ele?.deferred
          ) {
            emiObj.paidRegInterestAmount = +(
              ele?.currentdeferred + ele?.deferred
            ).toFixed(2);
            transObj.paidRegInterestAmount = +(ele?.deferred).toFixed(2);
            paidAmount -= ele?.deferred;
          } else if (
            paidAmount > 0 &&
            ele?.deferred > 0 &&
            paidAmount < ele?.deferred
          ) {
            emiObj.paidRegInterestAmount = +(
              paidAmount + ele?.currentdeferred
            ).toFixed(2);
            transObj.paidRegInterestAmount = +paidAmount.toFixed(2);
            paidAmount = 0;
          }

          // #1 - > Legal Charge
          if (paidAmount > 0 && ele?.legal > 0 && paidAmount > ele?.legal) {
            emiObj.paidLegalCharge = +(ele?.currentlegal + ele?.legal).toFixed(
              2,
            );
            transObj.paidLegalCharge = +(ele?.legal).toFixed(2);
            paidAmount -= ele?.legal;
          } else if (
            paidAmount > 0 &&
            ele?.legal > 0 &&
            paidAmount < ele?.legal
          ) {
            emiObj.paidLegalCharge = +(ele?.currentlegal + paidAmount).toFixed(
              2,
            );
            transObj.paidLegalCharge = +paidAmount.toFixed(2);
            paidAmount = 0;
          }

          for (let key in emiObj) {
            emiObj[key] = Math.round(emiObj[key]);
          }

          if (Object.values(emiObj).length > 1) {
            eBifurcation.push(emiObj);
            tBifurcation.push(transObj);
          }
        }
      } catch (error) {
        console.log({ error });
        successData.push(loanId);
        return { message: 'Error in Catch BCT.', successData };
        break;
      }
    }

    // # Covering PI
    if (Math.round(paidAmount) > 0) {
      for (let index = 0; index < expectedPI.length; index++) {
        try {
          const ele = expectedPI[index];
          let emiObj: any = {
            id: ele?.id,
          };
          let transObj: any = {
            id: ele?.id,
          };

          // #1 Covering Interest
          if (
            paidAmount > 0 &&
            ele?.interest > 0 &&
            paidAmount > ele?.interest
          ) {
            emiObj.paid_interest = +(
              ele?.interest + ele?.currentinterest
            ).toFixed(2);
            transObj.paid_interest = +(ele?.interest).toFixed(2);
            paidAmount -= ele?.interest;
          } else if (
            paidAmount > 0 &&
            ele?.interest > 0 &&
            paidAmount < ele?.interest
          ) {
            emiObj.paid_interest = +(paidAmount + ele?.currentinterest).toFixed(
              2,
            );
            transObj.paid_interest = +paidAmount.toFixed(2);
            paidAmount = 0;
          }

          // #2 Covering Principal
          if (
            paidAmount > 0 &&
            ele?.principal > 0 &&
            paidAmount > ele?.principal
          ) {
            emiObj.paid_principal = +(
              ele?.principal + ele?.currentprincipal
            ).toFixed(2);
            transObj.paid_principal = +(ele?.principal).toFixed(2);
            paidAmount -= ele?.principal;
          } else if (
            paidAmount > 0 &&
            ele?.principal > 0 &&
            paidAmount < ele?.principal
          ) {
            emiObj.paid_principal = +(
              paidAmount + ele?.currentprincipal
            ).toFixed(2);
            transObj.paid_principal = +paidAmount.toFixed(2);
            paidAmount = 0;
          }
          if (Object.values(emiObj).length > 1) {
            eBifurcation.push(emiObj);
            tBifurcation.push(transObj);
          }
          atPI = { loanId, userId, remainingAmount: paidAmount };
        } catch (error) {
          successData.push(loanId);
          return { message: 'Error in Catch BCT.', successData };
        }
      }
    }

    return {
      eBifurcation,
      tBifurcation,
      atPI,
      remainingAmt: paidAmount,
    };
  }

  private prepareTransactionForCreditPay(
    bifurcation,
    loanData,
    emiData,
    userId,
    trans,
    remainingAmt,
    totalAmount,
    successData,
  ) {
    try {
      let transactions = [];
      let isIgst = loanData?.penaltyCharges?.I_GST ?? false;
      const randomCode = this.typeService.generateRandomCode(10);
      let transactionId = EnvConfig.nbfc.nbfcCodeName + randomCode + '_CN';
      let maxDPD = 0;

      emiData.forEach((ele) => {
        if ((ele.penalty_days ?? 0) > 0 && ele.penalty_days > maxDPD)
          maxDPD = +ele.penalty_days;
      });

      for (let index = 0; index < bifurcation.length; index++) {
        try {
          const obj = bifurcation[index];
          if (Object.values(obj).length == 1) continue;
          const loanId = loanData?.id;
          let transId =
            index > 0 ? transactionId + `DMY${index}` : transactionId;
          let amount = 0;
          for (let key in obj) {
            if (key == 'id') continue;
            if (!obj[key]) obj[key] = 0;
            obj[key] = Math.round(obj[key]);
            amount += obj[key];
          }

          const creationData: any = { loanId, userId };
          creationData.paidAmount = amount;
          creationData.transactionId = transId;
          creationData.utr = transId;
          creationData.completionDate = this.typeService
            .getGlobalDate(new Date('2025-03-31T10:00:00.000Z'))
            .toJSON();
          creationData.emiId = obj.id;
          creationData.roundOff = 0;
          creationData.source = kCreditPay;
          creationData.status = kCompleted;
          creationData.subSource = kCreditPay;
          creationData.subStatus = 'CREDIT_SETTLEMENT';

          creationData.principalAmount = obj?.paid_principal ?? 0;
          creationData.interestAmount = obj?.paid_interest ?? 0;
          creationData.penaltyAmount = obj?.paid_penalty ?? 0;
          creationData.regInterestAmount = obj?.paidRegInterestAmount ?? 0;

          creationData.bounceCharge = obj?.paidBounceCharge ?? 0;
          creationData.cgstOnBounceCharge = 0;
          creationData.sgstOnBounceCharge = 0;
          creationData.igstOnBounceCharge = 0;

          creationData.penalCharge = obj?.paidPenalCharge ?? 0;
          creationData.cgstOnPenalCharge = 0;
          creationData.sgstOnPenalCharge = 0;
          creationData.igstOnPenalCharge = 0;

          const legal = this.fetchGstFromAmount(
            obj?.paidLegalCharge ?? 0,
            !isIgst,
          );
          creationData.legalCharge = legal?.pure;
          creationData.cgstOnLegalCharge = legal?.cgst ?? 0;
          creationData.sgstOnLegalCharge = legal?.sgst ?? 0;
          creationData.igstOnLegalCharge = legal?.gst ?? 0;

          creationData.forClosureAmount = 0;
          creationData.sgstForClosureCharge = 0;
          creationData.cgstForClosureCharge = 0;
          creationData.igstForClosureCharge = 0;
          creationData.forClosureDays = 0;
          creationData.accStatus = kCalBySystem;
          creationData.type = kPartPay;
          creationData.adminId = SYSTEM_ADMIN_ID;
          creationData.followerId = loanData?.followerId;
          creationData.remarks = '';
          creationData.maxDPD = maxDPD;
          const approvedAmount = +loanData?.netApprovedAmount;
          const totalFees = loanData?.feesIncome ?? 0;
          const chargePr = ((totalFees ?? 0) * 100) / approvedAmount;
          const principal = obj?.paid_principal ?? 0;
          const income = Math.round((principal * chargePr) / 100);
          creationData.feesIncome = income;
          creationData.createdAt = new Date('2025-03-31 15:49:14.673+00');
          creationData.updatedAt = new Date('2025-03-31 15:49:14.673+00');

          transactions.push(creationData);
          trans.push({
            'Amt to Be Refund': totalAmount,
            'Amt Remaining': remainingAmt,
            ...creationData,
          });
        } catch (error) {
          console.log({ error });
          successData.push(loanData?.id);
          return { message: 'Error in Catch PrepareTran.', successData };
        }
      }
      return transactions;
    } catch (error) {
      console.log({ error });
      successData.push(loanData?.id);
      return { message: 'Error in Catch PrepareTran.', successData };
    }
  }

  //#region markCreditTransAsComplete
  async markCreditTransAsComplete(
    paymentData,
    emiBifurcation,
    amount,
    remainingAmount,
    userId,
    loanId,
    loan,
    successData,
  ) {
    amount = Math.round(amount);
    remainingAmount = Math.round(remainingAmount);
    for (let index = 0; index < paymentData.length; index++) {
      const trans = paymentData[index];
      const emi = emiBifurcation[index];
      const emiId = emi.id;
      delete emi.id;
      if (trans?.paidAmount <= 0) continue;

      // #Trans -> Adding Transaction to Transaction Entity
      let transEntry = await this.transactionRepo.createRowData(trans);
      if (transEntry === k500Error) {
        successData.push(loanId);
        return {
          message: 'Error in transactionRepo.createRowData.',
          successData,
        };
      }

      // #EMI -> Updating Data to EMI Entity
      let emiEntry = await this.emiRepo.updateRowData(emi, emiId, true);
      if (emiEntry === k500Error) {
        successData.push(loanId);
        return { message: 'Error in emiRepo.updateRowData.', successData };
      }

      // #EMI Closing
      let emiUpdate: any = await this.checkAndCloseEmi(transEntry.id, emiId);
      if (emiUpdate === false || emiUpdate?.message) {
        successData.push(loanId);
        return { message: 'Error in this.checkAndCloseEmi.', successData };
      }

      // #Other Operations Need to Perform
      await this.checkUserStatus(loanId);
      await this.reCalculatePaidAmounts({ loanId });
      this.sharedCalculation
        .calculateCLTV({ loanIds: [loanId] })
        .catch((err) => {});
      await this.legalService.funCheckLegalAssingToCollection({ loanId });
    }

    // #USER -> Updating Data to EMI Entity
    const userUpdate = await this.userRepo.updateRowData(
      { totalCredit: amount, remainingCredit: remainingAmount },
      userId,
      true,
    );
    if (userUpdate === k500Error) {
      successData.push(loanId);
      return { message: 'Error in userRepo.updateRowData.', successData };
    }

    // Update Loan
    let data = {};
    let amt = amount ?? 0 - remainingAmount ?? 0;
    if (amt < 0) amt = 0;
    if (loan?.penaltyCharges) {
      data = { ...loan?.penaltyCharges, creditUsed: amt };
    } else {
      data = {
        creditUsed: amt,
      };
    }
    let loanUpdate = await this.loanRepo.updateRowData(
      { penaltyCharges: data },
      loanId,
      true,
    );
    if (loanUpdate === k500Error) {
      successData.push(loanId);
      return { message: 'Error in loanRepo.updateRowData.', successData };
    }

    // Closing Loan If Eligible
    const canCompleteLoan = await this.sharedTransaction.isEligibleForLoanClose(
      loanId,
    );
    if (canCompleteLoan)
      return await this.sharedTransaction.closeTheLoanForCreditUsers({
        loanId,
        userId,
      });
    return true;
  }

  private async checkAndCloseEmi(id: number, emiId: number) {
    try {
      const loanInclude = {
        model: loanTransaction,
        attributes: ['penaltyCharges', 'isPartPayment'],
      };
      const emiAttr = [
        'id',
        'emi_amount',
        'penalty',
        'partPaymentPenaltyAmount',
        'settledId',
        'pay_type',
        'principalCovered',
        'interestCalculate',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
        'payment_status',
        'payment_due_status',
        'loanId',
      ];
      const emiOptions: any = { where: { id: emiId } };
      emiOptions.include = loanInclude;
      const emiData = await this.emiService.getRowWhereData(
        emiAttr,
        emiOptions,
      );
      if (!emiData || emiData == k500Error) return false;

      // for old users bounce charge is alreafy included in penalty
      if (!emiData?.loan?.penaltyCharges?.MODIFICATION_CALCULATION)
        emiData.bounceCharge = 0;

      if (emiData?.payment_status == '1') return true;
      const att = [
        'id',
        'paidAmount',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'cgstOnBounceCharge',
        'sgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'cgstOnLegalCharge',
        'sgstOnLegalCharge',
        'igstOnLegalCharge',
        'completionDate',
      ];
      const option = { where: { emiId, status: kCompleted } };
      const transactionData = await this.transactionRepo.getTableWhereData(
        att,
        option,
      );
      if (!transactionData || transactionData == k500Error) return false;
      let emiPaidAmount = 0;
      let emiPaidPenalty = 0;
      let emiPaidCharges = 0;
      let idPaidPenaltyAmount = 0;
      let payment_done_date;
      transactionData.forEach((element) => {
        try {
          emiPaidAmount += element?.principalAmount ?? 0;
          emiPaidAmount += element?.interestAmount ?? 0;
          emiPaidPenalty += element?.penaltyAmount ?? 0;
          emiPaidCharges += element?.regInterestAmount ?? 0;
          emiPaidCharges += element?.bounceCharge ?? 0;
          emiPaidCharges += element?.sgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.cgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.igstOnBounceCharge ?? 0;
          emiPaidCharges += element?.penalCharge ?? 0;
          emiPaidCharges += element?.sgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.cgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.igstOnPenalCharge ?? 0;
          emiPaidCharges += element?.legalCharge ?? 0;
          emiPaidCharges += element?.cgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.sgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.igstOnLegalCharge ?? 0;
          if (element.id === id) {
            idPaidPenaltyAmount = element?.penaltyAmount ?? 0;
            payment_done_date = element.completionDate;
          }
        } catch (error) {}
      });

      let emiAmount =
        (emiData?.principalCovered ?? 0) + (emiData?.interestCalculate ?? 0);
      emiAmount -= emiPaidAmount;
      let emiCharges =
        this.typeService.manageAmount(emiData?.regInterestAmount ?? 0) +
        this.typeService.manageAmount(emiData?.bounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.gstOnBounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.dpdAmount ?? 0) +
        this.typeService.manageAmount(emiData?.penaltyChargesGST ?? 0) +
        this.typeService.manageAmount(emiData?.legalCharge ?? 0) +
        this.typeService.manageAmount(emiData?.legalChargeGST ?? 0);
      emiCharges -= emiPaidCharges;
      const partPenaltyAmount = +(emiData?.partPaymentPenaltyAmount ?? 0);
      let emi_amount = +(emiData.emi_amount ?? 0);
      if (emi_amount > emiAmount) emi_amount = emiAmount;
      let penalty = +(emiData.penalty ?? 0);
      const partPaymentPenaltyAmount = emiPaidPenalty;
      if (
        idPaidPenaltyAmount &&
        !(
          partPenaltyAmount + 10 > emiPaidPenalty &&
          partPenaltyAmount - 10 < emiPaidPenalty
        )
      )
        penalty -= idPaidPenaltyAmount;

      if (emi_amount < 0) emi_amount = 0;
      if (penalty < 0) penalty = 0;
      if (emiCharges < 0) emiCharges = 0;

      const updatedData: any = {};
      if (emi_amount <= 10 && penalty <= 10 && emiCharges <= 10) {
        emi_amount = +(emiData.emi_amount ?? 0);
        updatedData.payment_done_date = payment_done_date;
        updatedData.payment_status = '1';
      }
      updatedData.emi_amount = emi_amount;
      updatedData.penalty = penalty;
      updatedData.partPaymentPenaltyAmount = partPaymentPenaltyAmount;
      if (updatedData.payment_status === '1')
        updatedData.pay_type = emiData?.pay_type ?? 'EMIPAY';
      const updateResponse = await this.emiService.updateRowData(
        updatedData,
        emiId,
      );
      if (updateResponse == k500Error) return false;
      // Disabling The Part Pay Option (if it's enabled by admin)
      if (
        emiData?.payment_due_status === '1' &&
        updatedData.payment_status === '1' &&
        emiData?.loan?.isPartPayment
      ) {
        const emiList = await this.emiRepo.getTableWhereData(
          ['payment_status', 'payment_due_status'],
          {
            where: { loanId: emiData?.loanId },
          },
        );
        if (emiList == k500Error) return kInternalError;
        let canUpdate = true;
        emiList.forEach((ele) => {
          if (ele?.payment_status == '0' && ele?.payment_due_status == '1')
            canUpdate = false;
        });
        if (canUpdate) {
          const update = await this.loanRepo.updateRowData(
            { isPartPayment: 0, partPayEnabledBy: SYSTEM_ADMIN_ID },
            emiData?.loanId,
          );
          if (update == k500Error) return kInternalError;
        }
      }
    } catch (error) {
      return false;
    }
  }

  //#region fetchAmountWithGst
  fetchGstFromAmount(x: number, isTwoGst = false) {
    const original = x;
    let gstRaw = x - x / 1.18;
    let pureRaw = x - gstRaw;

    let pure = Math.round(pureRaw);
    let remaining = original - pure;
    if (remaining % 2 !== 0) {
      pure -= 1;
      remaining = original - pure;
    }

    if (isTwoGst) {
      let cgst = remaining / 2;
      let sgst = remaining / 2;
      return {
        pure,
        cgst,
        sgst,
      };
    }

    const gst = remaining;
    return {
      pure,
      gst,
    };
  }

  private async checkUserStatus(loanId) {
    try {
      // Table joins
      const emiInclude: { model; attributes? } = { model: EmiEntity };
      emiInclude.attributes = ['payment_due_status', 'payment_status'];
      // Query preparation
      const include = [emiInclude];
      const attributes = ['userId'];
      let options: any = { limit: 1, include, where: { id: loanId } };
      // Query
      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      const onTimeUsers = [];
      const delayedUsers = [];
      const defaultedUsers = [];
      loanList.forEach((el) => {
        try {
          const userId = el.userId;
          const emiList: any = el.emiData ?? {};
          const isDelayed = emiList.find((el) => el.payment_due_status == '1');
          // On time
          if (!isDelayed) onTimeUsers.push(userId);
          else {
            const isDefaulter = emiList.find(
              (el) => el.payment_due_status == '1' && el.payment_status == '0',
            );
            if (isDefaulter) defaultedUsers.push(userId);
            else delayedUsers.push(userId);
          }
        } catch (error) {}
      });
      // Update defaulter users
      if (defaultedUsers.length > 0) {
        const updatedData = { loanStatus: 3 };
        options = { where: { id: defaultedUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
      // Update delayed users
      if (delayedUsers.length > 0) {
        const updatedData = { loanStatus: 2 };
        options = { where: { id: delayedUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
      // Update on time users
      if (onTimeUsers.length > 0) {
        const updatedData = { loanStatus: 1 };
        options = { where: { id: onTimeUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
    } catch (error) {}
  }

  async reCalculatePaidAmounts(body) {
    const loanId = body?.loanId;
    if (!loanId) return kParamMissing('loanId');

    // Loan Data with EMI data and Transaction data
    const traAttrs = [
      'id',
      'emiId',
      'status',
      'type',
      'paidAmount',
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'transactionId',
      'legalCharge',
      'cgstOnLegalCharge',
      'sgstOnLegalCharge',
      'igstOnLegalCharge',
      'penalCharge',
      'cgstOnPenalCharge',
      'sgstOnPenalCharge',
      'igstOnPenalCharge',
      'regInterestAmount',
      'bounceCharge',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'forClosureAmount',
      'cgstForClosureCharge',
      'sgstForClosureCharge',
      'igstForClosureCharge',
    ];
    const emiAttr = [
      'id',
      'fullPayPrincipal',
      'fullPayPenalty',
      'fullPayInterest',
      'fullPayLegalCharge',
      'fullPayPenal',
      'fullPayRegInterest',
      'fullPayBounce',
      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
    ];
    const transInc = {
      model: TransactionEntity,
      attributes: traAttrs,
      where: { status: kCompleted },
    };
    const emiInc = { model: EmiEntity, attributes: emiAttr };
    const options = { where: { id: loanId }, include: [emiInc, transInc] };
    const loanData = await this.loanRepo.getRowWhereData(['id'], options);
    if (loanData === k500Error) throw new Error();
    if (!loanData) return {};
    const originalTrans = await this.commonSharedService.filterTransActionData(
      loanData?.transactionData ?? [],
    );
    loanData.transactionData = originalTrans;
    await this.updatePaidEMIBifurcation(loanData);
    return loanData;
  }

  private async updatePaidEMIBifurcation(loanData) {
    const transData = loanData?.transactionData ?? [];
    if (transData.length == 0) return {};
    const emiData = loanData?.emiData;
    for (let i = 0; i < emiData.length; i++) {
      const emi = emiData[i];
      const emiId = emi.id;
      let paid_principal = emi?.fullPayPrincipal ?? 0;
      let paid_interest = emi?.fullPayInterest ?? 0;
      let paid_penalty = emi?.fullPayPenalty ?? 0;
      let paidLegalCharge = emi?.fullPayLegalCharge ?? 0;
      let paidPenalCharge = emi?.fullPayPenal ?? 0;
      let paidRegInterestAmount = emi?.fullPayRegInterest ?? 0;
      let paidBounceCharge = emi?.fullPayBounce ?? 0;
      let forClosureAmount = emi?.forClosureAmount ?? 0;
      let cgstForClosureCharge = emi?.cgstForClosureCharge ?? 0;
      let sgstForClosureCharge = emi?.sgstForClosureCharge ?? 0;
      let igstForClosureCharge = emi?.igstForClosureCharge ?? 0;
      const trans = transData.filter((f) => f?.emiId == emiId);
      trans.forEach((tra) => {
        if (tra?.type != kFullPay) {
          const bounceCharge =
            (tra?.bounceCharge ?? 0) +
            (tra?.sgstOnBounceCharge ?? 0) +
            (tra?.cgstOnBounceCharge ?? 0) +
            (tra?.igstOnBounceCharge ?? 0);
          const penalCharge =
            (tra?.penalCharge ?? 0) +
            (tra?.sgstOnPenalCharge ?? 0) +
            (tra?.cgstOnPenalCharge ?? 0) +
            (tra?.igstOnPenalCharge ?? 0);
          const legalCharge =
            (tra?.legalCharge ?? 0) +
            (tra?.sgstOnLegalCharge ?? 0) +
            (tra?.cgstOnLegalCharge ?? 0) +
            (tra?.igstOnLegalCharge ?? 0);

          paid_principal += tra?.principalAmount ?? 0;
          paid_interest += tra?.interestAmount ?? 0;
          paid_penalty += tra?.penaltyAmount ?? 0;
          paidRegInterestAmount += tra?.regInterestAmount ?? 0;
          paidBounceCharge += bounceCharge;
          paidPenalCharge += penalCharge;
          paidLegalCharge += legalCharge;
          forClosureAmount += tra?.forClosureAmount ?? 0;
          cgstForClosureCharge += tra?.cgstForClosureCharge ?? 0;
          sgstForClosureCharge += tra?.sgstForClosureCharge ?? 0;
          igstForClosureCharge += tra?.igstForClosureCharge ?? 0;
        }
      });
      paid_penalty = +paid_penalty.toFixed(2);
      paidRegInterestAmount = +paidRegInterestAmount.toFixed(2);
      paidBounceCharge = +paidBounceCharge.toFixed(2);
      paidPenalCharge = +paidPenalCharge.toFixed(2);
      paidLegalCharge = +paidLegalCharge.toFixed(2);
      const updatedEMI = {
        paid_principal,
        paid_interest,
        paid_penalty,
        paidRegInterestAmount,
        paidBounceCharge,
        paidPenalCharge,
        paidLegalCharge,
        forClosureAmount,
        cgstForClosureCharge,
        sgstForClosureCharge,
        igstForClosureCharge,
      };
      await this.emiRepo.updateRowData(updatedEMI, emiId);
    }
    return {};
  }

  async useCredit(data, isDirectCreditTrans = false) {
    const thisTransCreditAmount = data?.thisTransCreditAmount ?? 0;
    const paymentData = data?.paymentData ?? 0;
    const loanId = paymentData?.loanId;
    // For This, thisTransCreditAmount is paidAmount
    let paidAmount = thisTransCreditAmount;
    const userId = paymentData?.userId;
    const transactionData = data?.transactionData;
    const transactionType = transactionData?.type;
    let usedCredit = 0;

    // #1 Find Remaining Credit
    const user = await this.userRepo.getRowWhereData(['remainingCredit'], {
      where: {
        id: userId,
      },
    });
    if (user == k500Error)
      throw new Error('Error in Getting remainingCredit from User Table');
    const remainingCredit = user?.remainingCredit;
    if (remainingCredit == 0) return {};

    if (!isDirectCreditTrans) {
      // Get GST Type //# will add this data to REDIS #todo
      const loan = await this.loanRepo.getRowWhereData(['penaltyCharges'], {
        where: { id: loanId },
      });
      if (loan === k500Error) throw new Error('Error in Getting Loan Data');
      const iGst = loan?.penaltyCharges?.I_GST ?? false;

      // #4 Preparing Bifurcation for Credit Amount
      let bifurcationList =
        transactionType === kFullPay
          ? await this.splitTransForCreditPay(paidAmount, loanId, iGst)
          : await this.splitTransaction(paidAmount, loanId, iGst, true);
      bifurcationList = bifurcationList.filter((obj) => obj.paidAmount > 0);

      if (bifurcationList.message) return bifurcationList;

      // #5 Preparing Transaction to Update Direclty -> COMPLETED
      const randomCode = this.typeService.generateRandomCode(10);
      let transactionId = EnvConfig.nbfc.nbfcCodeName + randomCode + '_CN';
      const updatedTransactions = bifurcationList.map((item, index) => {
        const baseData = {
          ...transactionData,
        };
        delete baseData.id;
        const tail = index === 0 ? `` : `DMY${index + 1}`;
        let transId = transactionId + tail;
        return {
          ...baseData,
          ...item,
          status: kCompleted,
          source: kCredit,
          subSource: kCreditPay,
          type: kPartPay,
          subStatus: kCreditTransaction,
          completionDate: this.typeService.getGlobalDate(new Date()).toJSON(),
          transactionId: transId,
          utr: transId,
        };
      });

      // #6 Adding Completed Transaction
      const bulkEntries = await this.repoManager.bulkCreate(
        TransactionEntity,
        updatedTransactions,
      );
      if (bulkEntries === k500Error)
        throw new Error('Error in Adding Bulk Trans. Entry');

      // #7 Iterating Over Created Trans. Entry to Update in EMIs
      for (let index = 0; index < bulkEntries.length; index++) {
        const element = bulkEntries[index];
        const emiId = element?.emiId;
        // Checking Can We Close EMI
        const updatingEmi: any = await this.checkAndUpdateEMIPartPayCredit(
          element?.id,
          emiId,
        );
        if (updatingEmi?.message || updatingEmi === false) return updatingEmi;
      }
      // Updating Paid Columns in EMI Table
      // const updatingEmiPaidAmt = await this.reCalculatePaidAmounts({
      //   loanId,
      // });
      // if (updatingEmiPaidAmt?.mesasge) return updatingEmiPaidAmt;
      usedCredit += paidAmount;
    } else usedCredit = transactionData?.paidAmount ?? 0;

    // // #8 Updating User's Remaining Credit
    let credit = remainingCredit - usedCredit;
    if (credit < 0) credit = 0;
    const updateUser = await this.userRepo.updateRowData(
      { remainingCredit: credit },
      userId,
    );
    if (updateUser == k500Error)
      throw new Error('Error in Updating remainingCredit in User Table');
    return true;
  }

  //#region Covering Waiver
  async coverWaiverByCredit(data) {
    if (data?.dummyPayment) return {};
    const loanId = data?.loanId;
    const userId = data?.userId;
    let usedCredit = 0;

    // #1 Find Remaining Credit
    const user = await this.userRepo.getRowWhereData(['remainingCredit'], {
      where: {
        id: userId,
      },
    });
    if (user == k500Error)
      throw new Error('Error in Getting remainingCredit from User Table');
    const remainingCredit = user?.remainingCredit;
    if (remainingCredit == 0) return {};

    if (remainingCredit > 0) {
      // #2 Find Loan's Waiver
      let attributes: any = [
        [
          fn(
            'SUM',
            literal(
              `COALESCE("waiver", 0) + COALESCE("paid_waiver", 0) + COALESCE("unpaid_waiver", 0)`,
            ),
          ),
          'totalWaiver',
        ],
      ];
      const waiverData = await this.emiRepo.getTableWhereData(attributes, {
        where: {
          loanId,
        },
      });
      if (waiverData === k500Error)
        throw new Error('Error in Getting Waiver Data from EMI Table');
      let waiverAmount = waiverData[0]?.totalWaiver ?? 0;
      if (waiverAmount === 0) return {};

      // #3 Covering Waiver First
      if (waiverAmount > 0) {
        if (waiverAmount > remainingCredit) waiverAmount = remainingCredit;
        const coverWaiver = await this.coverWaiverFromCreditAmount({
          waiverAmount,
          loanId,
        });
        if (coverWaiver?.mesasge) coverWaiver;
        usedCredit += waiverAmount;
      }

      // return {};

      let credit = remainingCredit - usedCredit;
      if (credit < 0) credit = 0;
      const updateUser = await this.userRepo.updateRowData(
        { remainingCredit: credit },
        userId,
      );
      if (updateUser == k500Error)
        throw new Error('Error in Updating remainingCredit in User Table');
    }
    return true;
  }

  //#region coverWaiverFromCreditAmount
  async coverWaiverFromCreditAmount(data) {
    if (data?.dummyPayment) return {};
    const waiverAmount = data?.waiverAmount;
    const loanId = data?.loanId;

    // #1 Initailzing Waiver Payment
    const initWaiverEntry = await this.createWaiverPaymentOrder({
      adminId: SYSTEM_ADMIN_ID,
      loanId: loanId,
      phone: null,
      email: null,
      amount: waiverAmount,
      dueDate: null,
      source: kRazorpay,
      subSource: kDirectBankPay,
      payment_date: null,
      transactionId: null,
      utr: null,
      submissionDate: this.typeService.getGlobalDate(new Date()).toJSON(),
      isCloseLoan: false,
      sendSMS: false,
      isUsingCredit: true,
    });
    // return {};
    if (initWaiverEntry?.mesasge) return initWaiverEntry;

    // #2 Completing Waiver Payment
    const transData = initWaiverEntry?.result;
    const paymentData: any = {
      id: transData.id,
      status: kCompleted,
    };
    paymentData.response = null;
    paymentData.utr = 'utr_' + transData.transactionId;
    paymentData.completionDate = this.typeService
      .getGlobalDate(new Date())
      .toJSON();
    paymentData.paymentTime = this.typeService.getIstTime(new Date()).toJSON();
    paymentData.type = transData.type;
    paymentData.loanId = transData.loanId;
    paymentData.userId = transData.userId;
    paymentData.subSource = transData.subSource;
    paymentData.dummyPayment = true;
    const updatePayment =
      await this.sharedTransaction.markTransactionAsComplete(paymentData);
    if (updatePayment?.mesasge) return updatePayment;
    return true;
  }

  //#region create payment
  async createWaiverPaymentOrder(body) {
    try {
      body.sendSMS = body?.sendSMS ?? false;
      if (!body.loanId) return kParamMissing('loanId');
      if (body.transactionId && (!body.adminId || !body.utr))
        return kParamsMissing;
      const source = body?.source ?? kRazorpay;
      const subSource = body?.subSource ?? 'WEB';
      body.source = source;
      body.subSource = subSource;
      if (body.source) body.source = body.source.toUpperCase();
      if (body.subSource) body.subSource = body.subSource.toUpperCase();
      if (
        source != kCashfree &&
        source != kRazorpay &&
        source != 'UPI' &&
        source != 'UPI2'
      )
        return k422ErrorMessage(kWrongSourceType);
      const keys = Object.keys(body);
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index];
        const value = body[key];
        if (value == null) delete body[key];
      }
      // if payments go update then check rights
      if (body?.adminId && body?.transactionId && body?.utr) {
        const checkAccess =
          await this.sharedTransaction.checkAdminAccessToUpdatePayments(
            'payment update',
            body?.adminId,
          );
        if (checkAccess !== true) return checkAccess;
      }

      // find loan Data
      const loanData: any = await this.sharedTransaction.getLoanDataWaiver(
        body,
      );
      if (loanData?.message) return loanData;
      const prePareData: any = await this.prePareAmountForWaiver(
        loanData,
        body,
      );
      if (prePareData?.message) return prePareData;
      const prePareAmount = prePareData?.finalData;
      const result: any = await this.prePareWaiverTransaction(
        body,
        prePareAmount,
        loanData,
      );
      if (result?.message) return result;
      if (
        body?.adminId &&
        body?.transactionId &&
        body?.utr &&
        (body?.source == 'UPI' || body?.source == 'UPI2')
      )
        await this.sharedTransaction.checkAndUpdatePaymenst(body);
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // prepare and create transaction
  private async prePareWaiverTransaction(
    body: any,
    prePareAmount: any,
    loanData,
  ) {
    try {
      // Params preparation
      const userId = loanData?.registeredUsers?.id;
      const loanId = body.loanId;
      const phone = body?.phone ?? loanData?.registeredUsers?.phone;
      const email = body?.email ?? loanData?.registeredUsers?.email;
      const amount = prePareAmount?.paidAmount;
      body.dueAmount = amount;
      const name = loanData?.registeredUsers?.fullName;
      const isUsingCredit = body?.isUsingCredit ?? false;
      const subSource = isUsingCredit ? kCreditPay : body?.subSource ?? 'WEB';
      const source = isUsingCredit ? kCredit : body?.source ?? kCashfree;
      const maxDPD = prePareAmount?.maxDPD;
      const preparedData: any = {
        amount,
        email,
        phone,
        name,
        loanId,
        subSource,
      };
      body.p_type = prePareAmount.type;

      // Order preparation
      const creationData: any = { loanId, userId };
      creationData.paidAmount = amount;
      creationData.source = source;
      creationData.status = 'INITIALIZED';
      creationData.subSource = subSource;
      creationData.principalAmount = prePareAmount?.principalAmount;

      creationData.interestAmount = prePareAmount?.interestAmount;

      creationData.regInterestAmount = prePareAmount?.regInterestAmount;

      creationData.bounceCharge = prePareAmount?.bounceCharge;
      creationData.cgstOnBounceCharge = prePareAmount?.cgstOnBounceCharge;
      creationData.sgstOnBounceCharge = prePareAmount?.sgstOnBounceCharge;
      creationData.igstOnBounceCharge = prePareAmount?.igstOnBounceCharge;

      creationData.penaltyAmount = prePareAmount?.penaltyAmount;

      creationData.penalCharge = prePareAmount?.penalCharge;
      creationData.cgstOnPenalCharge = prePareAmount?.cgstOnPenalCharge;
      creationData.sgstOnPenalCharge = prePareAmount?.sgstOnPenalCharge;
      creationData.igstOnPenalCharge = prePareAmount?.igstOnPenalCharge;

      creationData.legalCharge = prePareAmount?.legalCharge;
      creationData.cgstOnLegalCharge = prePareAmount?.cgstOnLegalCharge;
      creationData.sgstOnLegalCharge = prePareAmount?.sgstOnLegalCharge;
      creationData.igstOnLegalCharge = prePareAmount?.igstOnLegalCharge;

      creationData.accStatus = kCalBySystem;
      creationData.type = prePareAmount.type;
      creationData.adminId = body?.adminId ?? SYSTEM_ADMIN_ID;
      creationData.followerId = loanData?.followerId;
      creationData.maxDPD = maxDPD;
      if (prePareAmount?.settled_type)
        creationData.settled_type = prePareAmount?.settled_type;
      if (prePareAmount.emiId != -1) creationData.emiId = prePareAmount.emiId;
      // const createdData = await this.sharedTransaction.createLink(
      //   body,
      //   preparedData,
      // );
      // if (createdData === k500Error) return kInternalError;
      // else if (createdData?.statusCode) return createdData;
      const randomCode = this.typeService.generateRandomCode(10);
      let transactionId = EnvConfig.nbfc.nbfcCodeName + randomCode + '_CN';
      creationData.transactionId = transactionId;
      creationData.response = null;

      // Order creation in database
      const isCheck = await this.sharedTransaction.checkTransactionId(
        creationData,
      );
      if (isCheck) return k422ErrorMessage(kTransactionIdExists);

      const result = await this.transactionRepo.createRowData(creationData);
      if (!result || result === k500Error) return kInternalError;
      // const link = createdData.payLink;
      // await this.sendNotification(link, body, loanData);
      return { result };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region  prePare amount for waiver
  async prePareAmountForWaiver(loanData, body) {
    try {
      let amount = body?.amount ?? 0;
      let unPaidWaiveAmount = 0;
      let loanId = body?.loanId;
      const emiList = [];
      let maxDPD = 0;
      const iGst = loanData?.penaltyCharges?.I_GST ?? false;

      const emiData = await this.emiRepo.getTableWhereData(['penalty_days'], {
        where: { loanId, payment_due_status: '1' },
      });
      if (emiData === k500Error) return kInternalError;

      emiData.forEach((ele) => {
        if ((ele.penalty_days ?? 0) > 0 && ele.penalty_days > maxDPD)
          maxDPD = +ele.penalty_days;
      });
      /// pre pare emi amount paid and remanige
      loanData?.emiData.sort((a, b) => a.id - b.id);
      loanData?.emiData.forEach((ele) => {
        if ((ele?.totalPenalty ?? 0) > 0) ele.bounceCharge = 0;
        let waiveAmount = 0;
        waiveAmount += ele?.waiver;
        waiveAmount += ele?.paid_waiver;
        waiveAmount += ele?.unpaid_waiver;
        let paidPrincipal = ele?.fullPayPrincipal ?? 0;
        let paidInterest = ele?.fullPayInterest ?? 0;
        let paidPenalty = ele?.fullPayPenalty ?? 0;
        let paidRegInterest = ele?.fullPayRegInterest ?? 0;
        let paidBounce = ele?.fullPayBounce ?? 0;
        let paidPenal = ele?.fullPayPenal ?? 0;
        let paidLegal = ele?.fullPayLegalCharge ?? 0;

        try {
          const filter = loanData.transactionData.filter(
            (f) => f.emiId == ele.id,
          );
          filter.forEach((tran) => {
            paidPrincipal += tran?.principalAmount ?? 0;
            paidInterest += tran?.interestAmount ?? 0;
            paidPenalty += tran?.penaltyAmount ?? 0;
            paidRegInterest += tran?.regInterestAmount ?? 0;
            paidBounce += tran?.bounceCharge ?? 0;
            paidBounce += tran?.sgstOnBounceCharge ?? 0;
            paidBounce += tran?.cgstOnBounceCharge ?? 0;
            paidBounce += tran?.igstOnBounceCharge ?? 0;
            paidPenal += tran?.penalCharge ?? 0;
            paidPenal += tran?.sgstOnPenalCharge ?? 0;
            paidPenal += tran?.cgstOnPenalCharge ?? 0;
            paidPenal += tran?.igstOnPenalCharge ?? 0;
            paidLegal += tran?.legalCharge ?? 0;
            paidLegal += tran?.cgstOnLegalCharge ?? 0;
            paidLegal += tran?.sgstOnLegalCharge ?? 0;
            paidLegal += tran?.igstOnLegalCharge ?? 0;
          });
        } catch (error) {}
        paidPrincipal = this.typeService.manageAmount(paidPrincipal);
        paidInterest = this.typeService.manageAmount(paidInterest);
        paidPenalty = this.typeService.manageAmount(paidPenalty);
        unPaidWaiveAmount += waiveAmount;
        let diffPrincipal = ele.principalCovered - paidPrincipal;
        if (diffPrincipal < 0) {
          paidPrincipal += diffPrincipal;
          paidInterest += -1 * diffPrincipal;
          diffPrincipal = 0;
        }
        let diffInterest = ele.interestCalculate - paidInterest;
        if (diffPrincipal > 0) waiveAmount -= diffPrincipal;
        if (diffInterest > 0) waiveAmount -= diffInterest;
        if (diffInterest < 0) {
          paidInterest += diffInterest;
          paidPenalty += -1 * diffInterest;
          diffInterest = 0;
        }

        let diffPenalty = waiveAmount;

        let diffRegInterest = this.typeService.manageAmount(
          ele?.waived_regInterest ?? 0,
        );
        if (diffRegInterest < 0) diffRegInterest = 0;
        diffPenalty -= diffRegInterest;

        let diffBounce = this.typeService.manageAmount(ele?.waived_bounce ?? 0);
        if (diffBounce < 0) diffBounce = 0;
        diffPenalty -= diffBounce;

        let diffPenal = this.typeService.manageAmount(ele?.waived_penal ?? 0);
        if (diffPenal < 0) diffPenal = 0;
        diffPenalty -= diffPenal;

        let diffLegal = this.typeService.manageAmount(ele?.waived_legal ?? 0);
        if (diffLegal < 0) diffLegal = 0;
        diffPenalty -= diffLegal;

        waiveAmount = 0;
        diffPrincipal = Math.round(diffPrincipal);
        diffInterest = Math.round(diffInterest);
        diffPenalty = Math.round(diffPenalty);

        if (diffPenalty < 0) diffPenalty = 0;
        emiList.push({
          id: ele.id,
          paidPrincipal,
          paidInterest,
          paidPenalty,
          paidRegInterest,
          paidBounce,
          paidPenal,
          paidLegal,
          diffPrincipal,
          diffInterest,
          diffPenalty,
          diffRegInterest,
          diffBounce,
          diffPenal,
          diffLegal,
          waiveAmount,
          coverPrincipal: 0,
          coverInterest: 0,
          coverPenalty: 0,
          coverRegInterest: 0,
          coverBounce: 0,
          coverPenal: 0,
          coverLegal: 0,
          emi: ele,
        });
      });

      if (amount == 0 || amount > unPaidWaiveAmount) amount = unPaidWaiveAmount;
      let finalData = {
        paidAmount: +amount,
        principalAmount: 0,
        interestAmount: 0,
        penaltyAmount: 0,
        regInterestAmount: 0,
        bounceCharge: 0,
        penalCharge: 0,
        legalCharge: 0,
        cgstOnBounceCharge: 0,
        sgstOnBounceCharge: 0,
        igstOnBounceCharge: 0,
        cgstOnPenalCharge: 0,
        sgstOnPenalCharge: 0,
        igstOnPenalCharge: 0,
        cgstOnLegalCharge: 0,
        sgstOnLegalCharge: 0,
        igstOnLegalCharge: 0,
        emiId: null,
        type: 'FULLPAY',
        settled_type: 'WAIVER_SETTLED',
        maxDPD,
      };
      let principalAmount = 0;
      let interestAmount = 0;
      let penaltyAmount = 0;
      let regInterestAmount = 0;
      let bounceCharge = 0;
      let penalCharge = 0;
      let legalCharge = 0;
      let sgstOnBounceCharge = 0;
      let cgstOnBounceCharge = 0;
      let igstOnBounceCharge = 0;
      let sgstOnPenalCharge = 0;
      let cgstOnPenalCharge = 0;
      let igstOnPenalCharge = 0;
      let sgstOnLegalCharge = 0;
      let cgstOnLegalCharge = 0;
      let igstOnLegalCharge = 0;
      let deductedBounceCharge = 0;
      let deductedPenalCharge = 0;
      let deductedLegalCharge = 0;
      if (amount != 0 && amount <= unPaidWaiveAmount) {
        emiList.forEach((emi) => {
          //penal charge update
          if (emi?.diffPenal >= amount) {
            penalCharge = amount;
            emi.coverPenal = amount;
            amount = 0;
          } else {
            penalCharge = emi?.diffPenal;
            emi.coverPenal = emi?.diffPenal;
            amount -= emi?.diffPenal;
          }
          deductedPenalCharge = penalCharge;

          //bounce charge update
          if (emi?.diffBounce >= amount) {
            bounceCharge = amount;
            emi.coverBounce = amount;
            amount = 0;
          } else {
            bounceCharge = emi?.diffBounce;
            emi.coverBounce = emi?.diffBounce;
            amount -= emi?.diffBounce;
          }
          deductedBounceCharge = bounceCharge;

          // Deffered Amount update
          if (emi?.diffRegInterest >= amount) {
            regInterestAmount = amount;
            emi.coverRegInterest = amount;
            amount = 0;
          } else {
            regInterestAmount = emi?.diffRegInterest;
            emi.coverRegInterest = emi?.diffRegInterest;
            amount -= emi?.diffRegInterest;
          }

          // penalty Amount update
          if (emi?.diffPenalty >= amount) {
            penaltyAmount = amount;
            emi.coverPenalty = amount;
            amount = 0;
          } else {
            penaltyAmount = emi?.diffPenalty;
            emi.coverPenalty = emi?.diffPenalty;
            amount -= emi?.diffPenalty;
          }

          //legal charge update
          if (emi?.diffLegal >= amount) {
            legalCharge = amount;
            emi.coverLegal = amount;
            amount = 0;
          } else {
            legalCharge = emi?.diffLegal;
            emi.coverLegal = emi?.diffLegal;
            amount -= emi?.diffLegal;
          }
          deductedLegalCharge = legalCharge;

          // principal Amount update
          // if (emi?.diffPrincipal >= amount) {
          //   principalAmount = amount;
          //   emi.coverPrincipal = amount;
          //   amount = 0;
          // } else {
          //   principalAmount = emi?.diffPrincipal;
          //   emi.coverPrincipal = emi.diffPrincipal;
          //   amount -= emi?.diffPrincipal;
          // }

          // // interest Amount update
          // if (emi?.diffInterest >= amount) {
          //   interestAmount = amount;
          //   emi.coverInterest = amount;
          //   amount = 0;
          // } else {
          //   interestAmount = emi?.diffInterest;
          //   emi.coverInterest = emi.diffInterest;
          //   amount -= emi?.diffInterest;
          // }

          //gst calculation
          const gstBounceChargeAmount = 0;
          let gstPenalChargeAmount = 0;
          sgstOnPenalCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstPenalChargeAmount / 2);
          cgstOnPenalCharge = sgstOnPenalCharge;
          igstOnPenalCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstPenalChargeAmount);
          gstPenalChargeAmount =
            sgstOnPenalCharge + cgstOnPenalCharge + igstOnPenalCharge;

          const gstLegalChargeAmount = legalCharge - legalCharge / 1.18;

          bounceCharge = bounceCharge - gstBounceChargeAmount;
          penalCharge = penalCharge - gstPenalChargeAmount;
          legalCharge = legalCharge - gstLegalChargeAmount;

          sgstOnBounceCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstBounceChargeAmount / 2);
          cgstOnBounceCharge = sgstOnBounceCharge;
          igstOnBounceCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstBounceChargeAmount);

          sgstOnLegalCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstLegalChargeAmount / 2);
          cgstOnLegalCharge = sgstOnLegalCharge;
          igstOnLegalCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstLegalChargeAmount);

          //Add difference back to charges
          bounceCharge +=
            deductedBounceCharge -
            (bounceCharge +
              sgstOnBounceCharge +
              cgstOnBounceCharge +
              igstOnBounceCharge);

          penalCharge +=
            deductedPenalCharge -
            (penalCharge +
              sgstOnPenalCharge +
              cgstOnPenalCharge +
              igstOnPenalCharge);

          legalCharge +=
            deductedLegalCharge -
            (legalCharge +
              sgstOnLegalCharge +
              cgstOnLegalCharge +
              igstOnLegalCharge);

          regInterestAmount = +regInterestAmount.toFixed(2);
          bounceCharge = +bounceCharge.toFixed(2);
          penalCharge = +penalCharge.toFixed(2);
          legalCharge = +legalCharge.toFixed(2);
          if (
            bounceCharge +
              cgstOnBounceCharge +
              sgstOnBounceCharge +
              igstOnBounceCharge >
            590
          )
            bounceCharge -=
              bounceCharge + cgstOnBounceCharge + sgstOnBounceCharge - 590;

          // finalData.principalAmount += principalAmount;
          // finalData.interestAmount += interestAmount;
          finalData.penaltyAmount += penaltyAmount;
          finalData.regInterestAmount += regInterestAmount;
          finalData.bounceCharge += bounceCharge;
          finalData.penalCharge += penalCharge;
          finalData.legalCharge += legalCharge;
          finalData.cgstOnBounceCharge += cgstOnBounceCharge;
          finalData.igstOnBounceCharge += igstOnBounceCharge;
          finalData.sgstOnBounceCharge += sgstOnBounceCharge;
          finalData.cgstOnPenalCharge += cgstOnPenalCharge;
          finalData.sgstOnPenalCharge += sgstOnPenalCharge;
          finalData.igstOnPenalCharge += igstOnPenalCharge;
          finalData.cgstOnLegalCharge += cgstOnLegalCharge;
          finalData.sgstOnLegalCharge += sgstOnLegalCharge;
          finalData.igstOnLegalCharge += igstOnLegalCharge;
        });

        emiList.forEach((emi) => {
          // interest Amount update
          if (emi?.diffInterest >= amount) {
            interestAmount = amount;
            emi.coverInterest = amount;
            amount = 0;
          } else {
            interestAmount = emi?.diffInterest;
            emi.coverInterest = emi.diffInterest;
            amount -= emi?.diffInterest;
          }

          // principal Amount update
          if (emi?.diffPrincipal >= amount) {
            principalAmount = amount;
            emi.coverPrincipal = amount;
            amount = 0;
          } else {
            principalAmount = emi?.diffPrincipal;
            emi.coverPrincipal = emi.diffPrincipal;
            amount -= emi?.diffPrincipal;
          }

          finalData.principalAmount += principalAmount;
          finalData.interestAmount += interestAmount;
        });
      } else return k422ErrorMessage(kSomthinfWentWrong);
      return { finalData, emiList };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region closeLoanDirectly
  async closeLoanDirectly(prePareAmount, body, loanData) {
    try {
      const userId = loanData?.registeredUsers?.id;
      const loanId = body?.loanId;
      const mode = body?.mode;
      let amount = prePareAmount?.dueAmount;
      body.dueAmount = amount;
      let subSource = body?.subSource ?? 'WEB';
      const source = body?.source ?? kCashfree;
      let subStatus = body.subStatus;
      const creditAmount = prePareAmount?.creditAmount ?? 0;
      if (creditAmount > 0) subStatus = kCreditTransaction;

      if (body.isLoanClosure && prePareAmount.type == 'FULLPAY')
        subStatus = 'LOAN_CLOSURE';
      else if (body.isLoanSettled && prePareAmount.type == 'FULLPAY')
        subStatus = 'LOAN_SETTLEMENT';

      // To Calculate Max DPD for Current Transaction
      let maxDPD = 0;
      const delayedStatus = loanData?.registeredUsers?.loanStatus;
      if (delayedStatus == 2 || delayedStatus == 3) {
        const emiData = await this.emiRepo.getTableWhereData(['penalty_days'], {
          where: { loanId, payment_due_status: '1' },
        });
        if (emiData === k500Error) return kInternalError;
        emiData.forEach((ele) => {
          if (ele.penalty_days > 0 && ele.penalty_days > maxDPD)
            maxDPD = +ele.penalty_days;
        });
      }
      body.p_type = prePareAmount.type;
      const creationData: any = { loanId: body.loanId, userId };
      creationData.paidAmount = amount;
      creationData.roundOff = prePareAmount?.roundOff ?? 0;
      creationData.source = kCredit;
      creationData.status = kInitiated;
      creationData.subSource = kCreditPay;
      creationData.subStatus = kCreditTransactionDirect;
      creationData.principalAmount = prePareAmount?.principalAmount;
      creationData.interestAmount = prePareAmount?.interestAmount;
      creationData.penaltyAmount = prePareAmount?.penaltyAmount;
      creationData.forClosureAmount = prePareAmount?.forClosureAmount ?? 0;
      creationData.sgstForClosureCharge =
        prePareAmount?.sgstForClosureCharge ?? 0;
      creationData.cgstForClosureCharge =
        prePareAmount?.cgstForClosureCharge ?? 0;
      creationData.igstForClosureCharge =
        prePareAmount?.igstForClosureCharge ?? 0;
      creationData.forClosureDays = prePareAmount?.forClosureDays ?? 0;
      creationData.regInterestAmount = prePareAmount?.regInterestAmount;
      creationData.bounceCharge = prePareAmount?.bounceCharge;
      creationData.penalCharge = prePareAmount?.penalCharge;
      creationData.legalCharge = prePareAmount?.legalCharge;
      creationData.cgstOnBounceCharge = prePareAmount?.cgstOnBounceCharge;
      creationData.igstOnBounceCharge = prePareAmount?.igstOnBounceCharge;
      creationData.sgstOnBounceCharge = prePareAmount?.sgstOnBounceCharge;
      creationData.cgstOnPenalCharge = prePareAmount?.cgstOnPenalCharge;
      creationData.igstOnPenalCharge = prePareAmount?.igstOnPenalCharge;
      creationData.sgstOnPenalCharge = prePareAmount?.sgstOnPenalCharge;
      creationData.cgstOnLegalCharge = prePareAmount?.cgstOnLegalCharge;
      creationData.igstOnLegalCharge = prePareAmount?.igstOnLegalCharge;
      creationData.sgstOnLegalCharge = prePareAmount?.sgstOnLegalCharge;
      creationData.accStatus = kCalBySystem;
      creationData.type = prePareAmount.type;
      creationData.adminId = SYSTEM_ADMIN_ID;
      creationData.followerId = loanData?.followerId;
      creationData.remarks = body?.remarks ?? '';
      creationData.maxDPD = maxDPD;
      const approvedAmount = +loanData?.netApprovedAmount;
      const totalFees = loanData?.feesIncome ?? 0;
      const chargePr = ((totalFees ?? 0) * 100) / approvedAmount;
      const principal = prePareAmount?.principalAmount ?? 0;
      const income = Math.round((principal * chargePr) / 100);
      creationData.feesIncome = income;
      creationData.mode = mode;

      let transactionId;
      const randomCode = this.typeService.generateRandomCode(10);
      transactionId = EnvConfig.nbfc.nbfcCodeName + randomCode + '_CN';
      creationData.transactionId = transactionId;
      const isCheck = await this.sharedTransaction.checkTransactionId(
        creationData,
      );
      if (isCheck) return k422ErrorMessage(kTransactionIdExists);
      if (prePareAmount?.settled_type)
        creationData.settled_type = prePareAmount?.settled_type ?? '';
      if (body.emiId != -1) creationData.emiId = body.emiId;

      const result = await this.transactionRepo.createRowData(creationData);
      return { transactionId: result.id, checkPaymentOrder: true };
    } catch (error) {}
  }

  //#region checkCreditPaymentOrder
  async checkCreditPaymentOrder(data) {
    try {
      for (let i = 0; i < data.length; i++) {
        const transData = data[i];
        const transactionId = transData?.transactionId;
        let paymentData: any = {};
        const transactionStatus = kCompleted;
        const globalDate = this.typeService.getGlobalDate(new Date()).toJSON();
        paymentData.id = transData?.id;
        paymentData.status = transactionStatus;
        paymentData.response = null;
        paymentData.utr = 'utr_' + transactionId;
        paymentData.completionDate = globalDate;
        paymentData.paymentTime = this.typeService
          .getIstTime(new Date())
          .toJSON();
        paymentData.type = transData?.type;
        paymentData.loanId = transData?.loanId;
        paymentData.userId = transData?.userId;
        paymentData.subSource = transData?.subSource;
        if (transData?.emiId) paymentData.emiId = transData?.emiId;
        await this.sharedTransaction.markTransactionAsComplete(paymentData);
        let date = this.dateService.readableDate(new Date().toJSON());
        return {
          amount: transData?.paidAmount,
          status: paymentData?.status,
          paymentGateway: null,
          transactionId,
          date,
        };
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async splitTransForCreditPay(paidAmount, loanId, iGst, fullPay?) {
    const result = fullPay
      ? fullPay
      : await this.sharedCalculation.getFullPaymentData({ loanId });
    if (!result || result === k500Error) return kInternalError;
    if (result?.message) return result;

    const fullPIPData = result?.fullPIPData;
    if (!fullPIPData) return [];

    const emiIds = Object.keys(fullPIPData);
    const emiMap = new Map();

    for (const emiId of emiIds) {
      emiMap.set(+emiId, {
        emiId: +emiId,
        roundOff: 0,
        interestAmount: 0,
        principalAmount: 0,
        penaltyAmount: 0,
        regInterestAmount: 0,
        bounceCharge: 0,
        sgstOnBounceCharge: 0,
        cgstOnBounceCharge: 0,
        igstOnBounceCharge: 0,
        penalCharge: 0,
        sgstOnPenalCharge: 0,
        cgstOnPenalCharge: 0,
        igstOnPenalCharge: 0,
        legalCharge: 0,
        sgstOnLegalCharge: 0,
        cgstOnLegalCharge: 0,
        igstOnLegalCharge: 0,
        forClosureAmount: 0,
        cgstForClosureCharge: 0,
        sgstForClosureCharge: 0,
        igstForClosureCharge: 0,
        paidAmount: 0,
      });
    }

    let foreclosurePriority = [
      'forClosureAmount',
      // 'cgstForClosureCharge',
      // 'sgstForClosureCharge',
      // 'igstForClosureCharge',
    ];

    const chargePriority = [
      'principalAmount',
      'interestAmount',
      'regInterestAmount',
      'bounceCharge',
      'penaltyAmount',
      'penalCharge',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
    ];

    const fullPipKeyMap = {
      forClosureAmount: 'forClosureAmount',
      cgstForClosureCharge: 'cgstForClosureCharge',
      sgstForClosureCharge: 'sgstForClosureCharge',
      igstForClosureCharge: 'igstForClosureCharge',
      principalAmount: 'fullPayPrincipal',
      interestAmount: 'fullPayInterest',
      regInterestAmount: 'fullPayRegInterest',
      bounceCharge: 'fullPayBounce',
      penalCharge: 'fullPayPenal',
      penaltyAmount: 'fullPayPenalty',
      legalCharge: 'fullPayLegalCharge',
    };

    // First: Allocate foreclosure charges across all EMIs
    for (const charge of foreclosurePriority) {
      for (const emiId of emiIds) {
        if (paidAmount <= 0) break;
        const pipData = fullPIPData[emiId];
        const transObj = emiMap.get(+emiId);

        const chargeAmount =
          (pipData?.['forClosureAmount'] ?? 0) +
          (pipData?.['sgstForClosureCharge'] ?? 0) +
          (pipData?.['cgstForClosureCharge'] ?? 0) +
          (pipData?.['igstForClosureCharge'] ?? 0);
        const alreadyPaid = transObj[charge] ?? 0;
        const remainingCharge = chargeAmount - alreadyPaid;
        if (remainingCharge <= 0) continue;

        let allocAmount = Math.min(paidAmount, remainingCharge);
        allocAmount = this.typeService.manageAmount(allocAmount);
        const gstFetch = this.fetchGstFromAmount(allocAmount, iGst);

        transObj['forClosureAmount'] = gstFetch.pure ?? 0;
        transObj['cgstForClosureCharge'] = gstFetch.cgst ?? 0;
        transObj['sgstForClosureCharge'] = gstFetch.sgst ?? 0;
        transObj['igstForClosureCharge'] = gstFetch.gst ?? 0;
        transObj.paidAmount += allocAmount;
        paidAmount -= allocAmount;
      }
    }

    // Second: Allocate all other PI/Charges EMI-wise
    for (const emiId of emiIds) {
      const pipData = fullPIPData[emiId];
      const transObj = emiMap.get(+emiId);

      for (const charge of chargePriority) {
        if (paidAmount <= 0) break;

        const pipKey = fullPipKeyMap[charge];
        const chargeAmount = pipData?.[pipKey] ?? 0;
        const alreadyPaid = transObj[charge] ?? 0;
        const remainingCharge = chargeAmount - alreadyPaid;

        if (remainingCharge <= 0) continue;

        let allocAmount = Math.min(paidAmount, remainingCharge);
        allocAmount = this.typeService.manageAmount(allocAmount);

        if (charge === 'legalCharge') {
          const gstFetch = this.fetchGstFromAmount(allocAmount, iGst);
          transObj['legalCharge'] += gstFetch.pure;
          transObj['cgstOnLegalCharge'] += gstFetch.cgst ?? 0;
          transObj['sgstOnLegalCharge'] += gstFetch.sgst ?? 0;
          transObj['igstOnLegalCharge'] += gstFetch.gst ?? 0;
        } else {
          transObj[charge] += allocAmount;
        }

        transObj.paidAmount += allocAmount;
        paidAmount -= allocAmount;
      }
    }

    return Array.from(emiMap.values());
  }

  async checkAndUpdateEMIPartPayCredit(id: number, emiId: number) {
    try {
      const loanInclude = {
        model: loanTransaction,
        attributes: ['penaltyCharges', 'isPartPayment'],
      };
      const emiAttr = [
        'id',
        'emi_amount',
        'penalty',
        'partPaymentPenaltyAmount',
        'settledId',
        'pay_type',
        'principalCovered',
        'interestCalculate',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
        'payment_status',
        'payment_due_status',
        'loanId',
      ];
      const emiOptions: any = { where: { id: emiId } };
      emiOptions.include = loanInclude;
      const emiData = await this.emiService.getRowWhereData(
        emiAttr,
        emiOptions,
      );
      if (!emiData || emiData == k500Error) return false;

      // for old users bounce charge is alreafy included in penalty
      if (!emiData?.loan?.penaltyCharges?.MODIFICATION_CALCULATION)
        emiData.bounceCharge = 0;

      if (emiData?.payment_status == '1') return true;
      const att = [
        'id',
        'paidAmount',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'cgstOnBounceCharge',
        'sgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'cgstOnLegalCharge',
        'sgstOnLegalCharge',
        'igstOnLegalCharge',
        'completionDate',
        'forClosureAmount',
        'cgstForClosureCharge',
        'sgstForClosureCharge',
        'igstForClosureCharge',
      ];
      const option = { where: { emiId, status: kCompleted } };
      const transactionData = await this.transactionRepo.getTableWhereData(
        att,
        option,
      );
      if (!transactionData || transactionData == k500Error) return false;
      let emiPaidAmount = 0;
      let emiPaidPenalty = 0;
      let emiPaidCharges = 0;
      let idPaidPenaltyAmount = 0;
      let payment_done_date;
      transactionData.forEach((element) => {
        try {
          emiPaidAmount += element?.principalAmount ?? 0;
          emiPaidAmount += element?.interestAmount ?? 0;
          emiPaidPenalty += element?.penaltyAmount ?? 0;
          emiPaidCharges += element?.regInterestAmount ?? 0;
          emiPaidCharges += element?.bounceCharge ?? 0;
          emiPaidCharges += element?.sgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.cgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.igstOnBounceCharge ?? 0;
          emiPaidCharges += element?.penalCharge ?? 0;
          emiPaidCharges += element?.sgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.cgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.igstOnPenalCharge ?? 0;
          emiPaidCharges += element?.legalCharge ?? 0;
          emiPaidCharges += element?.cgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.sgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.igstOnLegalCharge ?? 0;
          emiPaidCharges += element?.forClosureAmount ?? 0;
          emiPaidCharges += element?.cgstForClosureCharge ?? 0;
          emiPaidCharges += element?.sgstForClosureCharge ?? 0;
          emiPaidCharges += element?.igstForClosureCharge ?? 0;
          if (element.id === id) {
            idPaidPenaltyAmount = element?.penaltyAmount ?? 0;
            payment_done_date = element.completionDate;
          }
        } catch (error) {}
      });

      let emiAmount =
        (emiData?.principalCovered ?? 0) + (emiData?.interestCalculate ?? 0);
      emiAmount -= emiPaidAmount;
      let emiCharges =
        this.typeService.manageAmount(emiData?.regInterestAmount ?? 0) +
        this.typeService.manageAmount(emiData?.bounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.gstOnBounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.dpdAmount ?? 0) +
        this.typeService.manageAmount(emiData?.penaltyChargesGST ?? 0) +
        this.typeService.manageAmount(emiData?.legalCharge ?? 0) +
        this.typeService.manageAmount(emiData?.legalChargeGST ?? 0);
      emiCharges -= emiPaidCharges;
      const partPenaltyAmount = +(emiData?.partPaymentPenaltyAmount ?? 0);
      let emi_amount = +(emiData.emi_amount ?? 0);
      if (emi_amount > emiAmount) emi_amount = emiAmount;
      let penalty = +(emiData.penalty ?? 0);
      const partPaymentPenaltyAmount = emiPaidPenalty;
      if (
        idPaidPenaltyAmount &&
        !(
          partPenaltyAmount + 10 > emiPaidPenalty &&
          partPenaltyAmount - 10 < emiPaidPenalty
        )
      )
        penalty -= idPaidPenaltyAmount;

      if (emi_amount < 0) emi_amount = 0;
      if (penalty < 0) penalty = 0;
      if (emiCharges < 0) emiCharges = 0;

      const updatedData: any = {};
      if (emi_amount <= 10 && penalty <= 10 && emiCharges <= 10) {
        emi_amount = +(emiData.emi_amount ?? 0);
        updatedData.payment_done_date = payment_done_date;
        updatedData.payment_status = '1';
      }
      updatedData.emi_amount = emi_amount;
      updatedData.penalty = penalty;
      updatedData.partPaymentPenaltyAmount = partPaymentPenaltyAmount;
      if (updatedData.payment_status === '1') updatedData.pay_type = kPartPay;
      const updateResponse = await this.emiService.updateRowData(
        updatedData,
        emiId,
      );
      if (updateResponse == k500Error) return false;
      // Disabling The Part Pay Option (if it's enabled by admin)
      if (
        emiData?.payment_due_status === '1' &&
        updatedData.payment_status === '1' &&
        emiData?.loan?.isPartPayment
      ) {
        const emiList = await this.emiRepo.getTableWhereData(
          ['payment_status', 'payment_due_status'],
          {
            where: { loanId: emiData?.loanId },
          },
        );
        if (emiList == k500Error) return kInternalError;
        let canUpdate = true;
        emiList.forEach((ele) => {
          if (ele?.payment_status == '0' && ele?.payment_due_status == '1')
            canUpdate = false;
        });
        if (canUpdate) {
          const update = await this.loanRepo.updateRowData(
            { isPartPayment: 0, partPayEnabledBy: SYSTEM_ADMIN_ID },
            emiData?.loanId,
          );
          if (update == k500Error) return kInternalError;
        }
      }
    } catch (error) {
      return false;
    }
  }

  async splitTransaction(
    paidAmount,
    loanId,
    iGst = false,
    isForCredit = false,
  ) {
    if (!loanId || isNaN(+loanId)) return kParamMissing('loanId');

    const transInclude: SequelOptions = { model: TransactionEntity };
    transInclude.attributes = [
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'bounceCharge',
      'penalCharge',
      'regInterestAmount',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'sgstOnPenalCharge',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
    ];
    transInclude.required = false;
    transInclude.where = { status: kCompleted };
    const include = [transInclude];

    const emiAttributes = [
      'id',
      'partPaymentPenaltyAmount',
      'principalCovered',
      'interestCalculate',
      'penalty',
      'bounceCharge',
      'gstOnBounceCharge',
      'dpdAmount',
      'penaltyChargesGST',
      'regInterestAmount',
      'legalCharge',
      'legalChargeGST',
    ];
    const emiOptions: any = {
      include,
      where: { loanId, payment_status: '0', payment_due_status: '1' },
    };
    if (isForCredit) emiOptions.where = { loanId, payment_status: '0' };

    const emiList = await this.repoManager.getTableWhereData(
      EmiEntity,
      emiAttributes,
      emiOptions,
    );
    if (!iGst) {
      for (let i = 0; i < emiList.length; i++) {
        try {
          let ele = emiList[i];
          let cGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          let sGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          ele.penaltyChargesGST = cGstOnPenal + sGstOnPenal;
        } catch (error) {}
      }
    }
    const splitResult = this.sharedCalculation.splitPaymentsforPI({
      paidAmount,
      emiList,
      loanId,
      iGst,
    });
    return splitResult;
  }

  //#region
  async sendRefundCreditEmails() {
    try {
      const usersFromFile = JSON.parse(
        fs.readFileSync(CRYPT_PATH.refundCreditData, 'utf8'),
      );

      const userIds = usersFromFile.map((user) => user.UserId);

      // Fetch the user data
      const userData = await this.repoManager.getTableWhereData(
        registeredUsers,
        ['id', 'email', 'totalCredit'],
        { where: { id: userIds } },
      );

      if (!userData || userData === 'k500Error') {
        throw new Error('Error in fetching the user data');
      }

      let length = userData.length;
      if (!gIsPROD) length = 5;

      const templateRelativePath =
        await this.sharedCommonService.getEmailTemplatePath(
          kCreditRefundEmailTemplate,
          1, // only for chinmay
          null,
          null,
        );
      const htmlTemplate = fs.readFileSync(templateRelativePath, 'utf-8');

      const success_UserIds = [];
      const not_Found_Credit = [];

      for (let i = 0; i < length; i++) {
        try {
          const { id, email, totalCredit } = userData[i];

          if (!email || !totalCredit) {
            not_Found_Credit.push(id);
            continue;
          }

          const formattedCredit =
            this.typeService.amountNumberWithCommas(totalCredit);
          const fullNBFCName = EnvConfig.nbfc.nbfcName;

          let html = htmlTemplate;
          html = html.replace(/###totalCredit###/g, formattedCredit);
          html = html.replace(/##NBFC##/g, fullNBFCName);
          html = html.replace(/##NBFCLogo##/g, EnvConfig.url.nbfcLogo);
          html = html.replace(
            /##NBFCNumber##/g,
            EnvConfig.nbfc.nbfcRegistrationNumber,
          );
          html = html.replace(/##Supportmail##/g, EnvConfig.mail.suppportMail);

          const subject = kCreditRefundEmailSubject;
          await this.sharedNotification.sendMailFromSendinBlue(
            email,
            subject,
            html,
            id,
            [],
            [],
            kNoReplyMail,
          );

          success_UserIds.push(id);
        } catch {
          continue;
        }
      }
      return {
        message: 'All refund emails were send successfully',
        sentUserIds: success_UserIds,
        notFoundCredit: not_Found_Credit,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async creditRefundEmails(body) {
    const { refundData } = body;

    if (!Array.isArray(refundData)) {
      throw new Error('Invalid or missing data array');
    }
    const templateRelativePath =
      await this.sharedCommonService.getEmailTemplatePath(
        kRefundEmailTemplate,
        1, // only for chinmay
        null,
        null,
      );

    const htmlTemplate = fs.readFileSync(templateRelativePath, 'utf-8');

    const fullNBFCName = EnvConfig.nbfc.nbfcName;
    const results = [];

    for (const data of refundData) {
      const subject = `Confirmation of Refund Processed-${data.utrNumber}`;
      const amount = this.typeService.amountNumberWithCommas(data.amount);

      let html = htmlTemplate;
      html = html.replace(/##Name##/g, data.fullName);
      html = html.replace(/###amount###/g, amount);
      html = html.replace(/#date#/g, data.date);
      html = html.replace(/##bank##/g, data.bank);
      html = html.replace(/##accountNumber##/g, data.accountNumber);
      html = html.replace(/##utrNumber##/g, data.utrNumber);
      html = html.replace(/##NBFC##/g, fullNBFCName);
      html = html.replace(
        /##NBFCNumber##/g,
        EnvConfig.nbfc.nbfcRegistrationNumber,
      );
      html = html.replace(/##Supportmail##/g, EnvConfig.mail.suppportMail);

      try {
        const sendResult = await this.sharedNotification.sendMailFromSendinBlue(
          data.email,
          subject,
          html,
          data.userId,
          [],
          [],
          kNoReplyMail,
        );

        results.push({
          userId: data.userId,
          Email: data.email,
          status: sendResult?.messageId ? 'Sent' : 'Failed to send',
          detail: sendResult,
        });
      } catch (error) {
        results.push({
          userId: data.userId,
          Email: data.email,
          status: 'Failed',
          error: error.message || error.toString(),
        });
      }
    }

    return results;
  }

  async getLatestCRReport() {
    let data = [];
    const userData = await this.userRepo.getTableWhereData(
      [
        'remainingCredit',
        'totalCredit',
        'id',
        'phone',
        'email',
        'creditData',
        'loanStatus',
      ],
      {
        where: {
          totalCredit: { [Op.gt]: 0 },
        },
      },
    );
    console.log(userData.length);
    const userIds = userData.map((usrData) => usrData.id);

    const userLoanData = await this.loanRepo.getTableWhereData(
      ['id', 'loanStatus', 'userId'],
      {
        where: {
          userId: userIds,
          loanStatus: { [Op.in]: ['Active', 'Complete'] },
        },
      },
    );
    console.log(userLoanData.length);

    for (let i = 0; i < userData.length; i++) {
      const element = userData[i];
      let loanStatus: any = await this.getUserLoanStatus(
        userLoanData.filter((loan) => loan.userId == element.id),
      );

      const transferredCredit = element?.creditData?.amount ?? 0;
      const remainingCredit = element?.remainingCredit ?? 0;
      const totalCredit = element?.totalCredit ?? 0;
      if (i % 50 === 0) console.log(i, `Total -> ${userData.length}`);

      let obj = {
        userId: element.id,
        outStanding: loanStatus?.remainingAmount ?? 0,
        phone: element.phone
          ? this.cryptService.decryptPhone(element.phone)
          : '',
        totalCredit: element.totalCredit,
        remainingCredit: element.remainingCredit,
        currentStatus: loanStatus?.status,

        typeOfRefund:
          transferredCredit &&
          transferredCredit == totalCredit &&
          remainingCredit == 0
            ? 'Refund to Bank'
            : remainingCredit == 0 && !transferredCredit
            ? 'Statement Credit'
            : transferredCredit != totalCredit &&
              transferredCredit > 0 &&
              remainingCredit == 0
            ? 'Both'
            : '',
        RefundStatus:
          remainingCredit > 0 && remainingCredit != totalCredit
            ? 'Partial'
            : remainingCredit == 0
            ? 'Completed'
            : remainingCredit == totalCredit
            ? 'Pending'
            : '',
      };
      data.push(obj);
    }
    const rawExcelData = {
      sheets: ['local-reports'],
      data: [data],
      sheetName: 'refundCreditData.xlsx',
      needFindTuneKey: false,
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    console.log({ url });
    return { url };
  }

  async getUserLoanStatus(loanData) {
    if (!loanData || loanData.length === 0) return 'No Loan';

    // Sort to get the latest loan
    loanData.sort((a, b) => b.id - a.id);
    const latestLoan = loanData[0];

    const loanRemaining = await this.sharedCalculation.getFullPaymentData({
      loanId: latestLoan.id,
    });

    const remainingAmount = loanRemaining?.totalAmount ?? 0;

    if (latestLoan.loanStatus === 'Complete') {
      // Check all loans of this user

      // Fetch all EMIs across all loans
      const emis = await this.emiRepo.getTableWhereData(['penalty_days'], {
        where: { loanId: latestLoan.id },
      });

      const hasAnyDelay = emis.some((emi) => (emi.penalty_days || 0) > 0);

      return {
        status: hasAnyDelay ? 'Inactive-Delayed' : 'Complete',
        remainingAmount,
      };
    }

    if (latestLoan.loanStatus === 'Active') {
      // Fetch EMIs for the latest loan only
      const emis = await this.emiRepo.getTableWhereData(
        ['emi_date', 'penalty_days', 'payment_status'],
        {
          where: { loanId: latestLoan.id },
        },
      );

      if (!emis || emis.length === 0) return 'Active - No EMIs';

      let maxPenalty = 0;
      let hasUpcoming = false;
      const today = new Date();

      for (const emi of emis) {
        const emiDate = new Date(emi.emi_date);
        const penaltyDays = emi.penalty_days || 0;

        if (emiDate > today) {
          hasUpcoming = true;
          continue;
        }

        if (penaltyDays > maxPenalty && emi?.payment_status == '0') {
          maxPenalty = penaltyDays;
        }
      }

      if (hasUpcoming && !maxPenalty)
        return { status: 'Active OnTime', remainingAmount };
      if (maxPenalty > 0)
        return { status: 'Active Defaulter', remainingAmount };
      return { status: 'Active - Ontime', remainingAmount };
    }
  }
}

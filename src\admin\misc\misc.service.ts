// Imports
import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { Op } from 'sequelize';
import { AdminRepository } from 'src/repositories/admin.repository';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import { UserPermissionRepository } from 'src/repositories/userPermission.repository';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';
import { ConfigurationRepository } from 'src/repositories/configurations.repository';
import { RBIGuidelineRepository } from 'src/repositories/rbiGuideline.repository';
import { PAGE_LIMIT } from 'src/constants/globals';
import { CommonSharedService } from 'src/shared/common.shared.service';
import {
  CLOUD_FOLDER_PATH,
  kMiscActionType,
  kMiscSubType,
} from 'src/constants/objects';
import { ErrorContextService } from 'src/utils/error.context.service';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { kMiscType } from 'src/constants/strings';
import { APIService } from 'src/utils/api.service';
import { lspConfigUpdateUrl } from 'src/constants/network';
import { kConfigId } from 'src/constants/numbers';
@Injectable()
export class MiscService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly userPermissionRepo: UserPermissionRepository,
    private readonly adminRedisSyncService: AdminRedisSyncService,
    private readonly configRepo: ConfigurationRepository,
    private readonly rbiGuidelineRepository: RBIGuidelineRepository,
    // Utils
    private readonly fileService: FileService,
    private readonly typeService: TypeService,
    private readonly commonSharedService: CommonSharedService,
    private readonly errorContextService: ErrorContextService,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly apiService: APIService,
  ) {}

  async createUserPermission(reqData) {
    // Params validation
    const updatedBy = reqData.updatedBy;
    if (!updatedBy) return kParamMissing('adminid');
    const title = reqData.title;
    if (!title) return kParamMissing('title');
    const img = reqData.img;
    if (!img) return kParamMissing('img');
    const description = reqData.description;
    if (!description) return kParamMissing('Description');
    const android = reqData.android;
    if (android == null || android == undefined)
      return kParamMissing('Android');
    const IOS = reqData.IOS;
    if (IOS == null || IOS == undefined) return kParamMissing('IOSmayAndroid');
    const asset = reqData.asset || 'assetPhoneInfov2';
    reqData.asset = asset;

    const options = { where: { title: reqData.title } };
    const checkTitle = await this.userPermissionRepo.getRowWhereData(
      ['id'],
      options,
    );

    if (checkTitle == k500Error) return kInternalError;
    if (checkTitle)
      return {
        valid: false,
        message: 'Already Exist!',
      };

    const result = await this.userPermissionRepo.createRowData(reqData);
    if (result == k500Error) return kInternalError;

    await this.adminRedisSyncService.storePermissionsInRedis();
    return result;
  }

  async editUserPermission(reqData) {
    const updatedBy = reqData.updatedBy;
    if (!updatedBy) return kParamMissing('adminid');
    const id = reqData.id;
    if (!id) return kParamMissing('id');
    const title = reqData.title;
    if (!title) return kParamMissing('title');
    const img = reqData.img;
    if (!img) return kParamMissing('img');
    const asset = reqData.asset || 'assetPhoneInfov2';
    const description = reqData.description;
    if (!description) return kParamMissing('Description');
    const android = reqData.android;
    if (android == null || android == undefined)
      return kParamMissing('Android');
    const IOS = reqData.IOS;
    if (IOS == null || IOS == undefined) return kParamMissing('IOSmayAndroid');

    const updatedData = {
      title,
      img,
      asset,
      description,
      android,
      IOS,
      updatedBy,
    };
    const result = await this.userPermissionRepo.updateRowData(updatedData, id);
    if (result == k500Error) return kInternalError;
    await this.adminRedisSyncService.storePermissionsInRedis();
    return result;
  }

  async deleteUserPermission(body) {
    const id = body?.id;
    try {
      // Getting User Permission's Data Which is Going to Delete, But It Will Be Stored in changeLogsRepo First
      const userPermission = await this.userPermissionRepo.getRowWhereData(
        [
          'id',
          'title',
          'img',
          'asset',
          'description',
          'android',
          'IOS',
          'updatedBy',
        ],
        { where: { id } },
      );
      if (userPermission == k500Error)
        throw new Error('Error While Getting User Permissions Data');

      // Storing Data to Changes Log Entity
      const storeLogs = await this.changeLogsRepo.create({
        type: kMiscType,
        subType: kMiscSubType?.permissionList,
        oldData: JSON.stringify(userPermission),
        newData: '',
        adminId: body?.adminId,
        status: kMiscActionType?.delete,
        ip: body?.ip,
      });
      if (storeLogs == k500Error)
        throw new Error('Error While Adding Data to Change Log Entity');

      // Deleting Data
      const result = await this.userPermissionRepo.deleteSingleData(id);
      if (result == k500Error)
        throw new Error('Error While Deleting User Permission Row');

      await this.adminRedisSyncService.storePermissionsInRedis();
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async uploadDatabaseBackup(reqData): Promise<any> {
    const file = reqData.file;
    if (!file) return kParamMissing('file');
    const extension = reqData.extension;
    const fileName = file?.filename;
    const directory = reqData.directory ?? CLOUD_FOLDER_PATH.misc;

    const url = await this.fileService.uploadFile(
      fileName,
      directory,
      extension,
      reqData?.bucketName,
    );
    return { url };
  }

  async uploadFile(reqData): Promise<any> {
    const file = reqData.file;
    if (!file) return kParamMissing('file');
    const extension = reqData.extension;
    const fileName = file?.filename;
    const directory = reqData.directory ?? CLOUD_FOLDER_PATH.misc;

    const url = await this.fileService.uploadFile(
      fileName,
      directory,
      extension,
    );
    return { url };
  }

  async uploadFilePublicBucket(reqData): Promise<any> {
    const file = reqData.file;
    if (!file) return kParamMissing('file');
    const extension = reqData.extension;
    const fileName = file?.filename;
    const directory =
      reqData.directory ?? CLOUD_FOLDER_PATH.Backend_static_stuff;

    const url = await this.fileService.uploadFilePublicBucket(
      fileName,
      directory,
      extension,
    );
    return { url };
  }

  async getConfig() {
    try {
      const attributes = [
        'updatedBy',
        'iosAppVersion',
        'nbfcIosAppVersion',
        'iosAppVersionCode',
        'androidAppVersion',
        'isIosAppForcefully',
        'androidAppVersionCode',
        'nbfcAndroidAppVersion',
        'nbfcIosAppVersionCode',
        'isNbfcIosAppForcefully',
        'isAndroidAppForcefully',
        'nbfcAndroidAppVersionCode',
        'isNbfcAndroidAppForcefully',
      ];
      const options = { where: { id: kConfigId } };
      let configData: any = await this.configRepo.getRowWhereData(
        attributes,
        options,
      );
      if (configData == k500Error) return kInternalError;

      configData.updatedBy = (
        await this.commonSharedService.getAdminData(configData?.updatedBy)
      )?.fullName;

      const configs = {
        nbfcAndroidAppVersion: configData?.nbfcAndroidAppVersion,
        nbfcAndroidAppForcefully: configData?.isNbfcAndroidAppForcefully,
        nbfcAndroidAppVersionCode: configData?.nbfcAndroidAppVersionCode,
        nbfcIosAppVersion: configData?.nbfcIosAppVersion,
        nbfcIosAppForcefully: configData?.isNbfcIosAppForcefully,
        nbfcIosAppVersionCode: configData?.nbfcIosAppVersionCode,
        androidAppVersion: configData?.androidAppVersion,
        androidAppForcefully: configData?.isAndroidAppForcefully,
        androidAppVersionCode: configData?.androidAppVersionCode,
        iosAppVersion: configData?.iosAppVersion,
        iosAppForcefully: configData?.isIosAppForcefully,
        iosAppVersionCode: configData?.iosAppVersionCode,
        updatedBy: configData?.updatedBy,
      };
      return configs;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateConfig(reqData) {
    try {
      const {
        updatedBy,
        iosAppVersion,
        androidAppVersion,
        nbfcIosAppVersion,
        iosAppVersionCode,
        isIosAppForcefully,
        androidAppVersionCode,
        nbfcAndroidAppVersion,
        nbfcIosAppVersionCode,
        isAndroidAppForcefully,
        isNbfcIosAppForcefully,
        nbfcAndroidAppVersionCode,
        isNbfcAndroidAppForcefully,
      } = reqData;
      if (
        !updatedBy ||
        !iosAppVersion ||
        !nbfcIosAppVersion ||
        !iosAppVersionCode ||
        !androidAppVersion ||
        !isIosAppForcefully ||
        !nbfcAndroidAppVersion ||
        !androidAppVersionCode ||
        !nbfcIosAppVersionCode ||
        !isAndroidAppForcefully ||
        !isNbfcIosAppForcefully ||
        !nbfcAndroidAppVersionCode ||
        !isNbfcAndroidAppForcefully
      )
        return kParamMissing();

      // Due the DataType in DB convert versionCode and updatedBy to number....
      const data = {
        iosAppVersion,
        androidAppVersion,
        nbfcIosAppVersion,
        updatedBy: +updatedBy,
        nbfcAndroidAppVersion,
        iosAppVersionCode: +iosAppVersionCode,
        androidAppVersionCode: +androidAppVersionCode,
        nbfcIosAppVersionCode: +nbfcIosAppVersionCode,
        isIosAppForcefully: isIosAppForcefully === 'true',
        nbfcAndroidAppVersionCode: +nbfcAndroidAppVersionCode,
        isNbfcIosAppForcefully: isNbfcIosAppForcefully === 'true',
        isAndroidAppForcefully: isAndroidAppForcefully === 'true',
        isNbfcAndroidAppForcefully: isNbfcAndroidAppForcefully === 'true',
      };

      const configUpdate = await this.configRepo.updateRowData(data, kConfigId);
      if (configUpdate == k500Error) return kInternalError;
      const lspConfigUpdate = await this.apiService.post(lspConfigUpdateUrl, {
        updatedBy,
        iosAppVersion,
        iosAppVersionCode,
        androidAppVersion,
        isIosAppForcefully,
        androidAppVersionCode,
        isAndroidAppForcefully,
      });
      if (lspConfigUpdate == k500Error) return kInternalError;

      await this.adminRedisSyncService.storeConfigData();
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async addRBIGuidelines(reqData) {
    const { file, body } = reqData;
    const { title, keyPoints, adminId, departmentIds, rbiCircularNo } = body;
    if (!file) return kParamMissing('file');
    if (!title) return kParamMissing('title');
    if (!keyPoints) return kParamMissing('keyPoints');
    if (!adminId) return kParamMissing('adminId');
    if (!departmentIds) return kParamMissing('departmentIds');

    //convert string into array
    const departmentIdsArr = JSON.parse(departmentIds);

    if (!rbiCircularNo) return kParamMissing('rbiCircularNo');

    const mimetype = file?.mimetype ?? '-';
    const extension = mimetype.split('/')[1];
    if (extension !== 'pdf') return kParamMissing('Invalid file!');

    const buffer = file?.buffer ?? '-';
    const originalname = file?.originalname.split('.')[0];

    const url = await this.fileService?.binaryToFileURL(
      buffer,
      extension,
      CLOUD_FOLDER_PATH.misc,
      originalname,
    );

    const imageURL = url;
    const createData: any = {
      title,
      keyPoints: keyPoints,
      docUrl: imageURL,
      adminId,
      departmentIds: departmentIdsArr,
      rbiCircularNo,
    };

    const data = await this.rbiGuidelineRepository.createRowData(createData);
    if (data == k500Error) throw new Error();

    let where = {
      isActive: { [Op.eq]: '1' },
      departmentId: { [Op.in]: [...departmentIdsArr] },
    };

    const updateData = await this.adminRepo.update(
      { latestRBIGuideline: true },
      where,
    );
    if (updateData == k500Error) throw new Error();

    return true;
  }

  async getAllRBIGuidelines(reqData) {
    const page = reqData?.page || 1;
    const adminId = reqData?.adminId;
    const departmentId = reqData?.departmentId;
    const startDate = reqData?.startDate;
    const endDate = reqData?.endDate;

    if (!adminId) return kParamMissing('adminId');
    if (!startDate) return kParamMissing('startDate');
    if (!endDate) return kParamMissing('endDate');

    const dateRange = await this.typeService.getUTCDateRange(
      startDate ?? new Date().toString(),
      endDate ?? new Date().toString(),
    );

    const pageLimit = PAGE_LIMIT;
    const offsetLimit = (page - 1) * PAGE_LIMIT;

    const attributes = [
      'id',
      'adminId',
      'title',
      'keyPoints',
      'createdAt',
      'docUrl',
      'rbiCircularNo',
      'departmentIds',
    ];

    const departmentWhere = departmentId
      ? {
          departmentIds: {
            [Op.contains]: [departmentId],
          },
        }
      : {};

    const options: any = {
      where: {
        createdAt: {
          [Op.gte]: dateRange.fromDate,
          [Op.lte]: dateRange.endDate,
        },
        ...departmentWhere,
      },
      order: [['id', 'DESC']],
      limit: pageLimit,
      offset: offsetLimit,
    };

    const response =
      await this.rbiGuidelineRepository.getTableWhereDataWithCounts(
        attributes,
        options,
      );

    if (response == k500Error) throw new Error();
    const data = response?.rows || [];

    const formattedData = await Promise.all(
      data.map(async (value) => {
        const name = await this.commonSharedService.getAdminData(
          value?.adminId,
        );
        return {
          Id: value?.id || '-',
          Title: value?.title || '-',
          'Key Points': value?.keyPoints || '-',
          'Updated Date': value?.createdAt || '-',
          'Updated By': name?.fullName || '-',
          docUrl: value?.docUrl || '-',
          'RBI Circular No': value?.rbiCircularNo || '-',
          departmentIds: value?.departmentIds,
        };
      }),
    );
    return { count: response.count, rows: formattedData };
  }

  async getLatestRBIGuideline(reqData) {
    const adminId = reqData?.adminId;
    const departmentId = reqData?.departmentId;

    if (!adminId) return kParamMissing('adminId');
    if (!departmentId) return kParamMissing('departmentId');

    const attributes = [
      'id',
      'adminId',
      'title',
      'keyPoints',
      'createdAt',
      'docUrl',
      'rbiCircularNo',
      'departmentIds',
    ];

    const options: any = {
      where: {
        departmentIds: {
          [Op.contains]: [departmentId],
        },
      },
      order: [['id', 'DESC']],
      limit: 1,
    };

    const response =
      await this.rbiGuidelineRepository.getTableWhereDataWithCounts(
        attributes,
        options,
      );
    if (response == k500Error) throw new Error();
    const data = response?.rows || [];

    const formattedData = await Promise.all(
      data.map(async (value) => {
        const name = await this.commonSharedService.getAdminData(
          value?.adminId,
        );
        return {
          Id: value?.id || '-',
          Title: value?.title || '-',
          'Key Points': value?.keyPoints || '-',
          'Updated Date': value?.createdAt || '-',
          'Updated By': name?.fullName || '-',
          docUrl: value?.docUrl || '-',
          'RBI Circular No': value?.rbiCircularNo || '-',
          departmentIds: value?.departmentIds,
        };
      }),
    );
    let updateValue = {
      latestRBIGuideline: false,
    };
    const updateData = await this.adminRepo.updateRowData(updateValue, adminId);
    if (updateData == k500Error) throw new Error();
    return formattedData[0];
  }
}

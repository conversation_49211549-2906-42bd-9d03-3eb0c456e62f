import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class ComplianceReminderEntity extends Model<ComplianceReminderEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: -1,
    comment: `
    -1: Task not uploaded or task gets rejected,
    0: Task is uploaded,
    1: Task is accepted,
    2: Task is expired, due to reminder date changes.
    `,
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
  })
  remind_admin_before_days: number;

  @Column({
    type: DataType.SMALLINT,
  })
  remind_director_before_days: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment:
      '1: Upcoming reminder, 2: Overdue reminder, 3: Both (Upcoming and overdue reminder)',
  })
  remind_for: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  sub_category_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  department_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  checkbox_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  reminder_date: Date;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  maker: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  checker: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  sub_category_name: string;
}

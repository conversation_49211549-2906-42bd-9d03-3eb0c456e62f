// Imports
import { Injectable } from '@nestjs/common';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import * as fs from 'fs';
import { CrmStatusRepository } from 'src/repositories/crmStatus.repository';
import { CrmDispositionRepository } from 'src/repositories/crmDisposition.repository';
import { CrmTitleRepository } from 'src/repositories/crmTitle.repository';
import { k500Error } from 'src/constants/misc';
import { DepartmentRepository } from 'src/repositories/department.repository';
import { AdminRepository } from 'src/repositories/admin.repository';
import { Department } from 'src/entities/department.entity';
import { Op, Sequelize } from 'sequelize';
import { CrmRepository } from 'src/repositories/crm.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { UserRepository } from 'src/repositories/user.repository';
import {
  COLLECTION,
  crmTypeTitle,
  SUPPORT,
  TypeService,
} from 'src/utils/type.service';
import { EmiEntity } from 'src/entities/emi.entity';
import { CrmReasonRepository } from 'src/repositories/Crm.reasons.repository';
import { admin } from 'src/entities/admin.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { CryptService } from 'src/utils/crypt.service';
import {
  COLLECTION_DEPARTMENT_ID,
  COLLECTION_PERFORMANCE_CYCLE_DATE,
  isUAT,
  LEAD_DEPARTMENT_ID,
  PAGE_LIMIT,
  templateDesign,
} from 'src/constants/globals';
import { CrmReasonEntity } from 'src/entities/crm.reasons.entity';
import { DefaulterOnlineRepository } from 'src/repositories/defaulterOnline.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { MasterRepository } from 'src/repositories/master.repository';
import {
  StrAdminAccess,
  StrDefault,
  kNoDataFound,
  nbfcInfoStr,
} from 'src/constants/strings';
import { EMIRepository } from 'src/repositories/emi.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { DateService } from 'src/utils/date.service';
import { kLspMsg91Templates, kMsg91Templates } from 'src/constants/objects';
import { nPaymentRedirect } from 'src/constants/network';
import { EnvConfig } from 'src/configs/env.config';
import { kEmailPaymentReminderCRM } from 'src/constants/directories';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ErrorContextService } from 'src/utils/error.context.service';
import { FileService } from 'src/utils/file.service';
import { DefaulterService } from '../defaulter/defaulter.service';
import { AgentCallHistoryEntity } from 'src/entities/agentCallHistory.entity';
@Injectable()
export class CRMService {
  constructor(
    private readonly crmStatusRepo: CrmStatusRepository,
    private readonly crmTitleRepo: CrmTitleRepository,
    private readonly dateService: DateService,
    private readonly dispositionRepo: CrmDispositionRepository,
    private readonly departmentRepo: DepartmentRepository,
    private readonly emiRepo: EMIRepository,
    private readonly adminRepo: AdminRepository,
    private readonly crmRepo: CrmRepository,
    private readonly loanRepository: LoanRepository,
    private readonly userRepository: UserRepository,
    private readonly typeService: TypeService,
    private readonly crmReasonsRepo: CrmReasonRepository,
    private readonly cryptService: CryptService,
    private readonly defaulterOnlineRepo: DefaulterOnlineRepository,
    private readonly commonShared: CommonSharedService,
    private readonly masterRepo: MasterRepository,
    private readonly sharedNotification: SharedNotificationService,
    private readonly redisService: RedisService,
    private readonly fileService: FileService,
    private readonly repoManager: RepositoryManager,
    private readonly errorContextService: ErrorContextService,
    private readonly defaulterService: DefaulterService,
  ) {}

  async migrateCrmTitles() {
    try {
      const jsonData = fs.readFileSync('./upload/crmParameter.json', 'utf-8');
      if (!jsonData) return kInternalError;
      const parsedData = JSON.parse(jsonData);
      for (const key in parsedData) {
        try {
          const mainData = parsedData[key];
          for (const subKey in mainData) {
            try {
              const subDisposition = mainData[subKey];
              const disposition = subDisposition['disposition'];
              const departmentIds = subDisposition['departmentIds'];
              let departmentData = await this.getDepartmentIds(departmentIds);
              let sourceData = await this.crmStatusRepo.getRowWhereData(
                ['id'],
                {
                  where: { status: subKey },
                },
              );
              if (!sourceData || sourceData == k500Error) {
                sourceData = await this.crmStatusRepo.createRowData({
                  status: subKey,
                  departmentIds: departmentData,
                });
                if (sourceData == k500Error) continue;
              } else {
                await this.crmStatusRepo.updateRowData(
                  { departmentIds: departmentData },
                  sourceData.id,
                );
              }
              for (let i = 0; i < disposition.length; i++) {
                try {
                  const dis = disposition[i];
                  const disDeparment = dis['departmentIds'];
                  let departmentData = await this.getDepartmentIds(
                    disDeparment,
                  );

                  delete dis['departmentIds'];
                  for (const dKey in dis) {
                    try {
                      let disId;
                      const checkDisData =
                        await this.dispositionRepo.getRowWhereData(['id'], {
                          where: { title: dKey },
                        });
                      if (!checkDisData) {
                        const createdData: any =
                          await this.dispositionRepo.createRowData({
                            title: dKey,
                            statusId: sourceData.id,
                            departmentIds: departmentData,
                          });
                        if (createdData) disId = createdData.id;
                      } else {
                        await this.dispositionRepo.updateRowData(
                          { departmentIds: departmentData },
                          checkDisData.id,
                        );
                        disId = checkDisData.id;
                      }
                      let titleData = dis[dKey];
                      for (let j = 0; j < titleData.length; j++) {
                        try {
                          let each = titleData[j];
                          const exitingData: any =
                            await this.crmTitleRepo.getRowWhereData(
                              ['id', 'title'],
                              {
                                where: { title: each.title },
                              },
                            );
                          departmentData = await this.getDepartmentIds(
                            each.departmentIds,
                          );
                          each.departmentIds = departmentData;
                          if (!exitingData) {
                            titleData = {
                              ...each,
                              crmDispositionId: disId,
                            };
                            await this.crmTitleRepo.createRowData(titleData);
                          } else {
                            await this.crmTitleRepo.updateRowData(
                              {
                                ...each,
                                crmDispositionId: disId,
                              },
                              exitingData.id,
                            );
                          }
                        } catch (error) {}
                      }
                    } catch (error) {}
                  }
                } catch (error) {}
              }
            } catch (error) {}
          }
        } catch (error) {}
      }

      return parsedData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getTodayCrmData(query) {
    try {
      const adminid = query?.adminid;
      const maskOptions = await this.commonShared.findMaskRole(adminid);
      const adminId: any = query.adminId ?? '-1';
      const page: number = query.page ?? 1;
      const start_Date = query?.start_date;
      const end_Date = query?.end_date;
      const download = query?.download ?? false;
      const searchText: string = (query?.searchText ?? '').trim().toLowerCase();
      if (!start_Date && !end_Date) return kParamsMissing;

      let { fromDate, endDate }: any = this.typeService.getUTCDateRange(
        start_Date,
        end_Date,
      );
      const passData = {
        fromDate,
        endDate,
        searchText,
        adminId,
        page,
        download,
      };
      const crmOptions: any = this.prepareCrmOptions(passData);
      if (crmOptions.message) return kInternalError;
      const attributes = [
        'id',
        'userId',
        'categoryId',
        'due_date',
        'adminId',
        'read',
        'closingDate',
        'remark',
        'createdAt',
        'settlementData',
        'amount',
        'reason',
        'referenceName',
        'relationData',
        'loanId',
      ];

      const crmData: any = await this.crmRepo.getTableWhereCountData(
        attributes,
        crmOptions,
      );
      if (crmData == k500Error)
        return { valid: false, message: 'crm data not found' };
      const finalData = [];
      for (let i = 0; i < crmData.rows.length; i++) {
        try {
          const element = crmData.rows[i];
          const decryptedPhone = this.cryptService.decryptPhone(
            element.registeredUsers.phone,
          );
          element.registeredUsers.phone = maskOptions?.isMaskPhone
            ? this.cryptService.dataMasking('phone', decryptedPhone)
            : decryptedPhone;
          if (element.settlementData)
            element.settlementData = JSON.parse(element.settlementData);
          if (!element.descriptionData)
            element.descriptionData = element.description ?? element.remark;
          const relationData = element.relationData;
          delete element.relationData;
          let pinCrm = 0;
          if (element?.registeredUsers?.pinCrm?.id == element?.id) pinCrm = 1;
          delete element.registeredUsers.pinCrm;
          finalData.push({ ...element, ...relationData, pinCrm });
        } catch (error) {}
      }

      if (download == 'true') {
        const formatedData = this.getFormatedDataForTodayCrm(finalData);
        const rawExcelData = {
          sheets: ['Crm Activity'],
          data: [formatedData],
          sheetName: 'crm activity.xlsx',
        };
        const url = await this.fileService.objectToExcelURL(rawExcelData);
        if (url.message) return url;
        return { fileUrl: url };
      }

      return { count: crmData.count, rows: finalData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private prepareCrmOptions(passData) {
    try {
      let loanId;
      let userWhere;
      let search = (passData?.searchText ?? '').toLowerCase();
      let where: any = {};

      if (search) {
        if (search.startsWith('l-')) {
          loanId = search.replace('l-', '');
        } else {
          if (!isNaN(search) && !passData.searchedByUserPage) {
            if (search.startsWith('+91')) search = search.replace('+91', '');
            search = this.cryptService.encryptPhone(search);
            if (search === k500Error) return k500Error;
            search = search.split('===')[1];

            userWhere = {
              phone: {
                [Op.like]: `%${search}%`,
              },
            };
          } else if (!passData.searchedByUserPage) {
            userWhere = {
              fullName: {
                [Op.iLike]: `%${search}%`,
              },
            };
          } else if (passData.searchedByUserPage) {
            where[Op.or] = [
              {
                relationData: {
                  dispositionName: { [Op.iLike]: `%${search}%` },
                },
              },
              { remark: { [Op.iLike]: `%${search}%` } },
            ];
          }
        }
      }

      const userInlude = {
        model: registeredUsers,
        attributes: ['fullName', 'phone', 'pinCrm'],
        where: userWhere,
      };
      //not remove beacuse admin is include inside the the include
      const adminInclude = {
        model: admin,
        as: 'adminData',
        attributes: ['id', 'fullName'],
        include: [{ model: Department, attributes: ['id', 'department'] }],
      };
      const closeByInclude = {
        model: admin,
        as: 'closedByData',
        attributes: ['id', 'fullName'],
      };

      const loanInclude = {
        model: loanTransaction,
        attributes: ['id'],
      };
      let condition = {};
      if (passData?.adminId && passData.adminId != '-1')
        condition['adminId'] = passData.adminId;
      if (passData.categoryId) condition['categoryId'] = passData.categoryId;
      if (passData.userId) condition['userId'] = passData.userId;
      if (loanId) condition['loanId'] = loanId;
      if (passData.fromDate && passData.endDate) {
        where.createdAt = {
          [Op.gte]: passData.fromDate,
          [Op.lte]: passData.endDate,
        };
      }

      const crmOptions: any = {
        where: {
          ...where,
          isDelete: '0',
          ...condition,
        },

        order: [['createdAt', 'DESC']],
        include: [userInlude, adminInclude, loanInclude, closeByInclude],
      };
      if (passData.page && passData?.download != 'true') {
        const offset = passData.page * PAGE_LIMIT - PAGE_LIMIT;
        const limit = PAGE_LIMIT;
        crmOptions.limit = limit;
        crmOptions.offset = offset;
      }

      return crmOptions;
    } catch (error) {
      console.log('funGetUserCrmActivity-prepareCrmOptions', error);
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private getFormatedDataForTodayCrm(crmData) {
    const formatedData = [];
    crmData.forEach((ele) => {
      const tempObj = {};
      tempObj['Loan Id'] = ele?.loanId ?? '-';
      tempObj['Name'] = ele?.registeredUsers?.fullName ?? '-';
      tempObj['Mobile Number'] = ele?.registeredUsers?.phone ?? '-';
      tempObj['Source'] = ele?.statusName ?? '-';
      const titleName = ele?.titleName ? ` (${ele?.titleName})` : '';
      const disposition = ele?.dispositionName
        ? ele?.dispositionName + titleName
        : '-';
      tempObj['Disposition'] = disposition;
      tempObj['Amount'] = ele?.amount ?? '0';
      tempObj['Category'] =
        ele?.categoryId == '0'
          ? 'Pre-calling'
          : ele?.categoryId == '1'
          ? 'Post-calling'
          : ele?.categoryId == '2'
          ? 'On Due'
          : '-';
      tempObj['Description'] = ele?.descriptionData ?? '-';
      const dueDate = ele?.due_date;
      tempObj['Due Date'] = dueDate
        ? this.typeService.getDateFormated(dueDate)
        : '-';
      tempObj['Department'] = ele?.adminData?.departmentData?.department ?? '-';
      tempObj['Action By'] = ele?.adminData?.fullName ?? '-';
      const createdAt = ele?.createdAt;
      tempObj['Created At'] = createdAt
        ? this.typeService.dateTimeFormate(createdAt)
        : '-';
      formatedData.push(tempObj);
    });
    return formatedData;
  }

  async funGetUserCrmActivity(query) {
    try {
      const page = query?.page ?? '1';
      const startDate = query?.start_date;
      const endDate = query?.end_date;
      const adminId = query?.adminId;
      const hAdmin = query?.hAdmin;
      const userId = query?.userId;
      const categoryId = query?.categoryId;
      const searchText = query?.searchText;

      let fromDate = null;
      let toDate = null;

      if (startDate && endDate) {
        const range = this.typeService.getUTCDateRange(startDate, endDate);
        fromDate = range.fromDate;
        toDate = range.endDate;
      }
      const admin = await this.commonShared.getAdminData(hAdmin);
      const departmentId = admin.departmentId;
      let canCreateCrm = true;
      if (departmentId === LEAD_DEPARTMENT_ID) {
        const latestLoan = await this.loanRepository.getRowWhereData(
          ['id', 'userId', 'loan_disbursement'],
          { where: { userId }, order: [['id', 'DESC']] },
        );
        if (latestLoan === k500Error) {
          console.log('funGetUserCrmActivity-latestLoan', query);
          return kInternalError;
        }
        const loanId = latestLoan?.id;
        if (loanId) {
          const redisKey = `CRM_WAIT_LOAN_${loanId}`;
          const isBlocked = await this.redisService.get(redisKey);
          if (isBlocked || latestLoan?.loan_disbursement === '1')
            canCreateCrm = false;
        }
      }
      const attributes = [
        'userId',
        'categoryId',
        'due_date',
        'adminId',
        'read',
        'id',
        'closingDate',
        'callSid',
        'createdAt',
        'remark',
        'settlementData',
        'amount',
        'reason',
        'referenceName',
        'relationData',
      ];

      const passData = {
        categoryId,
        adminId,
        userId,
        page,
        download: query?.download,
        searchText,
        fromDate,
        endDate: toDate,
        searchedByUserPage: true,
      };

      const crmOptions = await this.prepareCrmOptions(passData);
      if (crmOptions.message) return crmOptions;

      let crmData: any = await this.crmRepo.getTableWhereCountData(
        attributes,
        crmOptions,
      );
      if (crmData == k500Error)
        return { valid: false, message: 'CRM not found' };
      let count = crmData?.count;
      crmData = crmData?.rows;
      if (!crmData?.length)
        return { valid: true, message: 'CRM not found', canCreateCrm };

      ////get crm recordings for agent app
      const agentRecordings: any = await this.getRecording(crmData);

      for (let i = 0; i < crmData.length; i++) {
        const ele = crmData[i];
        ele.recordingURL = null;
        ele.recordingSource = null;

        ///if exotel data found
        if (ele?.relationData?.exotel?.recordingUrl) {
          ele.recordingURL = ele?.relationData?.exotel?.recordingUrl;
          ele.recordingSource = 'Exotel/Tata Tele';
        }

        if (ele?.relationData?.callai_rec) {
          ele.recordingURL = ele?.relationData?.callai_rec;
          ele.recordingSource = 'Call AI';
        }
        ///if agent connect recording found
        else if (
          !ele?.relationData?.exotel?.recordingUrl &&
          agentRecordings?.length &&
          !isNaN(+ele?.callSid) &&
          Number.isInteger(+ele?.callSid)
        ) {
          const recording = agentRecordings?.find(
            (data) => data?.id == ele?.callSid,
          );
          if (recording?.recordingURL) {
            ele.recordingURL = await this.cryptService.decryptText(
              recording?.recordingURL,
            );
            ele.recordingSource = 'Agent Connect';
          }
        }

        if (ele.settlementData)
          ele.settlementData = JSON.parse(ele.settlementData);
        const relationData = ele.relationData;
        crmData[i] = { ...relationData, ...ele };
        delete crmData[i]?.relationData;
        delete crmData[i]?.exotel;
      }

      return {
        count,
        rows: crmData,
        canCreateCrm,
      };
    } catch (error) {
      console.log('funGetUserCrmActivity-catch', query, error);
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get agent connect recoding
  private async getRecording(crmData) {
    const callIds = [];
    ///collect
    for (const item of crmData) {
      const relationData = item?.relationData;
      if (
        item?.callSid &&
        !isNaN(+item?.callSid) &&
        Number.isInteger(+item?.callSid) &&
        !relationData?.exotel?.recordingUrl
      ) {
        callIds.push(item.callSid);
      }
    }
    if (!callIds?.length) return;

    return this.repoManager.getTableWhereData(
      AgentCallHistoryEntity,
      ['id', 'recordingURL'],
      { where: { id: callIds } },
    );
  }
  //#endregion

  private async crmFormateByDay(data: any[]) {
    try {
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element?.closedByData?.departmentId) {
          const tempData = await this.departmentRepo.getRowWhereData(
            ['department'],
            {
              where: { id: element.closedByData.departmentId },
            },
          );
          if (tempData != k500Error)
            data[index]['department'] = tempData.department;
        }
      }

      data = data.map((obj) => ({
        ...obj,
        onlyDate: obj.createdAt.toJSON().substring(0, 10),
      }));
      return this.groupByKey(data, 'onlyDate');
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  groupByKey(array, key) {
    const data = array.reduce(function (r, a) {
      r[a[key]] = r[a[key]] || [];
      r[a[key]].push(a);
      return r;
    }, Object.create(null));
    return { ...data };
  }

  async getDispositionData(query) {
    try {
      if (!query.statusId || !query.departmentId) return kParamsMissing;
      const deparmentIds = +query.departmentId;
      const attributes = ['id', 'title', 'statusId', 'departmentIds'];
      const options = { order: [['id']] };
      const key = `${deparmentIds}_DISPOSITION_DATA`;
      let finalResponse = [];
      let finalData = await this.redisService.getKeyDetails(key);
      if (!finalData) {
        finalData = await this.dispositionRepo.getTableWhereData(
          attributes,
          options,
        );
        if (finalData == k500Error) return kInternalError;
        await this.redisService.set(
          key,
          JSON.stringify(finalData),
          NUMBERS.SEVEN_DAYS_IN_SECONDS,
        );
      } else finalData = JSON.parse(finalData);

      finalData.forEach((data) => {
        if (
          data.statusId == query.statusId &&
          data.departmentIds.includes(deparmentIds)
        ) {
          finalResponse.push({ id: data.id, title: data.title });
        }
      });
      return finalResponse;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getDepartmentIds(deparmentData) {
    try {
      let departmentData: any = await this.departmentRepo.getTableWhereData(
        ['id'],
        { where: { department: deparmentData } },
      );
      if (departmentData == k500Error) departmentData = [];
      return departmentData.map((ele) => ele.id);
    } catch (error) {
      return [];
    }
  }

  async checkCrmDataAsStatus(data) {
    try {
      const options = {
        where: { id: data.adminId },
        include: [
          { model: Department, attributes: ['id', 'department', 'loanStatus'] },
        ],
      };
      const adminData = await this.adminRepo.getRoweData(['id'], options);
      if (adminData == k500Error || !adminData)
        return k422ErrorMessage('admin not found!');
      const loanStatus = adminData.departmentData.loanStatus;
      const queryString = `SELECT "id", "loanStatus", "appType","fullName" FROM "loanTransactions" AS "loanTransaction" WHERE "loanTransaction"."userId" = '${data.userId}' ORDER BY "loanTransaction"."id" DESC LIMIT 1`;
      let loanData: any = await this.repoManager.injectRawQuery(
        loanTransaction,
        queryString,
        { useMaster: false },
      );
      if (loanData == k500Error) return kInternalError;
      if (loanData[0]) {
        data.appType = loanData[0].appType;
        data.fullName = loanData[0].fullName;
        if (loanStatus.includes(loanData[0].loanStatus))
          data.loanId = loanData[0].id;
        data.loanStatus = loanData[0].loanStatus;
      }
      data.adminId = data.adminId;
      data.remark = data.remark;
      const relationData: any = {};
      if (data.statusId) {
        const crmStatusData = await this.getCrmStatusData(data.statusId);
        if (crmStatusData.message) return crmStatusData;
        relationData.statusId = crmStatusData.id;
        relationData.statusName = crmStatusData.status;
      }

      if (data?.callAI) {
        relationData.callai_sid = data?.callAI?.callSid;
        relationData.callai_rec = data?.callAI?.recordingUrl;
      }

      if (data.crmReasonId) {
        const crmReasonData = await this.getCrmReasonData(data.crmReasonId);
        if (crmReasonData.message) return crmReasonData;
        relationData.reasonId = crmReasonData.id;
        relationData.reasonName = crmReasonData.reason;
      }
      if (data.dispositionId) {
        const disData: any = await this.getCrmDisData(data.dispositionId, [
          'templateList',
        ]);
        if (disData.message) return disData;
        relationData.dispositionId = disData.id;
        relationData.dispositionName = disData.title;
      }
      if (data.titleId) {
        const crmTitleData: any = await this.crmTitleRepo.getRowWhereData(
          [
            'id',
            'isAmount',
            'isDate',
            'isReference',
            'isReason',
            'isSettlement',
            'title',
            'tempData',
          ],
          { where: { id: data?.titleId } },
        );
        data.templateData = crmTitleData.tempData;
        if (crmTitleData == k500Error) return k500Error;
        relationData.titleId = crmTitleData.id;
        relationData.titleName = crmTitleData.title;
        if (crmTitleData.isAmount) {
          if (!data?.amount) return k422ErrorMessage('Amount is Required');
        }
        if (crmTitleData.isDate && !crmTitleData.isSettlement) {
          if (!data?.due_date) return k422ErrorMessage('Due Date is Required');
          data.due_date = new Date(data.due_date).toJSON();
        }
        if (crmTitleData.isReference) {
          if (!data?.referenceName)
            return k422ErrorMessage('Reference Name is Required');
        }
        if (crmTitleData.isSettlement) {
          if (!data.settlementData || data.settlementData.length == 0)
            return k422ErrorMessage('Settlement data is Required');
        }
        if (crmTitleData.isReason) {
          if (!data.reason)
            return k422ErrorMessage('Reason Status is Required');
        }
      }

      if (data.message) return data;
      delete data.titleId;
      delete data.crmReasonId;
      delete data.dispositionId;
      delete data.statusId;
      if (data.exotelCallRes) relationData.exotel = data.exotelCallRes;

      data.relationData = relationData;
      data.status = '0';
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getCrmStatusData(id) {
    try {
      const crmStatusData = await this.crmStatusRepo.getRowWhereData(
        ['id', 'status'],
        { where: { id } },
      );
      if (crmStatusData == k500Error || !crmStatusData)
        return k422ErrorMessage('source not found!');
      return crmStatusData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getCrmReasonData(id) {
    try {
      const crmReasonData = await this.crmReasonsRepo.getRowWhereData(
        ['id', 'reason'],
        { where: { id } },
      );
      if (crmReasonData == k500Error || !crmReasonData)
        return k422ErrorMessage('Reason not found!');
      return crmReasonData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getCrmDisData(id, extraAttrs = []) {
    try {
      const crmDisData = await this.dispositionRepo.getRowWhereData(
        ['id', 'title', ...extraAttrs],
        { where: { id } },
      );
      if (!crmDisData) return k422ErrorMessage('Disposition not found!');
      if (crmDisData == k500Error) return kInternalError;
      return crmDisData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getCrmActiviType(loanId = null, loanStatus = null) {
    try {
      let type = '0';
      if (loanId && loanStatus == 'Active') {
        const emiOptions: any = { where: { loanId }, order: [['id', 'ASC']] };
        const today = this.typeService.getGlobalDate(new Date());
        const emiData: any = await this.emiRepo.getTableWhereData(
          ['emi_date', 'payment_status', 'payment_due_status'],
          emiOptions,
        );
        if (emiData == k500Error) return kInternalError;
        for (let i = 0; i < emiData.length; i++) {
          try {
            const emi = emiData[i];
            const emiDate = new Date(emi.emi_date);
            if (
              emiDate.getTime() < today.getTime() &&
              emi.payment_status == '0' &&
              emi.payment_due_status == '1'
            ) {
              type = '1';
              break;
            } else if (
              emiDate.getTime() == today.getTime() &&
              emi.payment_status == '0'
            ) {
              type = '2';
              break;
            } else type = '0';
          } catch (error) {}
        }
      }
      return { id: type, title: crmTypeTitle[type] };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async createCrm(body) {
    try {
      // Params validation
      const statusId = body?.statusId;
      if (!statusId) return kParamMissing('statusId');
      const userId = body?.userId;
      if (!userId) return kParamMissing('userId');
      const dispositionId = body?.dispositionId;
      if (!dispositionId) return kParamMissing('dispositionId');
      const adminId = +body?.adminId;
      if (!adminId) return kParamMissing('adminId');
      if (!body.remark) return kParamMissing('remark');

      const createData: any = await this.checkCrmDataAsStatus(body);
      const templateData = createData.templateData ?? { isActive: false };
      delete createData.templateData;
      if (createData.message) return createData;
      const getCrmType: any = await this.getCrmActiviType(
        createData?.loanId,
        createData?.loanStatus,
      );
      if (getCrmType.message) return getCrmType;
      createData.categoryId = getCrmType.id;

      const isAssigned: any = await this.assignAdminAtFirstCrm(
        createData?.userId,
        createData?.adminId,
      );
      if (isAssigned.message) return isAssigned;
      if (
        createData?.settlementData &&
        createData?.settlementData.length != 0
      ) {
        let settlmentData = createData.settlementData.map((ele) => {
          return {
            amount: ele.amount,
            due_date: new Date(ele.due_date),
            read: '0',
            ptpStatus: '0',
          };
        });
        settlmentData = settlmentData.sort(
          (a, b) => a.due_date.getTime() - b.due_date.getTime(),
        );
        createData.settlementData = JSON.stringify(settlmentData);
        createData.due_date = settlmentData[0].due_date;
      } else createData.settlementData = null;
      const admin = await this.commonShared.getAdminData(adminId);
      let currentCrmId;
      let crmSuccessMsg = '';

      createData.lastUpdatedBy = admin?.fullName;
      const roleTitle: any = await this.commonShared.getRoleDetails(
        admin?.roleId,
      );
      createData.adminRole = roleTitle?.title || null;

      // Delete Redis Key Only When Reminder CRM Created With Today's Due Date
      const isCrmForToday =
        createData?.due_date &&
        new Date().toDateString() ===
          new Date(createData.due_date).toDateString();
      const keyUnreadCrm = `${adminId}_UNREAD_DUE_CRMS`;

      if (body?.crmId) {
        const updateCrm: any = await this.crmRepo.updateRowData(
          createData,
          body.crmId,
        );
        if (updateCrm[0] > 0) currentCrmId = body.crmId;
        else return k422ErrorMessage('CRM not updated');
        if (isCrmForToday) await this.redisService.del(keyUnreadCrm);
        createData.id = body.crmId;
        await this.defaulterOnlineCRM(createData);
        // temporary redis code commented due to PROD issue
        // const key = `${userId}_USER_BASIC_DETAILS`;
        // await this.redisService.del(key);
        crmSuccessMsg = 'CRM updated successfully!';
      } else {
        // // CRM rights validation
        const accessResult: any = await this.validateCRMRights(userId, adminId);
        if (accessResult?.message) return accessResult;

        createData.crmOrder = 1;
        const createdCrm: any = await this.crmRepo.createRawData(createData);
        if (createdCrm == k500Error) return k422ErrorMessage('CRM not created');
        if (isCrmForToday) await this.redisService.del(keyUnreadCrm);
        currentCrmId = createdCrm.id;
        await this.defaulterOnlineCRM(createdCrm);
        // temporary redis code commented due to PROD issue
        // const key = `${userId}_USER_BASIC_DETAILS`;
        // await this.redisService.del(key);
        crmSuccessMsg = 'CRM created successfully!';
      }

      await this.updateAndClosePreviousCrms(
        createData.adminId,
        currentCrmId,
        createData.userId,
      );

      await this.storePtpDataInRedis({
        ...createData,
        id: currentCrmId,
      });
      const relationData = createData?.relationData ?? {};
      let userCrm = createData;
      userCrm = { ...createData, ...relationData };
      const loanStatus = userCrm?.loanStatus;
      delete userCrm?.loanStatus;
      delete userCrm?.userId;
      delete userCrm?.adminId;
      delete userCrm?.id;
      delete userCrm?.crmId;
      delete userCrm?.relationData;
      delete userCrm?.lastUpdatedBy;
      delete userCrm?.pinCrm;
      userCrm.adminName = admin?.fullName;
      userCrm.createdAt = new Date().toJSON();

      if (loanStatus === 'Active') {
        const aatr = ['lastCrm'];
        const crmOpt = { where: { id: userId } };
        const crmData = await this.userRepository.getRowWhereData(aatr, crmOpt);
        if (crmData == k500Error) return kInternalError;
        const lastCrm = crmData?.lastCrm ?? {};
        const lastCreatedDate = lastCrm?.createdAt
          ? this.typeService.getGlobalDate(lastCrm.createdAt)
          : null;
        const toDay = this.typeService.getGlobalDate(new Date());
        const isSameDay = lastCreatedDate
          ? lastCreatedDate.toJSON().slice(0, 10) == toDay.toJSON().slice(0, 10)
          : false;
        userCrm.crmCount = isSameDay
          ? lastCrm.crmCount ?? 0
          : (lastCrm.crmCount ?? 0) + 1;
        userCrm.crmCountDate = isSameDay
          ? lastCrm?.crmCountDate
          : this.typeService.getGlobalDate(new Date());
      }

      let pinCrm;
      if (body.pinCrm) {
        pinCrm = {
          id: currentCrmId,
          ...userCrm,
        };
      }

      const updatedData = { lastCrm: userCrm, pinCrm };

      await this.userRepository.updateRowData(updatedData, userId);

      if (crmSuccessMsg) {
        // Sending notification, SMS and Email to current or past defaulter user
        if (templateData.isActive) {
          await this.notifyDefaulterAboutCRM(
            userId,
            createData.loanId,
            adminId,
            templateData,
            createData,
            currentCrmId,
          );
        }
        if (templateData.titleId == 81) {
          const disData: any = await this.getCrmDisData(dispositionId, [
            'templateList',
          ]);
          if (disData.message) return disData;
          let templateList = disData.templateList ?? [];
          const templateData = templateList.find(
            (el) => el.isActive && el.titleId == 44,
          );

          await this.notifyDefaulterAboutCRM(
            userId,
            createData.loanId,
            adminId,
            templateData,
            createData,
            currentCrmId,
          );
        }

        return crmSuccessMsg;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async resetCrmCount() {
    const today = new Date();
    const restMonth = today.getDate() === COLLECTION_PERFORMANCE_CYCLE_DATE;

    if (!restMonth) return true;

    const dataToUpdate = {
      lastCrm: Sequelize.literal(`
        jsonb_set(
          COALESCE("lastCrm", '{}'),
          '{crmCount}',
          '0'::jsonb
        )
      `),
    };

    const options = {
      where: Sequelize.where(
        Sequelize.cast(Sequelize.json('lastCrm.crmCount'), 'int'),
        { [Op.gt]: 0 },
      ),
    };

    const result = await this.userRepository.updateRowDataWithOptions(
      dataToUpdate,
      options,
      null,
    );
    return {};
  }

  private async storePtpDataInRedis(reqData) {
    const id = reqData.id;
    const userId = reqData.userId;
    const adminId = reqData.adminId;
    const amount = reqData.amount;
    const settlementData = reqData.settlementData;
    const fullName = reqData.fullName;
    let dueDate = reqData.due_date;

    let obj: any = {};
    const todayDate = new Date().toJSON().substring(0, 10);

    if (dueDate && userId && adminId && amount && id) {
      if (new Date(dueDate).toJSON().substring(0, 10) == todayDate) {
        const dueAmount = await this.defaulterService.getDueAmountNew(
          null,
          userId,
        );
        obj = {
          id,
          userId,
          amount,
          totalAmount: dueAmount,
          userName: fullName ?? null,
          settlementData:
            settlementData && typeof settlementData == 'string'
              ? JSON.parse(settlementData)
              : [],
          dueDate: new Date(dueDate).toISOString(),
        };
        const redisKey = `PTP_DATA_${todayDate}_${adminId}`;
        let ptpDataForRedis = await this.redisService.get(redisKey);
        if (ptpDataForRedis) ptpDataForRedis = JSON.parse(ptpDataForRedis);
        else ptpDataForRedis = [];
        ptpDataForRedis.push(obj);
        await this.redisService.set(
          redisKey,
          JSON.stringify(ptpDataForRedis),
          NUMBERS.THREE_HOURS_IN_SECONDS,
        );
      }
    }
    return true;
  }

  private async validateCRMRights(userId, adminId) {
    try {
      // Get user's master data
      const attributes = ['loanId', 'status'];
      const options = { order: [['id', 'DESC']], where: { userId } };
      const masterData = await this.masterRepo.getRowWhereData(
        attributes,
        options,
      );
      if (masterData == k500Error) return kInternalError;
      if (!masterData) return k422ErrorMessage(kNoDataFound);

      // Check which rights needs to validate as per user's loan status
      const statusData = masterData.status ?? {};
      const loanStatus = statusData.loan ?? '-2';
      let loanStage = StrAdminAccess.preDisbursementCRM;
      if (loanStatus == 6) {
        loanStage = StrAdminAccess.postDisbursementCRM;
        const attributes = ['emi_date', 'payment_status', 'payment_due_status'];
        const options = {
          order: [['id', 'ASC']],
          where: { loanId: masterData.loanId },
        };
        const emiList = await this.emiRepo.getTableWhereData(
          attributes,
          options,
        );
        if (emiList == k500Error) return kInternalError;

        const today = this.typeService.getGlobalDate(new Date());
        for (let index = 0; index < emiList.length; index++) {
          try {
            const emiData = emiList[index];
            const isDefault =
              emiData.payment_due_status == '1' &&
              emiData.payment_status == '0';
            if (isDefault) {
              loanStage = StrAdminAccess.delayUnpaidEMICRM;
              break;
            }

            // Pre EMI
            const emiDate = new Date(emiData.emi_date);
            if (today.getTime() <= emiDate.getTime()) {
              const dayDifference = this.typeService.dateDifference(
                today,
                emiDate,
              );
              if (dayDifference <= 7) loanStage = StrAdminAccess.upcomingEMICRM;
            }
          } catch (error) {}
        }
      }

      return await this.adminRepo.checkHasAccess(adminId, loanStage, 2);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Sending notification, SMS and Email to current or past defaulter user
  private async notifyDefaulterAboutCRM(
    userId,
    loanId,
    adminId,
    templateData,
    crmData,
    currentCrmId,
  ) {
    try {
      // Validation -> Parameters
      if (!loanId) return kParamMissing('loanId');

      // Query
      const attributes = ['id'];
      const options = { where: { loanId, payment_due_status: '1' } };
      const emiData = await this.emiRepo.getRowWhereData(attributes, options);
      if (!emiData) return k422ErrorMessage(kNoDataFound);
      if (emiData == k500Error) return kInternalError;

      // Get agent data
      const adminData = await this.adminRepo.getRowWhereData(['companyPhone'], {
        where: { id: adminId },
      });
      if (!adminData) return k422ErrorMessage(kNoDataFound);
      if (adminData == k500Error) return kInternalError;
      if (!adminData?.companyPhone) return k422ErrorMessage(kNoDataFound);
      const agentPhone = await this.cryptService.decryptText(
        adminData.companyPhone,
      );

      const appType = crmData?.appType;
      // Required content
      let newPTPDate = '-';
      let newPTPTime = '-';
      let previousPTPDate = '-';
      let previousPTPTime = '-';
      let previousAmount = '-';
      let previousPTPData = {};
      let totalSettlementParts = 0;
      let newAmount = 0;

      // Send notification
      let notificationTitle = '-';
      let notificationBody = '-';
      if (templateData.notification) {
        notificationTitle =
          templateData.notificationTitle ?? StrDefault.notificationTitle;
        notificationBody = templateData.notificationBody ?? '';
        // Agent number
        if (notificationBody.includes('##AGENT_NUMBER##')) {
          notificationBody = notificationBody.replace(
            /##AGENT_NUMBER##/g,
            agentPhone,
          );
        }
        // New PTP date
        if (notificationBody.includes('##NEW_DATE##')) {
          const dueDate = new Date(crmData.due_date);
          if (isNaN(dueDate.getTime())) return kInvalidParamValue('due_date');
          const dateInfo = this.dateService.dateToReadableFormat(dueDate);
          newPTPDate = dateInfo.readableStr;
          newPTPTime = `${dateInfo.hours}:${dateInfo.minutes} ${dateInfo.meridiem}`;
          notificationBody = notificationBody.replace(
            /##NEW_DATE##/g,
            newPTPDate,
          );
        }
        // New PTP time
        if (notificationBody.includes('##NEW_TIME##')) {
          notificationBody = notificationBody.replace(
            /##NEW_TIME##/g,
            newPTPTime,
          );
        }
        // Previous date
        if (notificationBody.includes('##PREVIOUS_DATE##')) {
          const attributes = ['amount', 'due_date'];
          const options = {
            order: [['id', 'DESC']],
            where: {
              loanId,
              [Op.or]: [
                {
                  relationData: {
                    [Op.contains]: { titleName: 'Will pay part payment' },
                  },
                },
                {
                  relationData: {
                    [Op.contains]: { titleName: 'Promise to Pay' },
                  },
                },
                {
                  relationData: {
                    [Op.contains]: {
                      titleName: 'PTP Broken and New PTP Given',
                    },
                  },
                },
              ],
              id: { [Op.ne]: currentCrmId },
            },
          };
          const crmData = await this.crmRepo.getRowWhereData(
            attributes,
            options,
          );
          if (!crmData) return k422ErrorMessage(kNoDataFound);
          if (crmData == k500Error) return kInternalError;
          previousPTPData = crmData;

          previousAmount = crmData.amount?.toString() ?? '-';
          const dueDate = new Date(crmData.due_date);
          if (isNaN(dueDate.getTime())) return kInvalidParamValue('due_date');
          const dateInfo = this.dateService.dateToReadableFormat(dueDate);
          previousPTPDate = dateInfo.readableStr;
          previousPTPTime = `${dateInfo.hours}:${dateInfo.minutes} ${dateInfo.meridiem}`;
          notificationBody = notificationBody.replace(
            /##PREVIOUS_DATE##/g,
            previousPTPDate,
          );
        }
        // Previous PTP time
        if (notificationBody.includes('##PREVIOUS_TIME##')) {
          notificationBody = notificationBody.replace(
            /##PREVIOUS_TIME##/g,
            previousPTPTime,
          );
        }
        // Previous amount
        if (notificationBody.includes('##PREVIOUS_AMOUNT##')) {
          notificationBody = notificationBody.replace(
            /##PREVIOUS_AMOUNT##/g,
            `Rs.${previousAmount}`,
          );
        }
        // New amount
        if (notificationBody.includes('##NEW_AMOUNT##')) {
          if (!crmData.amount) return kParamMissing('amount');
          newAmount = crmData.amount;
          notificationBody = notificationBody.replace(
            /##NEW_AMOUNT##/g,
            `Rs.${newAmount.toString()}`,
          );
        }
        // Settlement parts
        if (notificationBody.includes('##TOTAL_PARTS##')) {
          let settlementData = crmData.settlementData ?? '';
          if (!settlementData) return kParamMissing('settlementData');
          settlementData = JSON.parse(settlementData);
          totalSettlementParts = settlementData.length;
          notificationBody = notificationBody.replace(
            /##TOTAL_PARTS##/g,
            totalSettlementParts.toString(),
          );
          settlementData.forEach((el, index) => {
            try {
              const targetDate = this.typeService.getGlobalDate(el.due_date);
              if (isNaN(targetDate.getTime()))
                return kInvalidParamValue('due_date');
              const targetDateInfo =
                this.dateService.dateToReadableFormat(targetDate);
              const date = targetDateInfo.readableStr;
              const time =
                targetDateInfo.hours +
                ':' +
                targetDateInfo.minutes +
                ' ' +
                targetDateInfo.meridiem;

              let part = ` payment of Rs.${el.amount} needs to be done on or before ${date} and ${time}`;
              if (index < settlementData.length - 1) part += ' and ';
              if (index == 0) {
                notificationBody = notificationBody.replace(
                  /##PART_ONE##/g,
                  '1st' + part,
                );
              } else if (index == 1) {
                notificationBody = notificationBody.replace(
                  /##PART_TWO##/g,
                  '2nd' + part,
                );
              } else if (index == 2) {
                notificationBody = notificationBody.replace(
                  /##PART_THREE##/g,
                  '3rd' + part,
                );
              }
            } catch (error) {}
          });
          if (notificationBody.includes('##PART_ONE##'))
            notificationBody = notificationBody.replace(/##PART_ONE##/g, '');
          if (notificationBody.includes('##PART_TWO##'))
            notificationBody = notificationBody.replace(/##PART_TWO##/g, '');
          if (notificationBody.includes('##PART_THREE##'))
            notificationBody = notificationBody.replace(/##PART_THREE##/g, '');
        }
      }

      // Send email
      if (templateData.email) {
        const emailTitle = templateData.emailTitle ?? StrDefault.emailTitle;
        const emailTemplatePath = await this.commonShared.getEmailTemplatePath(
          kEmailPaymentReminderCRM,
          appType,
          null,
          null,
        );
        if (!emailTemplatePath)
          return k422ErrorMessage('Email template path is missing');
        let html = (await fs.readFileSync(emailTemplatePath)).toString();
        if (html.includes('##HEADER_IMAGE##')) {
          const imageUrl = templateData.headerImageHTML;
          if (!imageUrl) return k422ErrorMessage('headerImageHTML is missing');
          html = html.replace(/##HEADER_IMAGE##/g, imageUrl);
        }
        if (html.includes('##CONTENT##')) {
          const content = templateData.emailContent;
          if (!content) return k422ErrorMessage('Html content is missing');
          html = html.replace(/##CONTENT##/g, content);
        }
        if (html.includes('##NBFCSHORTNAME##')) {
          html = html.replace(
            /##NBFCSHORTNAME##/g,
            EnvConfig.nbfc.nbfcCamelCaseName,
          );
        }

        if (html.includes('##NBFCLOGO##')) {
          html = html.replace(/##NBFCLOGO##/g, EnvConfig.url.nbfcLogo);
        }
        if (html.includes('##NBFCINFO##')) {
          html = html.replace(/##NBFCINFO##/g, nbfcInfoStr);
        }
        // Payment button
        if (html.includes('##PAYMENT_BUTTON##')) {
          const buttonHtml = templateData.buttonHtml;
          if (!buttonHtml) return k422ErrorMessage('buttonHtml is missing');
          html = html.replace(/##PAYMENT_BUTTON##/g, buttonHtml);
        }
        // Payment link
        if (html.includes('##PAYMENT_LINK##')) {
          const paymentKey = loanId * 484848;
          const paymentLink = nPaymentRedirect + paymentKey;
          html = html.replace(/##PAYMENT_LINK##/g, paymentLink);
        }
        if (html.includes('##AGENT_NUMBER##'))
          // Agent number
          html = html.replace(/##AGENT_NUMBER##/g, agentPhone);
        // Email title
        if (html.includes('##TITLE##')) {
          const titleContent = templateData.emailTitleContent;
          if (!titleContent) return k422ErrorMessage('titleContent is missing');
          html = html.replace(/##TITLE##/g, titleContent);
        }

        // New PTP date
        if (html.includes('##NEW_DATE##')) {
          if (!newPTPDate || !newPTPTime) {
            const dueDate = new Date(crmData.due_date);
            if (isNaN(dueDate.getTime())) return kInvalidParamValue('due_date');
            newPTPDate = this.typeService.jsonToReadableDate(dueDate.toJSON());
            const hours = dueDate.getHours();
            const minutes = dueDate.getMinutes();
            const tail = hours >= 12 ? 'PM' : 'AM';
            newPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
              minutes <= 9 ? '0' + minutes : minutes
            } ${tail}`;
          }
          html = html.replace(/##NEW_DATE##/g, newPTPDate);
        }
        // New PTP time
        if (html.includes('##NEW_TIME##')) {
          html = html.replace(/##NEW_TIME##/g, newPTPTime);
        }
        // Previous date
        if (html.includes('##PREVIOUS_DATE##')) {
          if (!previousPTPData) {
            const attributes = ['amount', 'due_date'];
            const options = {
              order: [['id', 'DESC']],
              where: {
                loanId,
                [Op.or]: [
                  {
                    relationData: {
                      [Op.contains]: { titleName: 'Will pay part payment' },
                    },
                  },
                  {
                    relationData: {
                      [Op.contains]: { titleName: 'Promise to Pay' },
                    },
                  },
                  {
                    relationData: {
                      [Op.contains]: {
                        titleName: 'PTP Broken and New PTP Given',
                      },
                    },
                  },
                ],
                id: { [Op.ne]: currentCrmId },
              },
            };
            const crmData = await this.crmRepo.getRowWhereData(
              attributes,
              options,
            );
            if (!crmData) return k422ErrorMessage(kNoDataFound);
            if (crmData == k500Error) return kInternalError;

            previousAmount = crmData.amount?.toString() ?? '-';
            const dueDate = new Date(crmData.due_date);
            if (isNaN(dueDate.getTime())) return kInvalidParamValue('due_date');
            previousPTPDate = this.typeService.jsonToReadableDate(
              dueDate.toJSON(),
            );
            const hours = dueDate.getHours();
            const minutes = dueDate.getMinutes();
            const tail = hours >= 12 ? 'PM' : 'AM';
            previousPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
              minutes <= 9 ? '0' + minutes : minutes
            } ${tail}`;
          }
          html = html.replace(/##PREVIOUS_DATE##/g, previousPTPDate);
        }
        // Previous PTP time
        if (html.includes('##PREVIOUS_TIME##')) {
          html = html.replace(/##PREVIOUS_TIME##/g, previousPTPTime);
        }
        // Previous amount
        if (html.includes('##PREVIOUS_AMOUNT##')) {
          html = html.replace(/##PREVIOUS_AMOUNT##/g, `Rs.${previousAmount}`);
        }
        // New amount
        if (html.includes('##NEW_AMOUNT##')) {
          if (!crmData.amount) return kParamMissing('amount');
          newAmount = crmData.amount;
          html = html.replace(/##NEW_AMOUNT##/g, `Rs.${newAmount.toString()}`);
        }
        // Settlement parts
        if (html.includes('##TOTAL_PARTS##')) {
          let settlementData = crmData.settlementData ?? '';
          if (!settlementData) return kParamMissing('settlementData');
          settlementData = JSON.parse(settlementData);
          totalSettlementParts = settlementData.length;
          html = html.replace(
            /##TOTAL_PARTS##/g,
            totalSettlementParts.toString(),
          );
          settlementData.forEach((el, index) => {
            try {
              const targetDate = this.typeService.getGlobalDate(el.due_date);
              if (isNaN(targetDate.getTime()))
                return kInvalidParamValue('due_date');
              const targetDateInfo =
                this.dateService.dateToReadableFormat(targetDate);
              const date = targetDateInfo.readableStr;
              const time =
                targetDateInfo.hours +
                ':' +
                targetDateInfo.minutes +
                ' ' +
                targetDateInfo.meridiem;

              let part = ` payment of Rs.${el.amount} needs to be done on or before ${date} and ${time}`;
              if (index < settlementData.length - 1) part += ' and ';
              if (index == 0) {
                html = html.replace(/##PART_ONE##/g, '1st' + part);
              } else if (index == 1) {
                html = html.replace(/##PART_TWO##/g, '2nd' + part);
              } else if (index == 2) {
                html = html.replace(/##PART_THREE##/g, '3rd' + part);
              }
            } catch (error) {}
          });
          if (html.includes('##PART_ONE##'))
            html = html.replace(/##PART_ONE##/g, '');
          if (html.includes('##PART_TWO##'))
            html = html.replace(/##PART_TWO##/g, '');
          if (html.includes('##PART_THREE##'))
            html = html.replace(/##PART_THREE##/g, '');
        }

        // Get user email id
        const options = {
          where: {
            id: userId,
          },
        };
        const userData = await this.userRepository.getRowWhereData(
          ['email'],
          options,
        );
        if (!userData) return k422ErrorMessage(kNoDataFound);
        if (userData == k500Error) return kInternalError;

        // Sending email to user
        const email = userData.email;
        await this.sharedNotification.sendEmailToUser(
          StrDefault.customEmail,
          email,
          userId,
          { subject: emailTitle, html, userId, appType },
        );
      }

      // Send sms
      let isMsgSent = false;
      const smsOptions: any = {};
      let smsId = '';
      if (templateData.sms) {
        const smsTemplateId = templateData.smsTemplateId ?? '';
        const lspSmsTemplateId = templateData.lspSmsTemplateId ?? '';
        if (!smsTemplateId) return kParamMissing('smsTemplateId');
        if (!lspSmsTemplateId) return kParamMissing('lspSmsTemplateId');
        const smsData =
          appType == 1
            ? kMsg91Templates[smsTemplateId]
            : kLspMsg91Templates[lspSmsTemplateId];
        const varOptions = smsData.varOptions ?? [];
        for (let index = 0; index < varOptions.length; index++) {
          try {
            const el = varOptions[index];
            const key = el.key;
            const title = el.title;
            if (key && title) {
              // New amount
              if (title == '##NEW_AMOUNT##') {
                if (!crmData.amount) return kParamMissing('amount');
                newAmount = crmData.amount;
                smsOptions[key] = `Rs.${newAmount.toString()}`;
              }
              // Agent number
              else if (title == '##AGENT_NUMBER##')
                smsOptions[key] = agentPhone;
              // PTP
              else if (title == '##NEW_DATE## AND ##NEW_TIME##') {
                if (!newPTPDate || !newPTPTime) {
                  const dueDate = new Date(crmData.due_date);
                  if (isNaN(dueDate.getTime()))
                    return kInvalidParamValue('due_date');
                  newPTPDate = this.typeService.jsonToReadableDate(
                    dueDate.toJSON(),
                  );
                  const hours = dueDate.getHours();
                  const minutes = dueDate.getMinutes();
                  const tail = hours >= 12 ? 'PM' : 'AM';
                  newPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
                    minutes <= 9 ? '0' + minutes : minutes
                  } ${tail}`;
                }
                smsOptions[key] = newPTPDate + ' and ' + newPTPTime;
              }
              // Previous amount
              else if (title == '##PREVIOUS_AMOUNT##') {
                smsOptions[key] = `Rs.${previousAmount}`;
              }
              // Previous date
              else if (title == '##PREVIOUS_DATE##') {
                if (!previousPTPData) {
                  const attributes = ['amount', 'due_date'];
                  const options = {
                    order: [['id', 'DESC']],
                    where: {
                      loanId,
                      [Op.or]: [
                        {
                          relationData: {
                            [Op.contains]: {
                              titleName: 'Will pay part payment',
                            },
                          },
                        },
                        {
                          relationData: {
                            [Op.contains]: { titleName: 'Promise to Pay' },
                          },
                        },
                        {
                          relationData: {
                            [Op.contains]: {
                              titleName: 'PTP Broken and New PTP Given',
                            },
                          },
                        },
                      ],
                      id: { [Op.ne]: currentCrmId },
                    },
                  };
                  const crmData = await this.crmRepo.getRowWhereData(
                    attributes,
                    options,
                  );
                  if (!crmData) return k422ErrorMessage(kNoDataFound);
                  if (crmData == k500Error) return kInternalError;

                  previousAmount = crmData.amount?.toString() ?? '-';
                  const dueDate = new Date(crmData.due_date);
                  if (isNaN(dueDate.getTime()))
                    return kInvalidParamValue('due_date');
                  previousPTPDate = this.typeService.jsonToReadableDate(
                    dueDate.toJSON(),
                  );
                  const hours = dueDate.getHours();
                  const minutes = dueDate.getMinutes();
                  const tail = hours >= 12 ? 'PM' : 'AM';
                  previousPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
                    minutes <= 9 ? '0' + minutes : minutes
                  } ${tail}`;
                }
                smsOptions[key] = previousPTPDate;
              }
              // Previous time
              else if (title == '##PREVIOUS_TIME##')
                smsOptions[key] = previousPTPTime;
              // New date
              else if (title == '##NEW_DATE##') {
                if (!newPTPDate || !newPTPTime) {
                  const dueDate = new Date(crmData.due_date);
                  if (isNaN(dueDate.getTime()))
                    return kInvalidParamValue('due_date');
                  newPTPDate = this.typeService.jsonToReadableDate(
                    dueDate.toJSON(),
                  );
                  const hours = dueDate.getHours();
                  const minutes = dueDate.getMinutes();
                  const tail = hours >= 12 ? 'PM' : 'AM';
                  newPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
                    minutes <= 9 ? '0' + minutes : minutes
                  } ${tail}`;
                }
                smsOptions[key] = newPTPDate;
              } // New time
              else if (title == '##NEW_TIME##') {
                if (!newPTPDate || !newPTPTime) {
                  const dueDate = new Date(crmData.due_date);
                  if (isNaN(dueDate.getTime()))
                    return kInvalidParamValue('due_date');
                  newPTPDate = this.typeService.jsonToReadableDate(
                    dueDate.toJSON(),
                  );
                  const hours = dueDate.getHours();
                  const minutes = dueDate.getMinutes();
                  const tail = hours >= 12 ? 'PM' : 'AM';
                  newPTPTime = `${hours <= 9 ? '0' + hours : hours}:${
                    minutes <= 9 ? '0' + minutes : minutes
                  } ${tail}`;
                }
                smsOptions[key] = newPTPTime;
              }
            }
          } catch (error) {}
        }
        smsId = appType == 1 ? smsTemplateId : lspSmsTemplateId;
        isMsgSent = true;
      }
      const userData = [];
      userData.push({ userId, appType });
      const alertData = {
        userData,
        title: notificationTitle ?? '-',
        content: notificationBody ?? '-',
        adminId,
        data: null,
        smsOptions,
        isMsgSent,
        smsId,
      };

      if (isMsgSent || templateData.notification) {
        await this.sharedNotification.sendNotificationToUser(alertData);
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async defaulterOnlineCRM(crm) {
    try {
      if (crm) {
        const options = {
          where: {
            userId: crm.userId,
            lastOnline: this.typeService.getGlobalDate(new Date()).toJSON(),
            crmId: { [Op.eq]: null },
          },
        };

        const att = ['id'];
        const result = await this.defaulterOnlineRepo.getRoweData(att, options);
        if (result?.id) {
          const update = { crmId: crm.id };
          await this.defaulterOnlineRepo.updateRowData(update, result.id);
        }
      }
    } catch (error) {}
  }

  async updateAndClosePreviousCrms(adminId, currentCrmId, userId = null) {
    try {
      if (!userId) return kInternalError;
      const closingDate = new Date();
      const updateData = { status: '1', closingDate };
      const crmWhere: any = { userId: userId, id: { [Op.ne]: currentCrmId } };
      const lastCrm: any = await this.crmRepo.getRowWhereData(
        ['id', 'adminId'],
        {
          where: crmWhere,
          order: [['id', 'DESC']],
        },
      );
      if (lastCrm === k500Error) return kInternalError;
      if (!lastCrm) return {};
      if (lastCrm.id == currentCrmId) {
        const data: any = await this.crmRepo.updateRowData(
          { ...updateData, crmOrder: { [Op.or]: [null, -1] } },
          lastCrm.id,
        );
        if (data === k500Error) return kInternalError;
      } else {
        const data: any = await this.crmRepo.updateRowData(
          { ...updateData, crmOrder: 0 },
          lastCrm.id,
        );
        if (data === k500Error) return kInternalError;
      }
      const updatedData: any = await this.crmRepo.updateRowWhereData(
        { crmOrder: -1 },
        { where: { ...crmWhere, adminId } },
      );
      if (updatedData === k500Error) return kInternalError;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async assignAdminAtFirstCrm(userId, adminId) {
    try {
      const adminData = await this.adminRepo.getRoweData(['id', 'roleId'], {
        where: { id: adminId },
        include: [
          {
            model: Department,
            attributes: ['id', 'department'],
            where: { department: SUPPORT },
          },
        ],
      });
      if (!adminData || adminData == k500Error) return {};
      const isNotAssign = await this.userRepository.getRowWhereData(['id'], {
        where: { id: userId, assignTo: { [Op.eq]: null } },
      });
      if (isNotAssign && isNotAssign != k500Error)
        await this.userRepository.updateRowData({ assignTo: adminId }, userId);
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async departmentFromAdmin(adminId) {
    try {
      const departmentInclude = {
        model: Department,
        attributes: ['id', 'department', 'loanStatus'],
      };
      const adminData = await this.adminRepo.getRoweData(['id'], {
        where: { id: adminId },
        include: [departmentInclude],
      });
      if (adminData == k500Error || !adminData) return kInternalError;
      const deparmentData = adminData?.departmentData;
      return {
        departmentId: deparmentData.id,
        loanStatus: deparmentData.loanStatus,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getSourceData(query, filter = {}) {
    try {
      if (!query?.adminId) return kParamsMissing;
      const departmentData: any = await this.departmentFromAdmin(query.adminId);
      if (departmentData.message) return departmentData;
      const deparmentIds = [departmentData.departmentId];
      const key = `${deparmentIds}_SOURCE_DATA`;
      let data = await this.redisService.getKeyDetails(key);
      if (!data) {
        data = await this.crmStatusRepo.getTableWhereData(['id', 'status'], {
          where: { departmentIds: { [Op.contains]: deparmentIds }, ...filter },
        });
        if (data == k500Error) return kInternalError;
        await this.redisService.set(
          key,
          JSON.stringify(data),
          NUMBERS.SEVEN_DAYS_IN_SECONDS,
        );
      } else data = JSON.parse(data);
      if (query?.needPhone != 'true') {
        const idx = data.findIndex((el) => el.status == 'Phone');
        data.splice(idx, 1);
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getTitlesData(query) {
    try {
      if (!query?.dispositionId || !query.departmentId) return kParamsMissing;
      const loanStatus = query?.loanStatus;
      const disId = query.dispositionId;
      const departmentIds = [query.departmentId];
      let status = '0';
      if (loanStatus == 'Active') status = '1';
      const key = `${disId}_${departmentIds}_${status}_CRMTITLES`;
      let crmTitleData = await this.redisService.getKeyDetails(key);
      if (!crmTitleData) {
        crmTitleData = await this.crmTitleRepo.getTableWhereData(
          [
            'id',
            'title',
            'isAmount',
            'isDate',
            'isReference',
            'isReason',
            'isSettlement',
            'loanStatus',
            'crmDispositionId',
            'crmDispositionIds',
            'tempData',
          ],
          {
            where: {
              [Op.or]: [
                { crmDispositionId: disId },
                { crmDispositionIds: { [Op.contains]: [disId] } },
              ],
              departmentIds: { [Op.contains]: departmentIds },
              loanStatus: { [Op.or]: ['2', status] },
            },
          },
        );
        await this.redisService.set(
          key,
          JSON.stringify(crmTitleData),
          NUMBERS.SEVEN_DAYS_IN_SECONDS,
        );
      } else crmTitleData = JSON.parse(crmTitleData);
      // for getting Date and Time
      for (let i = 0; i < crmTitleData.length; i++) {
        const element = crmTitleData[i];
        if (
          element['title'] == 'Will process after sometime' &&
          element['loanStatus'] === 0 &&
          element['crmDispositionId'] === 1
        ) {
          crmTitleData[i]['isDate'] = true;
        } else if (element['title'] == 'Reminder') {
          crmTitleData[i]['isDate'] = true;
        }
      }

      if (crmTitleData == k500Error) return kInternalError;
      return crmTitleData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getCrmReasons(query) {
    try {
      const departmentId = query?.departmentId;
      const userId = query?.userId;
      if (!departmentId) return kParamMissing('departmentId');

      //because reasons is only for collection department
      if (departmentId != COLLECTION_DEPARTMENT_ID) return [];

      const data = await this.commonShared.getCRMReasons();
      if (!data?.length) return data;

      //get previous crm reason validate
      const previousReason: any = await this.lastCrmReason(userId);
      //check previous crm reason data
      if (previousReason?.crmReasonData && previousReason != k500Error) {
        const previousCrmReason = previousReason?.crmReasonData;
        data.previousReason.reason = { isReason: previousCrmReason.reason };
        data.previousReason.isReason = previousCrmReason.isReason;
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async lastCrmReason(userId) {
    try {
      return this.crmRepo.getRowWhereData(['id', 'reason'], {
        where: { userId },
        include: [{ model: CrmReasonEntity, attributes: ['id', 'reason'] }],
        order: [['id', 'DESC']],
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async fetchUnreadDueCrm(query) {
    if (!query.adminId) return kParamMissing('adminId is missing!');

    const now = new Date();
    const startOfToday = new Date(now);
    startOfToday.setHours(0, 0, 0, 0);
    const endOfToday = new Date(now);
    endOfToday.setHours(23, 59, 59, 999);

    const redisKey = `${query?.adminId}_UNREAD_DUE_CRMS`;
    let redisData = await this.redisService.get(redisKey);
    if (redisData) {
      redisData = JSON.parse(redisData);
      return this.filterDueToday(redisData, now);
    }

    // return only those due up to the current time
    const userInlude = {
      model: registeredUsers,
      attributes: ['fullName', 'phone'],
    };

    const attributes = [
      'id',
      'userId',
      'adminId',
      'createdAt',
      'due_date',
      'settlementData',
      'remark',
      'relationData',
    ];

    const options = {
      where: {
        adminId: query.adminId,
        read: { [Op.or]: [{ [Op.eq]: null }, { [Op.eq]: false }] },
        status: { [Op.ne]: '1' },
        due_date: {
          [Op.lte]: endOfToday,
        },
      },
      order: [['createdAt', 'DESC']],
      include: [userInlude],
    };

    const crmData = await this.crmRepo.getTableWhereData(attributes, options);
    if (crmData == k500Error) return k422ErrorMessage('CRM not found');
    const updatedCrmData: any[] = [];
    for (let i = 0; i < crmData.length; i++) {
      try {
        const element = crmData[i];
        crmData[i].registeredUsers.phone = this.cryptService.decryptPhone(
          element.registeredUsers.phone,
        );
        if (crmData[i]?.settlementData)
          crmData[i].settlementData = JSON.parse(element.settlementData);
        const relationData: any = element.relationData;
        delete element.relationData;

        updatedCrmData.push({
          ...element,
          ...relationData,
        });
      } catch (error) {}
    }

    // Setting All Day Data for Remaining Hours of the Day
    const remainingSeconds = Math.floor(
      (new Date().setHours(24, 0, 0, 0) - Date.now()) / 1000,
    );

    await this.redisService.set(
      redisKey,
      JSON.stringify(updatedCrmData),
      remainingSeconds,
    );
    return this.filterDueToday(updatedCrmData, now);
  }

  filterDueToday(crmList, now) {
    return crmList.filter((crm) => {
      const dueDate = new Date(crm.due_date);
      return dueDate <= now;
    });
  }

  async updateUnreadDueCrms(body) {
    try {
      const crmIds: any = body.crmIds;
      const adminId = body?.adminId ?? '';
      if (!crmIds || crmIds.length == 0)
        return kParamMissing('crmIds should not be empty!');
      for (let i = 0; i < crmIds.length; i++) {
        try {
          const element = crmIds[i];
          const getCrmReadTitle: any = await this.crmRepo.getRowWhereData(
            ['id', 'settlementData'],
            { where: { id: element, settlementData: { [Op.ne]: null } } },
          );
          let udpateData: any = {};
          if (getCrmReadTitle) {
            const settlementData = JSON.parse(getCrmReadTitle?.settlementData);
            const signlSettlement = settlementData.findIndex((ele) => {
              return ele?.read == '0';
            });
            if (signlSettlement != -1) {
              settlementData[signlSettlement].read = '1';
              if (signlSettlement + 1 < settlementData?.length) {
                const upcoming = settlementData[signlSettlement + 1];
                udpateData.due_date = upcoming.due_date;
              } else {
                udpateData.read = true;
              }
              udpateData.settlementData = JSON.stringify(settlementData);
              await this.crmRepo.updateRowData(udpateData, element);
            } else {
              await this.crmRepo.updateRowData({ read: true }, element);
            }
          } else this.crmRepo.updateRowData({ read: true }, element);

          // Delete crm data from redis
          await this.redisService.del(`${adminId}_UNREAD_DUE_CRMS`);
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateCrmData() {
    try {
      const options: any = {};
      if (!isUAT) {
        options.where = { relationData: { [Op.eq]: null } };
        options.limit = 100;
      }
      const crmData = await this.crmRepo.getTableWhereData(
        ['id', 'titleId', 'crmReasonId', 'crmStatusId'],
        options,
      );
      if (crmData.length == 0) return [];
      for (let i = 0; i < crmData.length; i++) {
        try {
          const crm: any = crmData[i];
          const relationData: any = {};
          if (crm.crmStatusId) {
            const crmStatusData = await this.crmStatusRepo.getRowWhereData(
              ['id', 'status'],
              { where: { id: crm.crmStatusId } },
            );
            if (crmStatusData == k500Error || !crmStatusData) continue;
            relationData.statusId = crmStatusData.id;
            relationData.statusName = crmStatusData.status;
          }
          if (crm.crmReasonId) {
            const crmReasonData = await this.crmReasonsRepo.getRowWhereData(
              ['id', 'reason'],
              { where: { id: crm.crmReasonId } },
            );
            if (crmReasonData == k500Error || !crmReasonData) continue;
            relationData.reasonId = crmReasonData.id;
            relationData.reasonName = crmReasonData.reason;
          }
          if (crm.titleId) {
            const crmTitleData: any = await this.crmTitleRepo.getRowWhereData(
              ['id', 'title'],
              { where: { id: crm.titleId } },
            );
            if (crmTitleData == k500Error || !crmTitleData) continue;
            relationData.titleId = crmTitleData.id;
            relationData.titleName = crmTitleData.title;
          }
          await this.crmRepo.updateRowData({ relationData }, crm.id);
        } catch (error) {}
      }
      if (!isUAT) return this.migrateCrmData();
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migaretCrmTitleData() {
    try {
      const titleData = await this.crmTitleRepo.getTableWhereData(
        ['id', 'departmentIds', 'departmentId'],
        { where: { departmentIds: { [Op.eq]: null } } },
      );
      if (titleData == k500Error) return kInternalError;
      for (let i = 0; i < titleData.length; i++) {
        try {
          const title = titleData[i];
          const departmentIds: any = title?.departmentIds ?? [];
          const departmentId = title?.departmentId;
          const findDepartment = departmentIds.filter(
            (dep) => dep != departmentId,
          );
          findDepartment.push(departmentId);
          await this.crmTitleRepo.updateRowData(
            { departmentIds: findDepartment },
            title.id,
          );
        } catch (error) {}
      }
      return titleData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateDisposition(reqData) {
    try {
      // Params validation
      const targetList = reqData.targetList;
      if (!targetList) return kParamMissing('targetList');

      for (let index = 0; index < targetList.length; index++) {
        try {
          const targetData = targetList[index];
          const dispositionId = targetData.dispositionId;
          if (!dispositionId) continue;

          await this.dispositionRepo.updateRowData(targetData, dispositionId);
        } catch (error) {}
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getCrmTitleByAdmin(query) {
    try {
      const adminId = +query?.adminId;
      if (!adminId) return kParamMissing('adminId');
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const { id, fullName, departmentId } =
        await this.commonShared.getAdminData(adminId);
      const adminData: any = { id, fullName, departmentId };
      if (adminData === k500Error) return kInternalError;
      if (!adminData) return kNoDataFound;

      const depData = await this.commonShared.getDepartment(departmentId);

      const department = depData?.department;

      adminData.departmentData = {
        id: departmentId,
        department,
      };

      const loanData = await this.loanRepository.getRowWhereData(['id'], {
        where: { userId: userId, loanStatus: 'Active' },
      });
      if (loanData === k500Error) return kInternalError;
      query.loanId = loanData?.id;
      const type: any = await this.getCrmActivityType(
        department,
        query?.loanId,
      );
      if (type?.message) return type;
      query.departmentId = departmentId;
      const reasons: any = await this.getCrmReasons(query);

      if (reasons?.message) return reasons;
      if (reasons?.length) adminData.reasonData = reasons;

      if (department != COLLECTION) {
        const activities = await this.commonShared.getCRMTitles(
          query?.departmentId,
        );
        adminData.activities = activities;
      }
      adminData.type = type;
      let status: any = [];
      if (department == SUPPORT) status = [{ id: '0', status: 'In Process' }];
      else if (department == COLLECTION) {
        status = await this.commonShared.getCRMStatus();
      } else
        status = [
          { id: '0', status: 'In Process' },
          { id: '3', status: 'pending' },
        ];
      adminData.status = status;
      return adminData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getCrmActivityType(department, loanId = null) {
    try {
      let type = '0';
      if (loanId) {
        const today = this.typeService.getGlobalDate(new Date());
        const emiData = await this.emiRepo.getTableWhereData(
          ['emi_date', 'payment_status', 'payment_due_status'],
          { where: { loanId } },
        );
        if (emiData === k500Error) return kInternalError;
        for (let i = 0; i < emiData.length; i++) {
          try {
            const ele = emiData[i];
            const emiDate = new Date(ele?.emi_date);
            if (
              emiDate.getTime() < today.getTime() &&
              ele?.payment_status == '0' &&
              ele?.payment_due_status == '1'
            ) {
              if (department == COLLECTION) type = '1';
              else type = '0';
              break;
            } else if (
              emiDate.getTime() == today.getTime() &&
              ele?.payment_status == '0'
            )
              type = '2';
            else if (
              emiDate.getTime() == today.getTime() &&
              ele?.payment_status == '1'
            )
              if (department == COLLECTION) type = '0';
              else type = '1';
            else type = '0';
          } catch (error) {}
        }
      }
      return { id: type, title: crmTypeTitle[type] };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateTemplateList(body) {
    try {
      const id = body?.id;
      if (!id) return kParamMissing('id');
      const opt = { where: { id } };
      let crmData: any = await this.dispositionRepo.getRowWhereData(
        ['id', 'templateList'],
        opt,
      );
      if (!crmData.templateList)
        return { message: 'Template list data is not found' };
      for (let i = 0; i < crmData.templateList.length; i++) {
        try {
          const ele = crmData.templateList[i];
          ele.headerImageHTML = ele.headerImageHTML.replace(
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/mediaImages%2F1687590469924.png',
            EnvConfig.gCloudAssets.crmHeaderV1,
          );
          ele.headerImageHTML = ele.headerImageHTML.replace(
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/mediaImages%2F1687758639516.jpeg',
            EnvConfig.gCloudAssets.crmHeaderV2,
          );

          ele.emailTemplatePath = ele.emailTemplatePath.replace(
            './upload/templates/crm/payment_reminder.html',
            `./upload/templateDesign${templateDesign}/templates/crm/payment_reminder.html`,
          );
          ele.emailContent = ele?.emailContent.replace(
            'Lenditt Loan Application',
            EnvConfig.nbfc.nbfcName,
          );
          ele.emailContent = ele?.emailContent.replace(
            'Lenditt',
            EnvConfig.nbfc.nbfcCodeNameS,
          );
          ele.notificationBody = ele?.notificationBody.replace(
            'Lenditt Loan Application',
            EnvConfig.nbfc.nbfcName,
          );
          ele.notificationBody = ele?.notificationBody.replace(
            'Lenditt',
            EnvConfig.nbfc.nbfcCodeNameS,
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '649328c5d6fc057e2b28d7a2',
            '65f1be9dd6fc05414f6f1192',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6493294dd6fc053e3e54bed2',
            '65f1be74d6fc05619e2a5db2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64932b97d6fc0544eb626392',
            '65f1be2fd6fc0518b17fecc2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64932d77d6fc052f6a78cd42',
            '65f1bd4cd6fc055aa47d1183',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '649326bbd6fc057c76119172',
            '65f1cae4d6fc0540cd588f62',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6493275dd6fc054649180c03',
            '65f1ca38d6fc054442633862',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '649327bad6fc053cb6496096',
            '65f1ca16d6fc053da017ca02',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '649188efd6fc050527050382',
            '65f1b618d6fc054736704bf2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6491884ad6fc0556a663bc93',
            '65f024b7d6fc0529d515d7d3',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6491b0f1d6fc051c546a57d3',
            '65f1cc10d6fc054c852063d2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6491b157d6fc052ce276e552',
            '65f1bc0cd6fc05753a3f9e52',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64928433d6fc053b765598a3',
            '65f1b53cd6fc0542193166d3',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '6491b0f1d6fc051c546a57d3',
            '65f1cc10d6fc054c852063d2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64928433d6fc053b765598a3',
            '65f1b53cd6fc0542193166d3',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64928881d6fc0522e75b2b43',
            '65f1bd2bd6fc053e2575aa02',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '649325e4d6fc056ab21d2e34',
            '65f02551d6fc0540fc7d97d2',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64932caed6fc056aee6a1162',
            '65f1cb08d6fc05388a655643',
          );
          ele.smsTemplateId = ele?.smsTemplateId.replace(
            '64932c5dd6fc0564c3238983',
            '65f1cb58d6fc05592f105091',
          );
        } catch (error) {}
      }
      const updateData = {
        templateList: crmData.templateList,
      };
      await this.dispositionRepo.updateRowData(updateData, id);
      return crmData;
    } catch (error) {}
  }
}

// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class ComplianceSubCategoryEntity extends Model<ComplianceSubCategoryEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '[true: Yes, false: No (default)]',
  })
  is_active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '[true: Yes, false: No (default)]',
  })
  always_on_time_submission: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '[true: Yes, false: No (default)]',
  })
  is_next_year: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment:
      '1: Upcoming reminder, 2: Overdue reminder, 3: Both (Upcoming and overdue reminder)',
  })
  remind_for: number;

  @Column({
    type: DataType.SMALLINT,
  })
  remind_admin_before_days: number;

  @Column({
    type: DataType.SMALLINT,
  })
  remind_director_before_days: number;

  @Column({
    type: DataType.SMALLINT,
  })
  dependent_task_reminder_days: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  category_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  updated_by: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  department_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  task_depends_on: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Day(s) for particular month(s)',
  })
  day_of_month: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'The month for which the checkbox is shown.',
  })
  month_of_year: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'The month till which admin can complete compliance task.',
  })
  due_month_of_year: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  maker: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  checker: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_made: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_accepted: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_rejected: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_admin_upcoming: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_admin_overdue: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_director_upcoming: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: 'Array of admin id(s)',
  })
  notify_director_overdue: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  category_name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  sub_category_name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  sub_category_description: string;
}

// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { GLOBAL_FLOW, gIsPROD } from 'src/constants/globals';
import { Op } from 'sequelize';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kBadRequest,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import {
  kCAMS,
  kCompleted,
  kFullPay,
  kNoDataFound,
  kNotEligibleForNBFC,
  kfinvu,
  tAadhaar,
  tApproval,
  tDisbursement,
  tEMandate,
  tESign,
  tEmployment,
  tInProgress,
  tLoanAccept,
  tNetbanking,
  tPan,
  tPending,
  tRegistration,
  tRejected,
  tSuccess,
} from 'src/constants/strings';
import { admin } from 'src/entities/admin.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { mandateEntity } from 'src/entities/mandate.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { InsuranceRepository } from 'src/repositories/insurance.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import { QualityParameterService } from '../qualityParameter/qualityParameter.service';
import { CommonSharedService } from 'src/shared/common.shared.service';
import {
  kGetCashfreePayment,
  kGetRazorpayPayment,
  kGetSigndeskPayment,
} from 'src/constants/network';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { EmiSharedService } from 'src/shared/emi.service';
import { EMIRepository } from 'src/repositories/emi.repository';
import { FileService } from 'src/utils/file.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { LoanDetails } from 'src/entities/loan_details.entity';
import { WaiverEntity } from 'src/entities/waiver.entity';
import { DateService } from 'src/utils/date.service';
import { COMMON_STATUS, LOAN_STATUS } from 'src/constants/objects';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { LoanTimelineDto } from './loan.dto';
import { UserRepository } from 'src/repositories/user.repository';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { SubscriptionRepository } from 'src/repositories/subscription.repository';
import { MandateRepository } from 'src/repositories/mandate.repository';
import { ESignRepository } from 'src/repositories/esign.repository';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { BankingRepository } from 'src/repositories/banking.repository';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Injectable()
export class LoanService {
  constructor(
    private readonly cryptService: CryptService,
    private readonly elephantService: ElephantService,
    private readonly repository: LoanRepository,
    private readonly emiRepo: EMIRepository,
    private readonly typeService: TypeService,
    private readonly insuranceRepo: InsuranceRepository,
    private readonly fileService: FileService,
    private readonly dateService: DateService,
    // Repositories
    private readonly masterRepo: MasterRepository,
    private readonly userRepo: UserRepository,
    private readonly employmentRepo: EmploymentRepository,
    private readonly salarySlipRepo: SalarySlipRepository,
    private readonly workMailRepo: WorkMailRepository,
    private readonly loanRepo: LoanRepository,
    private readonly subscriptionRepo: SubscriptionRepository,
    private readonly mandateRepo: MandateRepository,
    private readonly eSignRepo: ESignRepository,
    private readonly disbursmentRepo: DisbursmentRepository,
    private readonly bankingRepo: BankingRepository,
    private readonly kycRepo: KYCRepository,
    @Inject(forwardRef(() => QualityParameterService))
    private readonly qualityParameterService: QualityParameterService,
    private readonly commonService: CommonSharedService,
    @Inject(forwardRef(() => CalculationSharedService))
    private readonly sharedCalculation: CalculationSharedService,
    @Inject(forwardRef(() => EmiSharedService))
    private readonly emiSharedService: EmiSharedService,
    private readonly repo: RepositoryManager,
    private readonly redisService: RedisService,
    private readonly transRepo: TransactionRepository,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  async addAccIdToMissingOne(loanId: number) {
    const attributes = ['id'];
    const order = [['loan_disbursement_date', 'ASC']];
    const where: any = {
      accId: { [Op.eq]: null },
    };
    if (loanId) where.id = loanId;
    else where.loan_disbursement_date = { [Op.ne]: null };
    const options: any = { order, where };

    const loanList = await this.loanRepo.getTableWhereData(attributes, options);
    if (loanList == k500Error) throw new Error();

    let accNumber = await this.getNextAccId();

    for (let index = 0; index < loanList.length; index++) {
      try {
        const loanData = loanList[index];
        const updatedData = { accId: accNumber };
        const loanId = loanData.id;

        const updatedLoanData = await this.loanRepo.updateRowData(
          updatedData,
          loanId,
        );
        if (updatedLoanData != k500Error) accNumber++;
      } catch (error) {}
    }
  }

  private async getNextAccId() {
    const attributes = ['accId'];
    const where = { accId: { [Op.ne]: null } };
    const order = [['accId', 'DESC']];
    const options = { order, where };
    const loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (loanData == k500Error) throw new Error();
    else if (!loanData) return 1;
    else return loanData.accId + 1;
  }

  async insuranceProposal(reqData) {
    try {
      const loanId = reqData.loanId;
      const isUpdate = reqData?.isUpdate ?? false;
      if (!loanId) return kParamMissing('loanId');
      // Joins
      const bankingInclude: any = { model: BankingEntity };
      bankingInclude.attributes = ['mandateAccount'];
      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = [
        'aadhaarAddress',
        'aadhaarAddressResponse',
        'aadhaarDOB',
        'aadhaarResponse',
      ];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = [
        'email',
        'gender',
        'isBlacklist',
        'fullName',
        'phone',
      ];
      userInclude.include = [kycInclude];
      const eSignInclude: any = { model: esignEntity };
      eSignInclude.attributes = ['status'];
      const include = [bankingInclude, eSignInclude, userInclude];

      const attributes = [
        'id',
        'userId',
        'approvedDuration',
        'insuranceDetails',
        'loan_disbursement_date',
        'manualVerification',
        'netApprovedAmount',
        'nomineeDetail',
        'loanStatus',
        'insuranceOptValue',
      ];
      const options = { include, where: { id: loanId } };
      let loanData: any;
      if (isUpdate)
        loanData = await this.repository.getTableWhereData(attributes, options);
      else
        loanData = await this.repository.getRowWhereData(attributes, options);

      if (loanData == k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);

      if (!isUpdate) {
        if (
          loanData.loanStatus != 'Accepted' &&
          loanData.loanStatus != 'Active'
        )
          return k422ErrorMessage('Insurance procedure failed !');
        if (loanData.eSignData?.status != '1')
          return k422ErrorMessage('eSign process is yet to complete');
        if (!loanData.loan_disbursement_date && gIsPROD)
          return k422ErrorMessage('Disbursement yet to initiate');
        if (loanData.registeredUsers.isBlacklist == '1')
          return k422ErrorMessage(kNotEligibleForNBFC);
      }

      if (isUpdate) return await this.updateInsuranceData(loanData);

      // create data in
      let response: any = {};
      if (loanData?.insuranceOptValue == true) {
        const createData = { userId: loanData.userId, loanId, status: -1 };
        const result = await this.insuranceRepo.create(createData);
        if (!result || result === k500Error) return kInternalError;

        // update id
        await this.repository.updateRowData({ insuranceId: result.id }, loanId);

        loanData.id = loanId;
        const preparedData: any = await this.prepareDataForInsuranceProposal(
          loanData,
        );
        if (preparedData?.message) return preparedData;
        if (preparedData.pincode == '-')
          preparedData.pincode = await this.getPinCode(
            loanData.registeredUsers,
          );
        response = await this.elephantService.initiateProposal(preparedData);
        if (response?.message) return response;
        const update = { ...response };
        await this.insuranceRepo.updateRowData(update, result.id);
        const key = `${loanData.userId}_USER_PROFILE`;
        await this.redisService.del(key);
        const insuranceRedisKey = 'INSURANCE_DATA_' + loanId;
        await this.redisService.del(insuranceRedisKey);
      }
      return response;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateInsuranceData(loanData) {
    const responseData: any = [];
    for (let i = 0; i < loanData.length; i++) {
      const ele = loanData[i];
      try {
        if (ele?.insuranceOptValue == true) {
          const preparedData: any = await this.prepareDataForInsuranceProposal(
            ele,
          );
          if (preparedData?.message) return preparedData;
          if (preparedData.pincode == '-')
            preparedData.pincode = await this.getPinCode(ele?.registeredUsers);
          let response = await this.elephantService.initiateProposal(
            preparedData,
          );
          if (response?.message) return response;
          const update = { ...response };
          const up = await this.insuranceRepo.updateRowWhereData(update, {
            where: {
              loanId: ele.id,
              status: 2,
            },
          });
          console.log({ response, up });
          responseData.push(response);
          const key = `${ele.userId}_USER_PROFILE`;
          await this.redisService.del(key);
          const insuranceRedisKey = 'INSURANCE_DATA_' + ele.id;
          await this.redisService.del(insuranceRedisKey);
        }
      } catch (error) {}
    }
    return responseData;
  }

  private async prepareDataForInsuranceProposal(loanData) {
    try {
      const bankingData = loanData.bankingData ?? {};
      const userData = loanData.registeredUsers ?? {};
      const kycData = userData.kycData ?? {};
      let pincode = '-';
      try {
        if (kycData?.aadhaarResponse) {
          const response = JSON.parse(kycData?.aadhaarResponse);
          pincode = response?.zip ?? response?.pincode ?? '-';
          if (pincode == '-' && response['addressDetails']) {
            try {
              let address = response['addressDetails'];
              if (typeof address == 'string') address = JSON.parse(address);
              pincode = address['pc'] ?? '-';
            } catch (error) {}
          }
        }
      } catch (error) {}
      const data = {
        name: userData.fullName ?? '',
        phone: this.cryptService.decryptPhone(userData.phone),
        email: userData.email,
        dob: await this.typeService.getDateAsPerAadhaarDOB(kycData.aadhaarDOB),
        gender: userData.gender,
        bankAccNumber: bankingData.mandateAccount,
        aadhaarAddress: this.typeService.getAadhaarAddress(kycData).address,
        approvedAmount: +loanData.netApprovedAmount,
        loanTenure: loanData.approvedDuration,
        loanId: loanData.id,
        pincode: pincode,
        disbursementDate: loanData.loan_disbursement_date,
        insuranceDetails: loanData?.insuranceDetails ?? {},
        nomineeDetails: loanData?.nomineeDetail ?? {},
      };

      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get pincode
  private async getPinCode(userData) {
    try {
      const kycData = userData.kycData ?? {};
      const address = JSON.parse(kycData?.aadhaarAddress ?? '') ?? {};
      return await this.typeService.findPinCode(address);
    } catch (error) {}
    return '-';
  }
  //#endregion

  // old query - backup of old function incase of production error
  async findUserDataForTracking(userId, loanId) {
    const adminInclude = { model: admin, attributes: ['fullName'] };
    // Subscription model
    const subAttributes = [
      'status',
      'mode',
      'response',
      'subType',
      'updatedAt',
    ];
    const subscriptionInclude = {
      model: SubScriptionEntity,
      attributes: subAttributes,
    };
    // Mandate model
    const mandateAtt = ['actual_status', 'updatedAt'];
    const mandateInclude = { model: mandateEntity, attributes: mandateAtt };
    // Esign and disbursemenst model
    const edAtt = ['status', 'updatedAt'];
    const esignInclude = { model: esignEntity, attributes: edAtt };
    const disbursementInclude = {
      model: disbursementEntity,
      attributes: edAtt,
    };
    const bankingAtt = [
      'salaryVerification',
      'salaryVerificationDate',
      'dataOfMonth',
      'skipStmtAdmin',
    ];
    const bankingInclude: any = {
      required: false,
      model: BankingEntity,
      attributes: bankingAtt,
      include: [{ ...adminInclude, as: 'netApproveByData' }],
    };
    // Loan model
    const loanInc: any = [
      subscriptionInclude,
      mandateInclude,
      esignInclude,
      disbursementInclude,
      bankingInclude,
      { ...adminInclude, as: 'adminData' },
    ];
    const loanAtt = [
      'manualVerification',
      'id',
      'remark',
      'loanRejectReason',
      'userReasonDecline',
      'declineId',
    ];
    const loanInclude: any = {
      required: false,
      model: loanTransaction,
      attributes: loanAtt,
      include: loanInc,
    };
    // Work mail model
    const workMailAttr = [
      'approveById',
      'status',
      'rejectReason',
      'verifiedDate',
    ];
    const workMailInclude: any = {
      required: false,
      model: WorkMailEntity,
      attributes: workMailAttr,
    };
    // Salary slip model
    const salarySlipAttr = [
      'approveById',
      'status',
      'rejectReason',
      'salaryVerifiedDate',
    ];
    const salarySlipInclude: any = {
      required: false,
      model: SalarySlipEntity,
      attributes: salarySlipAttr,
    };
    salarySlipInclude.include = [{ ...adminInclude, as: 'admin' }];
    // Company details model
    const empAtt = ['companyVerification', 'rejectReason', 'createdAt'];
    const empInclude: any = {
      model: employmentDetails,
      required: false,
      attributes: empAtt,
    };
    const kycMo = {
      required: false,
      model: KYCEntity,
      attributes: ['id'],
      include: [
        { ...adminInclude, as: 'panVerifiedAdminData' },
        { ...adminInclude, as: 'aadhaarVerifiedAdminData' },
      ],
    };
    const userAttributes = ['id', 'fullName'];
    const userInclude = {
      model: registeredUsers,
      attributes: userAttributes,
      include: [
        kycMo,
        { ...adminInclude, as: 'residenceAdminData' },
        { ...adminInclude, as: 'contactAdminData' },
      ],
    };
    const masterWhere: any = {};
    if (loanId) masterWhere.loanId = loanId;
    else if (userId) masterWhere.userId = userId;
    const masterOptions = {
      useMaster: false,
      where: masterWhere,
      include: [
        userInclude,
        empInclude,
        salarySlipInclude,
        workMailInclude,
        loanInclude,
      ],
    };

    const attributes = [
      'id',
      'status',
      'rejection',
      'dates',
      'loanId',
      'salarySlipId',
      'workMailId',
      'empId',
      'kfsStatus',
      'kfsAcceptDate',
    ];
    const masterData = await this.masterRepo.getRowWhereData(
      attributes,
      masterOptions,
    );
    if (!masterData || masterData === k500Error) throw new Error();
    return masterData;
  }

  // old query - backup of old function incase of production error
  async prepareTrackingData(data) {
    try {
      const masterData = data;
      const userData = data.userData;
      const kycData = userData.kycData;
      const registration = this.getRegistrationTracking(masterData);
      const employement = await this.getEmployementTracking(masterData);
      const netBanking = this.getNetbankingTracking(masterData);
      const aadhaarTracking = this.getAadhaarTracking(masterData, kycData);
      const panTracking = this.getPanTracking(masterData, kycData);
      const finalVerification = this.getFinalVerification(masterData);
      const loanAccept = this.getLoanAccept(masterData);
      const emandateVerification = this.getEmandateVerification(masterData);
      const esingVerification = this.getEsignVerification(masterData);
      const disbursementTrack = this.getDisburesementTracking(masterData);

      const trackingList = [
        registration,
        aadhaarTracking,
        employement,
        netBanking,
        panTracking,
        finalVerification,
        emandateVerification,
        esingVerification,
        disbursementTrack,
      ];

      // Dynamic loanAccept
      if (loanAccept.kfsStatus) {
        delete loanAccept.kfsStatus;
        trackingList.push(loanAccept);
      }

      trackingList.sort((a, b) => a.orderBy - b.orderBy);
      return this.prepareNextUnderVerification(trackingList);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private getRegistrationTracking(masterData) {
    let tracking: any = {};
    tracking.title = tRegistration;
    tracking.orderBy = 0;
    try {
      const statusData = masterData?.status;
      const dates = masterData?.dates;
      const basic = statusData?.basic ?? -1;
      const phone = statusData?.phone ?? -1;
      let status =
        basic == '1' && phone == '1'
          ? '1'
          : basic == '-1' || phone == '-1'
          ? '-1'
          : '0';
      tracking.approveBy = 'system';
      tracking.date = dates?.registration
        ? new Date(dates.registration).toJSON()
        : 0;

      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private async getEmployementTracking(masterData) {
    let tracking: any = {};
    tracking.title = tEmployment;
    tracking.orderBy = 2;
    try {
      const statusData = masterData?.status ?? {};
      const company = statusData?.company ?? -1;
      const salarySlip = statusData?.salarySlip ?? -1;
      const workMail = statusData?.workMail ?? -1;
      const loan = statusData?.loan ?? -1;
      const dates = masterData?.dates;
      const rejection = masterData?.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const approvedStatus = [1, 3, 4];
      const salarySlipData = masterData?.salarySlipData ?? {};
      const salarySlipAdmin = (
        await this.commonService.getAdminData(salarySlipData.approveById)
      ).fullName
        ?.trim()
        ?.toLowerCase();
      const workMailData = masterData?.workMailData ?? {};
      const workMailAdmin = (
        await this.commonService.getAdminData(workMailData.approveById)
      ).fullName
        ?.trim()
        ?.toLowerCase();

      // Employment approved by
      tracking.approveBy = declineId ? 'User' : 'system';
      if (workMailAdmin && workMailAdmin != 'system')
        tracking.approveBy = workMailAdmin;
      else if (salarySlipAdmin && salarySlipAdmin != 'system')
        tracking.approveBy = salarySlipAdmin;

      let status =
        approvedStatus.includes(company) &&
        approvedStatus.includes(salarySlip) &&
        approvedStatus.includes(workMail)
          ? '1'
          : company == 2 || workMail == 2 || salarySlip == 2
          ? '2'
          : company == -1 || workMail == -1 || salarySlip == -1
          ? '-1'
          : '0';

      tracking.date = dates?.employment
        ? new Date(dates.employment).toJSON()
        : 0;
      tracking.message = workMail == 4 ? 'SKIPPED' : '';
      if (loan == 7) status = '1';
      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
      else if (status == '2') {
        tracking.status = tRejected;
        tracking.message =
          rejection.company ??
          rejection?.salarySlip ??
          rejection?.workMail ??
          '';
      } else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getNetbankingTracking(masterData) {
    let tracking: any = {};
    tracking.title = tNetbanking;
    tracking.orderBy = 2;

    const statusData = masterData?.status ?? {};
    const rejection = masterData?.rejection;
    const loanData = masterData?.loanData;
    const bankingData = loanData?.bankingData;
    const bank = statusData?.bank ?? -1;
    const dates = masterData.dates;
    const declineId = loanData?.declineId;
    const approvedStatus = [1, 3];
    const status = approvedStatus.includes(bank)
      ? '1'
      : bank == '2'
      ? '2'
      : bank == '-1'
      ? '-1'
      : '0';

    tracking.approveBy = declineId
      ? 'User'
      : bankingData?.netApproveByData?.fullName ?? '';
    tracking.date = dates?.banking ? new Date(dates?.banking).toJSON() : 0;
    if (status == '1') tracking.status = tSuccess;
    else if (status == '2') {
      tracking.status = tRejected;
      tracking.message = rejection?.banking ?? '';
    } else if (status == '-1') tracking.status = tPending;
    else tracking.status = tInProgress;

    return tracking;
  }

  private getAadhaarTracking(masterData, kycData) {
    let tracking: any = {};
    tracking.title = tAadhaar;
    tracking.orderBy = 1;
    try {
      const statusData = masterData?.status ?? {};
      const aadhaar = statusData?.aadhaar ?? -1;
      const dates = masterData.dates;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const approvedStatus = [1, 3];
      const approvedBy = declineId
        ? 'User'
        : kycData?.aadhaarVerifiedAdminData?.fullName;
      tracking.approveBy = approvedBy;

      const status = approvedStatus.includes(aadhaar)
        ? '1'
        : aadhaar == 2
        ? '2'
        : aadhaar == -1
        ? '-1'
        : '0';
      tracking.date = dates?.aadhaar ? new Date(dates?.aadhaar).toJSON() : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '2') {
        tracking.message = rejection?.aadhaar;
        tracking.status = tRejected;
      } else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getPanTracking(masterData, kycData) {
    let tracking: any = {};
    tracking.title = tPan;
    tracking.orderBy = 5;
    try {
      const statusData = masterData?.status ?? {};
      const pan = statusData?.pan ?? -1;
      const dates = masterData.dates;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const approvedStatus = [1, 3];
      const approvedBy = declineId
        ? 'User'
        : kycData?.panVerifiedAdminData?.fullName ?? 'system';
      tracking.approveBy = approvedBy;

      const status = approvedStatus.includes(pan)
        ? '1'
        : pan == 2
        ? '2'
        : pan == -1
        ? '-1'
        : '0';
      tracking.date = dates?.pan ? new Date(dates?.pan).toJSON() : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '2') {
        tracking.message = rejection.pan;
        tracking.status = tRejected;
      } else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getFinalVerification(masterData) {
    let tracking: any = {};
    tracking.title = tApproval;
    tracking.orderBy = 6;
    try {
      const statusData = masterData?.status ?? {};
      const eligibility = statusData?.eligibility ?? -1;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const adminData = loanData?.adminData;
      const dates = masterData.dates;
      const approvedStatus = [1, 3];
      tracking.approveBy = declineId ? 'User' : adminData?.fullName ?? '';
      const status = approvedStatus.includes(eligibility)
        ? '1'
        : eligibility == '2'
        ? '2'
        : eligibility == '-1' || eligibility == '-2' || eligibility == '5'
        ? '-1'
        : '0';
      tracking.date = dates?.eligibility
        ? new Date(dates?.eligibility).toJSON()
        : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '2') {
        tracking.status = tRejected;
        tracking.message =
          loanData?.userReasonDecline ??
          loanData?.remark ??
          loanData?.loanRejectReason ??
          rejection?.eligiblity ??
          rejection?.loan;
      } else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getLoanAccept(masterData) {
    let tracking: any = {};
    tracking.title = tLoanAccept;
    tracking.orderBy = 7;
    try {
      const kfsStatus = masterData?.kfsStatus;
      const kfsAcceptDate = masterData.kfsAcceptDate;
      const approvedStatus = [1, 3];
      tracking.approveBy = 'system';
      tracking.kfsStatus = kfsStatus;
      const status = approvedStatus.includes(kfsStatus) ? '1' : '-1';
      tracking.date = kfsAcceptDate ? new Date(kfsAcceptDate).toJSON() : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private getEmandateVerification(masterData) {
    let tracking: any = {};
    tracking.title = tEMandate;
    tracking.orderBy = 8;
    try {
      const statusData = masterData?.status ?? {};
      const eMandate = statusData?.eMandate ?? -1;
      const dates = masterData.dates;
      const approvedStatus = [1, 3];
      tracking.approveBy = 'system';
      const status = approvedStatus.includes(eMandate)
        ? '1'
        : eMandate == '-1'
        ? '-1'
        : '0';
      tracking.date = dates?.eMandate
        ? new Date(dates?.eMandate ?? 0).toJSON()
        : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getEsignVerification(masterData) {
    let tracking: any = {};
    tracking.title = tESign;
    tracking.orderBy = 9;
    try {
      const statusData = masterData?.status ?? {};
      const eSign = statusData?.eSign ?? -1;
      const dates = masterData.dates;
      const approvedStatus = [1, 3];
      tracking.approveBy = 'system';
      const status = approvedStatus.includes(eSign)
        ? '1'
        : eSign == -1
        ? '-1'
        : '0';

      tracking.date = dates?.eSign ? new Date(dates.eSign).toJSON() : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private getDisburesementTracking(masterData) {
    let tracking: any = {};
    tracking.title = tDisbursement;
    tracking.orderBy = 10;
    try {
      const statusData = masterData?.status ?? {};
      const disbursement = statusData?.disbursement ?? -1;
      const dates = masterData.dates;
      const approvedStatus = [1, 3];
      tracking.approveBy = 'system';
      const status = approvedStatus.includes(disbursement)
        ? '1'
        : disbursement == -1
        ? '-1'
        : '0';
      tracking.date = dates?.disbursement
        ? new Date(dates?.disbursement).toJSON()
        : 0;
      if (status == '1') tracking.status = tSuccess;
      else if (status == '-1') tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  async trackerAndMissingMonth(reqData: LoanTimelineDto) {
    // Params validation
    const loanId = reqData.loanId;
    if (!loanId) return kParamMissing('loanId');
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');

    const isRefresh = reqData?.isRefresh ?? false;

    let response: any = {
      verificationTrackerData: [],
      isStatementMonthMissing: false,
      monthList: [],
    };

    // find data in redis
    if (!isRefresh) {
      const loan = await this.getTimeLineRedisDetails(loanId);
      if (loan?.verificationTrackerData?.length) return loan;
    }

    ///check redis data of CSE
    let redisCSEData = await this.redisService.get('CSE_REJECT_LOAN' + loanId);
    if (redisCSEData) redisCSEData = JSON.parse(redisCSEData);

    // Get data from database
    // const result = await this.findUserDataForTracking(userId, loanId); // old query - backup of old function incase of production error
    const result = await this.findUserDataForTrackingNew(
      userId,
      loanId,
      redisCSEData,
    ); // new breaked and improved query
    if (result === k500Error) throw new Error();

    const loanData = result?.loanData ?? {};
    const bankingData = loanData.bankingData ?? {};

    const loan_status = result?.loanData?.loanStatus ?? LOAN_STATUS.InProcess;

    const salaryVerification = bankingData.salaryVerification ?? '-1';
    if (
      bankingData.dataOfMonth &&
      bankingData?.dataOfMonth.includes('false') &&
      (salaryVerification == '4' ||
        (salaryVerification == '0' && !bankingData.skipStmtAdmin))
    ) {
      try {
        response.monthList = JSON.parse(bankingData.dataOfMonth);
        response.isStatementMonthMissing = true;
      } catch (error) {}
    }

    // Preparation for tracking data
    // response.verificationTrackerData = await this.prepareTrackingData(result); // old query - backup of old function incase of production error
    response.verificationTrackerData = await this.prepareTrackingDataNew(
      result,
      redisCSEData,
    ); // new breaked and improved query

    /// manage data in redis if loan is completed, active or rejected
    this.manageLoanTimeLine(
      response,
      loanId,
      loan_status,
      result,
      redisCSEData,
    );

    return response;
  }

  //#region store data in redis
  private async manageLoanTimeLine(
    response,
    loanId,
    loan_status,
    result,
    redisCSEData,
  ) {
    if (
      ![
        LOAN_STATUS.Complete,
        LOAN_STATUS.Active,
        LOAN_STATUS.Rejected,
      ].includes(loan_status) ||
      (result?.loanData?.cseRejectBy &&
        redisCSEData &&
        result?.status?.disbursement < 0 &&
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW)
    )
      return;

    /// store data in redis
    await this.redisService.set(
      'TIMELINE_' + loanId,
      JSON.stringify(response),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
  }
  //#endregion

  //#region
  private async getTimeLineRedisDetails(loan_id) {
    // Temporary
    return null;

    const data = await this.redisService.get('TIMELINE_' + loan_id);
    if (data) return await JSON.parse(data);
  }
  //#endregion

  // new breaked and improved query
  // Gather all data in masterData
  async findUserDataForTrackingNew(userId, loanId, redisCSEData) {
    let masterData: any = {};
    masterData = await this.getMasterData(userId, loanId);
    masterData.userData = await this.getUserData(masterData);
    if (masterData.userData)
      masterData.userData.kycData = await this.getKycData(masterData);
    masterData.empData = await this.getEmploymentData(masterData);
    masterData.salarySlipData = await this.getSalarySlipData(masterData);
    masterData.workMailData = await this.getWorkMailData(masterData);
    masterData.loanData = await this.getLoanData(masterData, redisCSEData);
    let loanData = masterData.loanData;
    loanData.subscriptionData = await this.getSubscriptionData(masterData);
    loanData.mandateData = await this.getMandateData(masterData);
    loanData.eSignData = await this.getESignData(masterData);
    loanData.disbursementData = await this.getDisbursementData(masterData);
    loanData.bankingData = await this.getBankingData(masterData);
    delete masterData?.userId;
    delete masterData?.userData?.kycId;
    delete masterData?.userData?.kycData?.panVerifiedAdmin;
    delete masterData?.userData?.kycData?.aadhaarVerifiedAdmin;
    delete masterData?.loanData?.subscriptionId;
    delete masterData?.loanData?.bankingId;
    delete masterData?.loanData?.manualVerificationAcceptId;
    delete masterData?.loanData?.bankingData?.adminId;
    return masterData;
  }

  async getMasterData(userId, loanId) {
    if (!userId && !loanId) return null;
    const masterWhere: any = {};
    if (loanId) masterWhere.loanId = loanId;
    else if (userId) masterWhere.userId = userId;
    const options = {
      useMaster: false,
      where: masterWhere,
    };
    const attributes = [
      'id',
      'status',
      'rejection',
      'dates',
      'loanId',
      'salarySlipId',
      'workMailId',
      'empId',
      'kfsStatus',
      'kfsAcceptDate',
      'userId',
      'loanAcceptStatus',
    ];
    let masterData = await this.masterRepo.getRowWhereData(attributes, options);
    if (!masterData || masterData === k500Error) throw new Error();
    return masterData;
  }

  async getEmploymentData(masterData) {
    const empId = masterData?.empId;
    if (!empId) return null;
    const attributes = ['companyVerification', 'rejectReason', 'createdAt'];
    const options = { where: { id: masterData?.empId } };
    let employmentData = await this.employmentRepo.getRowWhereData(
      attributes,
      options,
    );
    if (!employmentData) employmentData = null;
    if (employmentData === k500Error) throw new Error();
    return employmentData;
  }

  async getSalarySlipData(masterData) {
    const salarySlipId = masterData?.salarySlipId;
    if (!salarySlipId) return null;
    const attributes = [
      'approveById',
      'status',
      'rejectReason',
      'salaryVerifiedDate',
    ];
    const options = { where: { id: masterData?.salarySlipId } };
    let salarySlipData = await this.salarySlipRepo.getRowWhereData(
      attributes,
      options,
    );
    if (!salarySlipData) return null;
    const adminFullName = (
      await this.commonService.getAdminData(salarySlipData?.approveById)
    )?.fullName?.trim();
    salarySlipData.adminData = { fullName: adminFullName ?? null };
    if (salarySlipData === k500Error) throw new Error();
    return salarySlipData;
  }

  async getWorkMailData(masterData) {
    const workMailId = masterData?.workMailId;
    if (!workMailId) return null;
    const attributes = [
      'approveById',
      'status',
      'rejectReason',
      'verifiedDate',
    ];
    const options = { where: { id: masterData?.workMailId } };
    let workMailData = await this.workMailRepo.getRowWhereData(
      attributes,
      options,
    );
    if (!workMailData) workMailData = null;
    if (workMailData === k500Error) throw new Error();
    return workMailData;
  }

  async getLoanData(masterData, redisCSEData) {
    const loanId = masterData?.loanId;
    if (!loanId) return null;
    const attributes = [
      'manualVerification',
      'id',
      'remark',
      'loanRejectReason',
      'userReasonDecline',
      'declineId',
      'subscriptionId',
      'bankingId',
      'manualVerificationAcceptId',
      'loanStatus',
      'cseRejectBy',
      'approvedReason',
      'hold_reason',
      'hold_by',
    ];
    const options = { where: { id: loanId } };
    let loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (!loanData) return null;
    const adminFullName = (
      await this.commonService.getAdminData(
        loanData?.manualVerificationAcceptId,
      )
    )?.fullName?.trim();
    loanData.adminData = { fullName: adminFullName ?? null };

    ///if cse reject loan due to user not responding at that time need to show loan as rejected to cse till disbursement is not initiated
    loanData.loanStatus =
      GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
      loanData?.cseRejectBy &&
      redisCSEData &&
      masterData?.status?.disbursement < 0
        ? LOAN_STATUS.Rejected
        : loanData?.loanStatus;
    if (loanData === k500Error) throw new Error();
    return loanData;
  }

  async getSubscriptionData(masterData) {
    const subscriptionId = masterData?.loanData?.subscriptionId;
    if (!subscriptionId) return null;
    const attributes = ['status', 'mode', 'response', 'subType', 'updatedAt'];
    const options = { where: { id: subscriptionId } };
    let subscriptionData = await this.subscriptionRepo.getRowWhereData(
      attributes,
      options,
    );
    if (!subscriptionData) return null;
    if (subscriptionData === k500Error) throw new Error();
    return subscriptionData;
  }

  async getMandateData(masterData) {
    const loanId = masterData?.loanData?.id;
    if (!loanId) return [];
    const attributes = ['actual_status', 'updatedAt'];
    const options = { where: { loanId: loanId } };
    let mandateData = await this.mandateRepo.getTableWhereData(
      attributes,
      options,
    );
    if (mandateData === k500Error) throw new Error();
    return mandateData;
  }

  async getESignData(masterData) {
    const loanId = masterData?.loanData?.id;
    if (!loanId) return null;
    const attributes = ['status', 'updatedAt'];
    const options = {
      where: { loanId: masterData?.loanData?.id },
      order: [['createdAt', 'DESC']],
    };
    let eSignData = await this.eSignRepo.getRowWhereData(attributes, options);
    if (!eSignData) return null;
    if (eSignData === k500Error) throw new Error();
    return eSignData;
  }

  async getDisbursementData(masterData) {
    const loanId = masterData?.loanData?.id;
    if (!loanId) return [];
    const attributes = ['status', 'updatedAt'];
    const options = { where: { loanId: masterData?.loanData?.id } };
    let disbursementData = await this.disbursmentRepo.getTableWhereData(
      attributes,
      options,
    );
    if (disbursementData === k500Error) throw new Error();
    return disbursementData;
  }

  async getBankingData(masterData) {
    const bankingId = masterData?.loanData?.bankingId;
    if (!bankingId) return null;
    const attributes = [
      'salaryVerification',
      'salaryVerificationDate',
      'dataOfMonth',
      'skipStmtAdmin',
      'adminId',
      'loanId',
      'otherDetails',
    ];
    const options = { where: { id: bankingId } };
    let bankingData = await this.bankingRepo.getRowWhereData(
      attributes,
      options,
    );
    if (!bankingData) return null;
    const adminFullName = (
      await this.commonService.getAdminData(bankingData?.adminId)
    )?.fullName?.trim();
    bankingData.netApproveByData = { fullName: adminFullName ?? null };
    if (bankingData === k500Error) throw new Error();
    return bankingData;
  }

  async getUserData(masterData) {
    const masterUserId = masterData?.userId;
    if (!masterUserId) return null;
    const attributes = ['id', 'fullName', 'kycId'];
    const options = { where: { id: masterUserId } };
    let userData = await this.userRepo.getRowWhereData(attributes, options);
    if (!userData) return null;
    const residenceAdminFullName = (
      await this.commonService.getAdminData(userData?.residenceAdminId)
    )?.fullName?.trim();
    if (userData === k500Error) throw new Error();
    return userData;
  }

  async getKycData(masterData) {
    const kycId = masterData?.userData?.kycId;
    if (!kycId) return null;
    const attributes = ['id', 'panVerifiedAdmin', 'aadhaarVerifiedAdmin'];
    const options = { where: { id: masterData?.userData?.kycId } };
    let kycData = await this.kycRepo.getRowWhereData(attributes, options);
    if (!kycData) return null;
    const panVerifiedAdminFullName = (
      await this.commonService.getAdminData(kycData?.panVerifiedAdmin)
    )?.fullName?.trim();
    kycData.panVerifiedAdminData = {
      fullName: panVerifiedAdminFullName ?? null,
    };
    const aadhaarVerifiedAdminFullName = (
      await this.commonService.getAdminData(kycData?.aadhaarVerifiedAdmin)
    )?.fullName?.trim();
    kycData.aadhaarVerifiedAdminData = {
      fullName: aadhaarVerifiedAdminFullName ?? null,
    };
    if (kycData === k500Error) throw new Error();
    return kycData;
  }

  // new breaked and improved query
  private async prepareTrackingDataNew(data, redisCSEData) {
    try {
      const masterData = data;
      const registration = this.getRegistrationTrackingNew(masterData);
      const employement = await this.getEmployementTrackingNew(masterData);
      const netBanking = await this.getNetbankingTrackingNew(masterData);
      const aadhaarTracking = await this.getAadhaarTrackingNew(masterData);
      const panTracking = await this.getPanTrackingNew(masterData);
      const finalVerification = await this.getFinalVerificationNew(masterData);
      const loanAccept = await this.getLoanAcceptNew(masterData, redisCSEData);
      const emandateVerification = this.getEmandateVerificationNew(masterData);
      const esingVerification = this.getEsignVerificationNew(masterData);
      const disbursementTrack = this.getDisburesementTrackingNew(masterData);

      const trackingList = [
        registration,
        aadhaarTracking,
        employement,
        netBanking,
        panTracking,
        finalVerification,
        emandateVerification,
        esingVerification,
        disbursementTrack,
      ];

      // Dynamic loanAccept
      if (loanAccept.kfsStatus) {
        delete loanAccept.kfsStatus;
        trackingList.push(loanAccept);
      }

      trackingList.sort((a, b) => a.orderBy - b.orderBy);
      return this.prepareNextUnderVerification(trackingList);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private getRegistrationTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tRegistration;
    tracking.orderBy = 0;
    try {
      const statusData = masterData?.status;
      const dates = masterData?.dates;
      const basic = statusData?.basic ?? COMMON_STATUS.userPending;
      const phone = statusData?.phone ?? COMMON_STATUS.userPending;
      let status =
        +basic == COMMON_STATUS.SystemApproved &&
        +phone == COMMON_STATUS.SystemApproved
          ? COMMON_STATUS.SystemApproved
          : +basic == COMMON_STATUS.userPending ||
            +phone == COMMON_STATUS.userPending
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.AdminPending;
      tracking.approveBy = 'system';
      tracking.date = dates?.registration
        ? new Date(dates.registration).toJSON()
        : 0;

      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.userPending) tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private async getEmployementTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tEmployment;
    tracking.orderBy = 2;
    try {
      const statusData = masterData?.status ?? {};
      const company = statusData?.company ?? COMMON_STATUS.userPending;
      const salarySlip = statusData?.salarySlip ?? COMMON_STATUS.userPending;
      const workMail = statusData?.workMail ?? COMMON_STATUS.userPending;
      const loan = statusData?.loan ?? COMMON_STATUS.userPending;
      const dates = masterData?.dates;
      const rejection = masterData?.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
        COMMON_STATUS.Skip,
      ];
      const salarySlipData = masterData?.salarySlipData ?? {};
      //check
      const salarySlipAdmin = (
        await this.commonService.getAdminData(salarySlipData.approveById)
      ).fullName
        ?.trim()
        ?.toLowerCase();
      const workMailData = masterData?.workMailData ?? {};
      const workMailAdmin = (
        await this.commonService.getAdminData(workMailData.approveById)
      ).fullName
        ?.trim()
        ?.toLowerCase();

      // Employment approved by
      tracking.approveBy = 'system';
      if (workMailAdmin && workMailAdmin != 'system')
        tracking.approveBy = workMailAdmin;
      else if (salarySlipAdmin && salarySlipAdmin != 'system')
        tracking.approveBy = salarySlipAdmin;

      let status =
        approvedStatus.includes(company) &&
        approvedStatus.includes(salarySlip) &&
        approvedStatus.includes(workMail)
          ? COMMON_STATUS.SystemApproved
          : company == COMMON_STATUS.Rejected ||
            workMail == COMMON_STATUS.Rejected ||
            salarySlip == COMMON_STATUS.Rejected
          ? COMMON_STATUS.Rejected
          : company == COMMON_STATUS.userPending ||
            workMail == COMMON_STATUS.userPending ||
            salarySlip == COMMON_STATUS.userPending
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.AdminPending;

      if (status == COMMON_STATUS.AdminPending) {
        tracking.approveBy = 'Admin';
      } else if (status == COMMON_STATUS.userPending) {
        tracking.approveBy = 'User';
      }

      tracking.date = dates?.employment
        ? new Date(dates.employment).toJSON()
        : 0;
      tracking.message = workMail == COMMON_STATUS.Skip ? 'SKIPPED' : '';
      if (loan == 7) status = COMMON_STATUS.SystemApproved;
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.userPending) tracking.status = tPending;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
        if (declineId) {
          tracking.approveBy = 'User';
        }
        tracking.message =
          loanData?.userReasonDecline ??
          rejection.company ??
          rejection?.salarySlip ??
          rejection?.workMail ??
          '';
      } else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private async getNetbankingTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tNetbanking;
    tracking.orderBy = 2;

    const statusData = masterData?.status ?? {};
    const rejection = masterData?.rejection;
    const loanData = masterData?.loanData;
    const bankingData = loanData?.bankingData;
    const bank = statusData?.bank ?? COMMON_STATUS.userPending;
    const dates = masterData.dates;
    const declineId = loanData?.declineId;
    const userReasonDecline = loanData?.userReasonDecline;
    const approvedStatus = [
      COMMON_STATUS.SystemApproved,
      COMMON_STATUS.ManualApproved,
    ];

    let status = COMMON_STATUS.AdminPending;

    // Bank Status = APPROVED (1) - System/Manual Approval or Skiped Banking Verification
    if (approvedStatus.includes(bank)) {
      status = COMMON_STATUS.SystemApproved;
      //  Bank Status = REJECTED (2) - Admin/System Rejection
    } else if (+bank == COMMON_STATUS.Rejected) {
      status =
        loanData?.loanStatus != 'Rejected'
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.Rejected;
    } else if (
      +bank == COMMON_STATUS.userPending ||
      +bank == COMMON_STATUS.Skip
    ) {
      status = COMMON_STATUS.userPending;
    }

    let approveBy =
      status === COMMON_STATUS.Rejected && declineId
        ? 'User'
        : bankingData?.netApproveByData?.fullName ?? 'System';
    tracking.approveBy = approveBy;

    if (status === COMMON_STATUS.Rejected && declineId) {
      tracking.date = dates?.banking
        ? new Date(dates.banking).toJSON()
        : bankingData?.salaryVerificationDate
        ? new Date(bankingData.salaryVerificationDate).toJSON()
        : '';
    } else {
      tracking.date = dates?.banking ? new Date(dates?.banking).toJSON() : 0;
    }

    if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
    else if (+status == COMMON_STATUS.Rejected) {
      tracking.status = tRejected;
      // User Declined 
      if (declineId) {
        tracking.approveBy = 'User';
        tracking.message = userReasonDecline ?? rejection?.banking ?? '';
      } else {
        // Admin Rejected and Message
        if (approveBy.toLowerCase() != 'system') {
          if (bankingData?.otherDetails?.salaryVerificationStatus == '3') {
            tracking.salaryApprovedBy = approveBy;
            tracking.approveBy =
              masterData?.loanData?.adminData?.fullName ?? '';
          }
        }
        tracking.message = rejection?.loan ?? '';
      }
      // User pending with Banking Rejection Message 
    } else if (+status == COMMON_STATUS.userPending) {
      tracking.status = tPending;
      if (+bank == COMMON_STATUS.Rejected) {
        tracking.message = rejection?.banking ?? '';
      }
    } else if (+bank == COMMON_STATUS.OnHold) {
      tracking.status = tInProgress;
      tracking.message = masterData?.loanData?.hold_reason ?? '';
      tracking.onHold = true;
      tracking.hold_by = (
        await this.commonService.getAdminData(loanData?.hold_by)
      )?.fullName;
    } else tracking.status = tInProgress;
    return tracking;
  }

  private async getAadhaarTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tAadhaar;
    tracking.orderBy = 1;
    try {
      const statusData = masterData?.status ?? {};
      const aadhaar = statusData?.aadhaar ?? COMMON_STATUS.userPending;
      const dates = masterData.dates;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];

      let aadhaarVerifiedAdminFullName =
        masterData?.userData?.kycData?.aadhaarVerifiedAdminData?.fullName;
      tracking.approveBy = aadhaarVerifiedAdminFullName ?? 'system';

      const status = approvedStatus.includes(aadhaar)
        ? COMMON_STATUS.SystemApproved
        : aadhaar == COMMON_STATUS.Rejected
        ? COMMON_STATUS.Rejected
        : aadhaar == COMMON_STATUS.userPending
        ? COMMON_STATUS.userPending
        : COMMON_STATUS.AdminPending;

      if (status == COMMON_STATUS.AdminPending) {
        tracking.approveBy = 'Admin';
      } else if (status == COMMON_STATUS.userPending) {
        tracking.approveBy = 'User';
      }

      tracking.date = dates?.aadhaar ? new Date(dates?.aadhaar).toJSON() : 0;
      if (status == COMMON_STATUS.Rejected) {
        tracking.approveBy = declineId
          ? 'User'
          : aadhaarVerifiedAdminFullName ?? 'system';
      }
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.message = rejection?.aadhaar;
        tracking.status = tRejected;
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private async getPanTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tPan;
    tracking.orderBy = 5;
    try {
      const statusData = masterData?.status ?? {};
      const pan = statusData?.pan ?? COMMON_STATUS.userPending;
      const dates = masterData.dates;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;
      const userReasonDecline = loanData?.userReasonDecline;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];

      const status = approvedStatus.includes(pan)
        ? COMMON_STATUS.SystemApproved
        : +pan == COMMON_STATUS.Rejected
        ? COMMON_STATUS.Rejected
        : +pan == COMMON_STATUS.userPending || +pan == COMMON_STATUS.WrongPan
        ? COMMON_STATUS.userPending
        : COMMON_STATUS.AdminPending;

      let panVerifiedAdminFullName =
        status === COMMON_STATUS.Rejected && declineId
          ? 'User'
          : masterData?.userData?.kycData?.panVerifiedAdminData?.fullName;
      const approvedBy = panVerifiedAdminFullName ?? 'system';
      tracking.approveBy = approvedBy;
      if (status == COMMON_STATUS.AdminPending) {
        tracking.approveBy = 'Admin';
      } else if (status == COMMON_STATUS.userPending) {
        tracking.approveBy = 'User';
      }

      if (status === COMMON_STATUS.Rejected && declineId) {
        tracking.date = dates?.pan ? new Date(dates.pan).toJSON() : '';
      } else {
        tracking.date = dates?.pan ? new Date(dates?.pan).toJSON() : 0;
      }

      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.message = userReasonDecline ?? rejection.pan;
        tracking.status = tRejected;
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  private async getFinalVerificationNew(masterData) {
    // If FV is rejected, show when only FV is rejected
    let tracking: any = {};
    tracking.title = tApproval;
    tracking.orderBy = 6;
    try {
      const statusData = masterData?.status ?? {};
      let eligibility = statusData?.eligibility ?? COMMON_STATUS.userPending;
      const loanAcceptStatus = masterData?.loanAcceptStatus;
      const rejection = masterData.rejection;
      const loanData = masterData?.loanData;
      const declineId = loanData?.declineId;

      const dates = masterData.dates;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];

      let loanApprovedAdminFullName = masterData?.loanData?.adminData?.fullName;
      if (approvedStatus.includes(eligibility)) {
        tracking.approveBy = loanApprovedAdminFullName
          ? loanApprovedAdminFullName
          : 'System';
      } else if (
        +eligibility === COMMON_STATUS.Rejected &&
        loanApprovedAdminFullName
      ) {
        tracking.approveBy = loanApprovedAdminFullName;
      } else if (+eligibility == COMMON_STATUS.OnHold) {
        tracking.status = tInProgress;
        tracking.message = masterData?.loanData?.hold_reason ?? '';
        tracking.onHold = true;
        tracking.hold_by = (
          await this.commonService.getAdminData(loanData?.hold_by)
        )?.fullName;
      } else {
        tracking.approveBy = 'System';
      }

      const rejectedKeys = [];
      for (const [key, value] of Object.entries(statusData)) {
        if (value === COMMON_STATUS.Rejected) rejectedKeys.push(key);
      }

      const preFVSteps = ['bank', 'pan'];
      const postFVSteps = ['eMandate', 'eSign', 'disbursement'];
      if (
        rejectedKeys.find((el) => postFVSteps.includes(el)) ||
        loanAcceptStatus == COMMON_STATUS.Rejected
      )
        eligibility = COMMON_STATUS.SystemApproved;
      else if (rejectedKeys.find((el) => preFVSteps.includes(el)))
        eligibility = COMMON_STATUS.userPending;
      if (loanAcceptStatus === COMMON_STATUS.Rejected) {
        eligibility = COMMON_STATUS.SystemApproved;
      }
      const status = approvedStatus.includes(eligibility)
        ? COMMON_STATUS.SystemApproved
        : +eligibility == COMMON_STATUS.Rejected
        ? COMMON_STATUS.Rejected
        : +eligibility == COMMON_STATUS.userPending ||
          eligibility == '-2' ||
          +eligibility == COMMON_STATUS.ThirdPartyPending
        ? COMMON_STATUS.userPending
        : COMMON_STATUS.AdminPending;

      if (status == COMMON_STATUS.Rejected && declineId) {
        tracking.approveBy = 'User';
      }

      tracking.date = dates?.eligibility
        ? new Date(dates?.eligibility).toJSON()
        : 0;
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
        tracking.message =
          loanData?.userReasonDecline ??
          loanData?.remark ??
          loanData?.loanRejectReason ??
          rejection?.eligiblity ??
          rejection?.loan;
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tInProgress;

      ///if loan status is approve then add approve reason
      if (tracking.status == tSuccess)
        tracking.remark = masterData?.loanData?.approvedReason;
    } catch (error) {}
    return tracking;
  }

  private async getLoanAcceptNew(masterData, redisCSEData) {
    let tracking: any = {};
    tracking.title = tLoanAccept;
    tracking.orderBy = 7;
    try {
      const kfsStatus = masterData?.kfsStatus;
      const kfsAcceptDate = masterData.kfsAcceptDate;
      const rejection = masterData?.rejection ?? {};
      const loanAcceptStatus = masterData?.loanAcceptStatus;
      const dates = masterData.dates;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];
      tracking.approveBy = 'system';
      tracking.kfsStatus = kfsStatus;
      let status;
      if (loanAcceptStatus === COMMON_STATUS.Rejected) {
        status = COMMON_STATUS.Rejected;
        tracking.approveBy = masterData?.loanData?.declineId
          ? 'User'
          : masterData?.loanData?.adminData?.fullName ?? 'system';
        tracking.date = dates?.loanAccept
          ? new Date(dates.loanAccept).toJSON()
          : '';
        tracking.message =
          rejection?.loanAccept ||
          masterData?.loanData?.userReasonDecline ||
          '';
      } else {
        status = approvedStatus.includes(kfsStatus)
          ? COMMON_STATUS.SystemApproved
          : COMMON_STATUS.userPending;
        if (approvedStatus.includes(kfsStatus)) {
          tracking.approveBy = 'system';
        }
        tracking.date = kfsAcceptDate ? new Date(kfsAcceptDate).toJSON() : 0;
      }
      if (
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
        redisCSEData &&
        masterData?.loanData?.cseRejectBy &&
        masterData?.status?.disbursement < 0
      )
        tracking.status = tRejected;
      else if (+status == COMMON_STATUS.SystemApproved)
        tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private getEmandateVerificationNew(masterData) {
    let tracking: any = {};
    tracking.title = tEMandate;
    tracking.orderBy = 8;
    try {
      const statusData = masterData?.status ?? {};
      const rejection = masterData?.rejection;
      const eMandate = statusData?.eMandate ?? COMMON_STATUS.userPending;
      const dates = masterData.dates;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];
      tracking.approveBy = 'system';
      let status;
      if (eMandate === COMMON_STATUS.Rejected) {
        status = COMMON_STATUS.Rejected;
        tracking.approveBy = masterData?.loanData?.declineId
          ? 'User'
          : masterData?.loanData?.adminData?.fullName ?? 'system';
        tracking.date = dates?.eMandate
          ? new Date(dates.eMandate).toJSON()
          : '';
        tracking.message =
          rejection?.eMandate || masterData?.loanData?.userReasonDecline || '';
      } else {
        status = approvedStatus.includes(eMandate)
          ? COMMON_STATUS.SystemApproved
          : +eMandate == COMMON_STATUS.Rejected
          ? COMMON_STATUS.Rejected
          : +eMandate == COMMON_STATUS.userPending
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.userPending;
        tracking.date = dates?.eMandate
          ? new Date(dates?.eMandate ?? 0).toJSON()
          : 0;
      }
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
        if (!tracking.message) {
          tracking.message =
            rejection?.eMandate ||
            masterData?.loanData?.userReasonDecline ||
            '';
        }
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private getEsignVerificationNew(masterData) {
    let tracking: any = {};
    tracking.title = tESign;
    tracking.orderBy = 9;
    try {
      const statusData = masterData?.status ?? {};
      const rejection = masterData?.rejection;
      const eSign = statusData?.eSign ?? COMMON_STATUS.userPending;
      const dates = masterData.dates;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];
      tracking.approveBy = 'system';
      let status;
      if (eSign === COMMON_STATUS.Rejected) {
        status = COMMON_STATUS.Rejected;
        tracking.approveBy = masterData?.loanData?.declineId
          ? 'User'
          : masterData?.loanData?.adminData?.fullName ?? 'system';
        tracking.date = dates?.eSign ? new Date(dates.eSign).toJSON() : '';
        tracking.message =
          rejection?.eSign || masterData?.loanData?.userReasonDecline || '';
      } else {
        status = approvedStatus.includes(eSign)
          ? COMMON_STATUS.SystemApproved
          : +eSign == COMMON_STATUS.Rejected
          ? COMMON_STATUS.Rejected
          : +eSign == COMMON_STATUS.userPending
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.userPending;
        tracking.date = dates?.eSign ? new Date(dates.eSign).toJSON() : 0;
      }
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
        if (!tracking.message) {
          tracking.message = rejection?.eSign || '';
        }
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tPending;
    } catch (error) {}
    return tracking;
  }

  private getDisburesementTrackingNew(masterData) {
    let tracking: any = {};
    tracking.title = tDisbursement;
    tracking.orderBy = 10;
    try {
      const statusData = masterData?.status ?? {};
      const rejection = masterData?.rejection;
      const disbursement =
        statusData?.disbursement ?? COMMON_STATUS.userPending;
      const dates = masterData.dates;
      const approvedStatus = [
        COMMON_STATUS.SystemApproved,
        COMMON_STATUS.ManualApproved,
      ];
      tracking.approveBy = 'system';
      let status;
      if (disbursement === COMMON_STATUS.Rejected) {
        status = COMMON_STATUS.Rejected;
        const adminName = masterData?.loanData?.adminData?.fullName;
        tracking.approveBy = adminName || 'System';

        tracking.date = dates?.disbursement
          ? new Date(dates.disbursement).toJSON()
          : '';
        tracking.message = rejection?.disbursement || '';
      } else {
        status = approvedStatus.includes(disbursement)
          ? COMMON_STATUS.SystemApproved
          : +disbursement == COMMON_STATUS.Rejected
          ? COMMON_STATUS.Rejected
          : +disbursement == COMMON_STATUS.userPending
          ? COMMON_STATUS.userPending
          : COMMON_STATUS.AdminPending;

        tracking.date = dates?.disbursement
          ? new Date(dates?.disbursement).toJSON()
          : 0;
      }
      if (+status == COMMON_STATUS.SystemApproved) tracking.status = tSuccess;
      else if (+status == COMMON_STATUS.Rejected) {
        tracking.status = tRejected;
        tracking.message = rejection?.disbursement ?? '';
      } else if (+status == COMMON_STATUS.userPending)
        tracking.status = tPending;
      else tracking.status = tInProgress;
    } catch (error) {}
    return tracking;
  }

  prepareNextUnderVerification(trackingData) {
    let finalTrack: any = [];
    for (let i = 0; i < trackingData.length; i++) {
      try {
        let trackData: any = trackingData[i];
        if (i > 0) {
          const previousOne = trackingData[i - 1];
          if (trackData.status != tRejected)
            if (
              previousOne.status == tInProgress ||
              previousOne.status == tPending ||
              previousOne.status == tRejected
            ) {
              trackData.status = tPending;
            }
        }
        finalTrack.push(trackData);
      } catch (error) {}
    }
    return finalTrack;
  }

  //#region update pending
  async updatePendingInsurance(query) {
    try {
      const list = await this.findPendingInsurance(query);
      if (list?.message) return list;
      for (let index = 0; index < list.length; index++) {
        try {
          const data = list[index];
          if (data) {
            const lanId = data?.lan_id;
            const userId = data?.userId;
            const loanId = data?.loanId;
            if (lanId) {
              const result = await this.elephantService.getCOI(lanId);
              if (result?.message) continue;
              if (result?.success === true) {
                try {
                  const update: any = {};
                  const url = (result?.url ?? '').trim();
                  const url1 = (result?.url1 ?? '').trim();
                  if (url) update.insuranceURL = url;
                  if (url1) update.insuranceURL1 = url1;
                  await this.insuranceRepo.updateRowData(update, data.id);
                  const key = `${userId}_USER_PROFILE`;
                  await this.redisService.del(key);
                  const insuranceRedisKey = 'INSURANCE_DATA_' + loanId;
                  await this.redisService.del(insuranceRedisKey);
                } catch (error) {}
              }
            }
          }
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region find pending insurance
  private async findPendingInsurance(query) {
    try {
      const options: any = {
        where: {
          [Op.or]: [
            { insuranceURL: { [Op.eq]: null } },
            { insuranceURL1: { [Op.eq]: null } },
          ],
        },
      };
      if (query?.loanId) options.where.loanId = query?.loanId;
      const att = ['id', 'lan_id', 'userId', 'loanId'];
      const result = await this.insuranceRepo.getTableWhereData(att, options);
      if (!result || result === k500Error) return kInternalError;
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region migrate lanid
  async migrateLanId() {
    try {
      const options = { where: { lan_id: { [Op.eq]: null } } };
      const att = ['id', 'body', 'response', 'loanId'];
      const result = await this.insuranceRepo.getTableWhereData(att, options);
      if (!result || result === k500Error) return kInternalError;
      for (let index = 0; index < result.length; index++) {
        try {
          const ele = result[index];
          const loanId = ele?.loanId;
          const response = JSON.parse(ele.response);
          const body = JSON.parse(ele.body);
          const leadId = response?.LeadId ?? '';
          if (leadId) {
            const lan_id =
              response?.lan_id ??
              body?.QuoteRequest?.LoanDetails?.LoanAccountNo ??
              '';
            if (lan_id) {
              const update = { leadId, lan_id, status: 1 };
              await this.insuranceRepo.updateRowData(update, ele.id);
              const insuranceRedisKey = 'INSURANCE_DATA_' + loanId;
              await this.redisService.del(insuranceRedisKey);
            }
          } else console.log(ele.id);
        } catch (error) {}
      }
    } catch (error) {}
  }
  //#endregion

  //#region reinitiate insurance
  async reinitiateInsurance(body) {
    try {
      const loanId = body?.loanId;
      const isUpdate = body?.isUpdate ?? false;
      const options: any = {
        where: {
          status: 2,
          leadId: { [Op.eq]: null },
          lan_id: { [Op.eq]: null },
        },
      };
      if (loanId) options.where.loanId = loanId;
      const att = ['id', 'loanId', 'status', 'response'];
      const result = await this.insuranceRepo.getTableWhereData(att, options);
      if (result === k500Error) return kInternalError;
      if (isUpdate === true)
        for (let index = 0; index < result.length; index++) {
          try {
            const ele = result[index];
            if (ele?.loanId) {
              const response = JSON.parse(ele?.response);
              if (response?.success != true) {
                const data = await this.insuranceRepo.deleteData(ele.id, false);
                if (data === k500Error) continue;
                const insuranceRedisKey = 'INSURANCE_DATA_' + ele?.loanId;
                await this.redisService.del(insuranceRedisKey);
                await this.insuranceProposal({ loanId: ele?.loanId });
              }
            }
          } catch (error) {}
        }
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get loan and emi wise paid and waived bifurcation data
  async loanPaidWaivedData(query) {
    try {
      const id = query?.loanId ?? '';
      if (!id) return kParamMissing('loanId');
      const loanAttr = [
        'id',
        'netApprovedAmount',
        'loanStatus',
        'paid_principal',
        'paid_interest',
        'paid_penalty',
        'waived_principal',
        'waived_interest',
        'waived_penalty',
      ];

      const emiAttr = [
        'id',
        'penalty',
        'principalCovered',
        'interestCalculate',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
        'paid_principal',
        'paid_interest',
        'paid_penalty',
        'waived_principal',
        'waived_interest',
        'waived_penalty',
      ];
      const emiInclude = { model: EmiEntity, attributes: emiAttr };

      const loanOpt = {
        where: { id },
        include: [emiInclude],
      };

      const loanData: any = await this.repository.getRowWhereData(
        loanAttr,
        loanOpt,
      );
      if (loanData == k500Error) return kInternalError;

      loanData.TotalRepayAmount = loanData.emiData.reduce((acc, emi, idx) => {
        acc += +emi.principalCovered;
        acc += +emi.interestCalculate;
        acc += +emi.penalty;
        loanData.emiData[idx].emi_amount =
          +emi.principalCovered + +emi.interestCalculate;
        return acc;
      }, 0);
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region add QualityParameters to loan
  async addQualityParameters(body) {
    const loanId = body?.loanId;
    const adminId = body?.adminId;
    let newParameters = body?.qualityParameters;
    if (!loanId) return kParamMissing('loanId');
    if (!adminId) return kParamMissing('adminId');
    if (!newParameters) return kParamMissing('newParameters');

    newParameters = await this.qualityParameterService.getLowerCaseData(
      newParameters,
    );
    if (newParameters?.message) return newParameters;
    if (!newParameters) return kBadRequest;

    // check loan must be complete
    const loanStatus: any = await this.getLoanStatusByLoanId(loanId);
    if (loanStatus?.message) return loanStatus;
    if (loanStatus !== 1) return k422ErrorMessage(kNoDataFound);

    // get quality parameters from Database
    const oldParameters: any =
      await this.qualityParameterService.getQualityParameter(false);
    if (oldParameters?.message) return oldParameters;

    const isSame: any = this.compareQualityParameters(
      oldParameters,
      newParameters,
    );
    if (isSame?.message) return isSame;
    if (!isSame) return k422ErrorMessage('Parameter field missing');

    // check only one option must be selected for all parameters
    const isSelected: any = this.checkAllQualitySelected(newParameters);

    if (isSelected?.message) return isSelected;
    if (isSelected == false) return k422ErrorMessage('Select any one option');

    const qualityData: any = this.getQualityScore(oldParameters, newParameters);
    if (qualityData?.message) throw new Error();

    const qualityScore = qualityData?.qualityScore;
    newParameters = qualityData?.newParameters;

    // Add quality parameters and quality score to the loan id
    const updatedLoanQuality = await this.repository.updateRowData(
      {
        qualityParameters: {
          submission_date: new Date().toJSON(),
          data: newParameters,
          adminId,
          openTime: body?.openTime ?? new Date().toJSON(),
          submitTime: body?.submitTime ?? new Date().toJSON(),
        },
        qualityScore,
      },
      loanId,
    );
    if (updatedLoanQuality === k500Error) throw new Error();
    return { loanId, qualityScore };
  }
  //#endregion

  async enablePartPay(body) {
    const id = body?.loanId;
    if (!id) return kParamMissing('loanId');
    const adminId = body?.adminId;
    if (!adminId) return kParamMissing('adminId');

    const attributes = ['penalty_days', 'payment_status'];
    const loanInclude = {
      model: loanTransaction,
      attributes: ['isPartPayment'],
      where: { loanStatus: 'Active' },
    };
    const options = {
      where: {
        loanId: id,
        payment_status: '0',
        penalty_days: { [Op.gt]: 0 },
      },
      include: [loanInclude],
    };
    const emiData = await this.emiRepo.getRowWhereData(attributes, options);
    if (emiData == k500Error) return kInternalError;
    if (!emiData) return k422ErrorMessage('Not Eligible for Part-Pay');

    const loanData = emiData?.loan;
    // Enable part pay if User is delay(Weather he is One day delay)
    const isPartPayment = loanData?.isPartPayment ?? 0 ? 0 : 1;
    const updateLoan: any = await this.repository.updateRowData(
      { isPartPayment, partPayEnabledBy: adminId },
      id,
    );
    if (updateLoan == k500Error) return kInternalError;
    return true;
  }

  //#region get quality score
  private getQualityScore(oldData, newData) {
    try {
      oldData?.sort((a, b) => a?.title - b?.title);
      newData?.sort((a, b) => a?.title - b?.title);
      let qualityScore = 0;
      let newParameters = [];
      for (let i = 0; i < newData?.length; i++) {
        try {
          const options = newData[i]?.options;
          const oldOptions = oldData[i]?.options;
          const key = Object.keys(oldOptions ?? '{}');
          if (!key) continue;
          key.forEach((i) => {
            try {
              options[i].score = oldOptions[i]?.score ?? 0;
              if (options[i]?.selected == true) {
                qualityScore += oldOptions[i]?.score ?? 0;
              }
            } catch (error) {}
          });
          newParameters.push({ ...newData[i], options });
        } catch (error) {}
      }
      qualityScore = Math.round(qualityScore / newData?.length);
      return { qualityScore, newParameters };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get loan status for loan id
  private async getLoanStatusByLoanId(id: number) {
    try {
      const loanOpt = {
        where: {
          id,
          loanStatus: ['Active', 'Complete'],
          qualityParameters: { [Op.eq]: null },
        },
      };
      const loanData = await this.repository.getCountsWhere(loanOpt);
      if (loanData === k500Error) return kInternalError;
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check all parameter must be selected from options
  private checkAllQualitySelected(qualityParameters) {
    try {
      for (let i = 0; i < qualityParameters?.length; i++) {
        try {
          const options = qualityParameters[i]?.options;
          if (!options) continue;
          const keys = Object.keys(options);
          const selected = keys.filter(
            (item) => options[item].selected == true,
          );
          if (selected.length !== 1) return false;
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return kInternalError;
        }
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region compare parameters with latest version
  private compareQualityParameters(oldData, newData) {
    try {
      if (oldData?.length != newData?.length) return false;
      oldData?.sort((a, b) => a?.title - b?.title);
      newData?.sort((a, b) => a?.title - b?.title);

      for (let i = 0; i < oldData.length; i++) {
        try {
          const oldD = oldData[i];
          const newD = newData[i];
          if (oldD.title != newD.title) return false;
          if (oldD?.disabled !== newD?.disabled) return false;
          const oldKeys = Object.keys(oldD.options);
          const newKeys = Object.keys(newD.options);
          if (oldKeys.length !== newKeys.length) return false;
          oldKeys.sort();
          newKeys.sort();
          if (JSON.stringify(oldKeys) !== JSON.stringify(newKeys)) return false;
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get loan quality parameters by loan id
  async getQualityParameters(query) {
    try {
      const id = query?.loanId;
      if (!id) return kParamMissing('loanId');
      const data = await this.repository.getRowWhereData(
        ['qualityParameters'],
        { where: { id } },
      );
      if (data === k500Error) return kInternalError;
      const adminData = await this.commonService.getAdminData(
        data?.qualityParameters?.adminId,
      );
      if (adminData === k500Error) return kInternalError;
      return {
        data: this.prepareQualityData(data?.qualityParameters?.data),
        adminName: adminData?.fullName,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepare quality parameters data
  private prepareQualityData(data) {
    try {
      const preparedData = [];
      for (let i = 0; i < data?.length; i++) {
        try {
          const tempObj = data[i];
          const options = tempObj?.options;
          const keys = Object.keys(options ?? '{}');
          if (!keys?.length) continue;
          keys?.sort(
            (a, b) => (options[b]?.score ?? 0) - (options[a]?.score ?? 0),
          );
          const tempOpts = [];
          // prepare options
          for (let j = 0; j < keys.length; j++) {
            try {
              const key = keys[j];
              const rawData = options[key];
              tempOpts.push({
                option: key,
                selected: rawData?.selected ?? 'false',
              });
            } catch (error) {}
          }
          tempObj.options = tempOpts;
          delete tempObj?.disabled;
          preparedData.push(tempObj);
        } catch (error) {}
      }
      return preparedData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region Insurance Migrate for Null Insurance ids
  async InsuranceMigrate() {
    try {
      const attributes = ['id'];
      const date = new Date();
      date.setDate(date.getDate() - 2);
      const twoDaysAgo = await this.typeService.getGlobalDate(date).toJSON();
      const options = {
        where: {
          loanStatus: 'Active',
          insuranceId: { [Op.eq]: null },
          insuranceOptValue: true,
          loan_disbursement_date: { [Op.gte]: twoDaysAgo },
        },
      };
      const loanIds = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (loanIds == k500Error) return kInternalError;
      let length = loanIds.length;
      for (let index = 0; index < length; index++) {
        try {
          const element = loanIds[index];
          await this.insuranceProposal({ loanId: element.id });
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funEMIAndRepaymentData(data) {
    try {
      const loanId = data?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const attributes = [
        'id',
        'loanStatus',
        'userId',
        'netEmiData',
        'interestRate',
        'loan_disbursement_date',
        'penaltyCharges',
      ];
      const transactionInclude = {
        model: TransactionEntity,
        required: false,
        order: [['id', 'DESC']],
        attributes: [
          'id',
          'emiId',
          'paidAmount',
          'status',
          'completionDate',
          'subscriptionDate',
          'source',
          'type',
          'subSource',
          'accStatus',
          'transactionId',
          'utr',
          'principalAmount',
          'interestAmount',
          'penaltyAmount',
          'userId',
          'subStatus',
          'createdAt',
          'adminId',
          'legalCharge',
          'cgstOnLegalCharge',
          'sgstOnLegalCharge',
          'igstOnLegalCharge',
          'cgstOnPenalCharge',
          'sgstOnPenalCharge',
          'igstOnPenalCharge',
          'penalCharge',
          'regInterestAmount',
          'bounceCharge',
          'cgstOnBounceCharge',
          'igstOnBounceCharge',
          'sgstOnBounceCharge',
        ],
      };
      const EmiInclude = {
        model: EmiEntity,
        attributes: [
          'id',
          'userId',
          'bounceCharge',
          'gstOnBounceCharge',
          'emi_amount',
          'emi_date',
          'payment_status',
          'payment_due_status',
          'principalCovered',
          'interestCalculate',
          'penalty',
          'totalPenalty',
          'penalty_days',
          'partPaymentPenaltyAmount',
          'waiver',
          'paid_waiver',
          'unpaid_waiver',
          'fullPayPrincipal',
          'fullPayPenalty',
          'fullPayInterest',
          'pay_type',
          'legalCharge',
          'legalChargeGST',
          'fullPayLegalCharge',
          'regInterestAmount',
          'dpdAmount',
          'penaltyChargesGST',
          'fullPayPenal',
          'fullPayRegInterest',
          'fullPayBounce',
          'waived_regInterest',
          'waived_bounce',
          'waived_penal',
          'waived_legal',
          'paid_principal',
          'paid_interest',
          'paidBounceCharge',
          'paidPenalCharge',
          'paidLegalCharge',
          'adScreenshotUrl',
          'paidRegInterestAmount',
          'forClosureAmount',
          'sgstForClosureCharge',
          'cgstForClosureCharge',
          'igstForClosureCharge',
        ],
      };
      const subsInclude = {
        model: SubScriptionEntity,
        attributes: ['id', 'mode'],
      };
      const options = {
        where: { id: loanId },
        include: [EmiInclude, transactionInclude, subsInclude],
      };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      const fullPay = await this.sharedCalculation.getFullPaymentData({
        loanId,
      });
      if (loanData?.emiData?.length == 0) {
        const NetEMIData = await this.prepareNetEmiData(loanData);
        return { EMIData: NetEMIData, transactionData: [] };
      }
      const EMIData = await this.emiSharedService.prepareEMIDetails(loanData);
      for (let index = 0; index < loanData?.transactionData.length; index++) {
        const ele = loanData?.transactionData[index];
        try {
          const emiData = loanData?.emiData;
          emiData.sort((a, b) => a.id - b.id);
          ele.emiNum = '';
          emiData.forEach((el, index) => {
            try {
              if (ele?.emiId == el.id) ele.emiNum = `EMI-${index + 1}`;
              if (ele.type == 'FULLPAY' && el.pay_type == 'FULLPAY') {
                if (ele.emiNum == '') ele.emiNum = `EMI-${index + 1}`;
                else ele.emiNum += ` & ${index + 1}`;
              }
            } catch (error) {}
          });
          const adminData = await this.commonService.getAdminData(ele.adminId);
          ele.admin = { id: adminData.id, fullName: adminData.fullName };
          const mode = loanData?.subscriptionData?.mode;
          ele.source = ele?.source == 'AUTOPAY' ? mode : ele?.source;
          const source = ele?.source;
          const type = ele?.type;
          ele['Response date'] = ele?.completionDate ?? '-';
          ele.createdAt =
            ele?.subSource == 'AUTODEBIT'
              ? ele.subscriptionDate
              : ele.createdAt;
          const utr = ele?.utr;
          ele.paymentURL = '-';
          if (ele?.status != 'INITIALIZED') {
            if (source == 'CASHFREE') {
              const response = JSON.parse(ele?.response);
              const resObj = Array.isArray(response) ? response[0] : response;
              const paymentId =
                resObj?.cf_payment_id ?? resObj?.payment?.referenceId;
              ele.paymentURL = `${kGetCashfreePayment}?txId=${paymentId}`;
            } else if (source == 'RAZORPAY') {
              const pay = type == 'REFUND' ? 'refunds/' : 'payments/';
              ele.paymentURL = `${kGetRazorpayPayment}${pay}${utr}`;
            } else if (source == 'SIGNDESK') {
              const mandateId = utr.split('-id-')[1];
              ele.paymentURL =
                mandateId != 'NA' ? `${kGetSigndeskPayment}${mandateId}` : '-';
            }
          }
          delete ele.subscriptionDate;
          delete ele.completionDate;
        } catch (error) {}
      }
      return {
        EMIData,
        transactionData: loanData?.transactionData,
        fullPay: fullPay?.totalAmount,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async getFullPayAmount(reqData: any) {
    try {
      const id = reqData.loanId;
      if (!id) return kParamMissing('loanId');
      let targetDate = reqData.targetDate;
      if (targetDate) {
        targetDate = new Date(targetDate);
        const today = new Date();
        const differenceInDays = this.typeService.dateDifference(
          today,
          targetDate,
          'Days',
        );
        if (differenceInDays > 3)
          return k422ErrorMessage('Transaction is more than 3 days old');
      } else targetDate = new Date();
      const data = await this.sharedCalculation.getFullPaymentData(reqData);
      if (data?.message) return data;
      const fullPayAmount = data?.totalAmount;
      if (!fullPayAmount) return kInternalError;
      return fullPayAmount;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async massEMIRepaymentDetails(reqData) {
    // Params validation
    const loanIds = reqData.loanIds;
    if (!loanIds) return kParamMissing('loanIds');

    const finalizedData = {};
    const totalLists = this.typeService.splitToNChunks(loanIds, 100);
    for (let index = 0; index < totalLists.length; index++) {
      try {
        const targetList = totalLists[index];
        const promiseList = [];
        for (let i = 0; i < targetList.length; i++) {
          try {
            const loanId = targetList[i];
            promiseList.push(this.funEMIAndRepaymentData({ loanId }));
          } catch (error) {}
        }
        const spanResponse = await Promise.all(promiseList);
        for (let i = 0; i < targetList.length; i++) {
          try {
            const loanId = targetList[i];
            if (spanResponse[i]?.message) continue;
            finalizedData[loanId] = spanResponse[i];
          } catch (error) {}
        }
      } catch (error) {}
    }

    return finalizedData;
  }

  async getEMIDetails(data) {
    // Validation -> Parameters
    const loanId = data?.loanId;
    if (!loanId && !data.loanIds) return kParamMissing('loanId');
    // Purpose -> Testing
    const loanIds = data?.loanIds;

    // Preparation -> Query
    const loanAttr = [
      'id',
      'netEmiData',
      'penaltyCharges',
      'interestRate',
      'loan_disbursement_date',
      'isPartPayment',
      'partPayEnabledBy',
      'loanClosureMailCount',
      'settlementMailCount',
      'loanClosureEnabledBy',
      'loanSettlementEnabledBy',
      'isLoanClosure',
      'loanStatus',
      'charges',
    ];
    const traAttrs = [
      'id',
      'emiId',
      'paidAmount',
      'status',
      'completionDate',
      'subscriptionDate',
      'type',
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'transactionId',
      'createdAt',
      'legalCharge',
      'cgstOnLegalCharge',
      'sgstOnLegalCharge',
      'igstOnLegalCharge',
      'cgstOnPenalCharge',
      'sgstOnPenalCharge',
      'igstOnPenalCharge',
      'penalCharge',
      'regInterestAmount',
      'bounceCharge',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
      'source',
    ];
    const emiAttr = [
      'id',
      'userId',
      'bounceCharge',
      'gstOnBounceCharge',
      'emi_amount',
      'emi_date',
      'payment_status',
      'payment_due_status',
      'principalCovered',
      'interestCalculate',
      'penalty',
      'totalPenalty',
      'penalty_days',
      'partPaymentPenaltyAmount',
      'waiver',
      'paid_waiver',
      'unpaid_waiver',
      'fullPayPrincipal',
      'fullPayPenalty',
      'fullPayInterest',
      'pay_type',
      'legalCharge',
      'legalChargeGST',
      'fullPayLegalCharge',
      'regInterestAmount',
      'dpdAmount',
      'penaltyChargesGST',
      'fullPayPenal',
      'fullPayRegInterest',
      'fullPayBounce',
      'waived_regInterest',
      'waived_bounce',
      'waived_penal',
      'waived_legal',
      'paid_principal',
      'paid_interest',
      'paidBounceCharge',
      'paidPenalCharge',
      'paidLegalCharge',
      'adScreenshotUrl',
      'paidRegInterestAmount',
      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
      'waived_foreclose',
    ];
    const transInc = {
      model: TransactionEntity,
      attributes: traAttrs,
      where: { status: kCompleted },
      required: false,
    };
    if (loanIds)
      transInc.attributes = ['completionDate', 'loanId', 'paidAmount', 'type'];

    const waiverDataInc = {
      model: WaiverEntity,
      attributes: [
        'oldBifurcation',
        'newBifurcation',
        'waiverBifurcation',
        'waiverAmount',
        'loanId',
        'userId',
        'emiId',
        'adminId',
        'type',
      ],
      where: {
        type: { [Op.or]: [{ [Op.ne]: 'WAIVER_REVERSED' }, { [Op.eq]: null }] },
      },
      required: false,
    };
    const emiInc = { model: EmiEntity, attributes: emiAttr };
    const options = {
      where: { id: loanIds ?? loanId },
      include: [emiInc, transInc, waiverDataInc],
    };
    // Hit -> Query
    if (loanIds)
      return await this.repository.getTableWhereData(loanAttr, options);
    // Hit -> Query
    const loanData = await this.repository.getRowWhereData(loanAttr, options);
    // Validation -> Query data
    if (loanData === k500Error) throw new Error();
    if (!loanData) return k422ErrorMessage(kNoDataFound);
    return await this.calculateEMIDetails(loanData);
  }

  async calculateEMIDetails(loanData) {
    // Before loan is active
    if (loanData?.emiData?.length == 0) {
      const NetEMIData = this.prepareNetEmiData(loanData);
      return { EMIData: NetEMIData };
    }
    loanData.isCalledFromEMIDetails = true;
    const emiData: any = await this.emiSharedService.prepareEMIDetails(
      loanData,
    );
    if (emiData?.message) return emiData;

    return {
      ...emiData,
    };
  }

  prepareNetEmiData(loanData) {
    try {
      const EmiData = [];
      const netEmiData = loanData.netEmiData;
      const collateral_charge_amt =
        loanData?.charges?.collateral_charge_amt ?? 0;
      let collateralEmiCalculation: any = {};

      for (let index = 0; index < netEmiData.length; index++) {
        try {
          const emi = JSON.parse(netEmiData[index]);
          const ObjData: any = {
            emiDate: this.typeService.getDateFormated(emi.Date, '/'),
            emiAmount: emi.Emi,
            emiDays: emi.Days,
            principal: emi.PrincipalCovered,
            interest: emi.InterestCalculate,
            interestRate: emi.RateofInterest,
          };
          // Prepare separate parameters on the last iteration
          if (index == netEmiData.length - 1 && collateral_charge_amt) {
            collateralEmiCalculation.lastEmi =
              ObjData.emiAmount + collateral_charge_amt;
            collateralEmiCalculation.cashCollateral = collateral_charge_amt;
            collateralEmiCalculation.netRepayAmount = ObjData.emiAmount;
            ObjData.collateralEmiCalculation = collateralEmiCalculation;
          }
          EmiData.push(ObjData);
        } catch (error) {}
      }
      return EmiData;
    } catch (error) {
      return [];
    }
  }

  async foreCloseReport(data) {
    const loanId = data?.loanId;
    const download = data?.download;
    let startDate;
    let endDate;
    if (data?.startDate && data?.endDate) {
      startDate = this.typeService.getGlobalDate(data.startDate);
      endDate = this.typeService.getGlobalDate(data.endDate);
    }
    if (!loanId && !startDate && !endDate) return {};
    const loanAttr = [
      'id',
      'loan_disbursement_date',
      'loanCompletionDate',
      'userId',
    ];
    const traAttrs = [
      'id',
      'emiId',
      'status',
      'type',
      'paidAmount',
      'principalAmount',
      'interestAmount',
      'transactionId',
      'completionDate',
    ];
    const emiAttr = [
      'id',
      'emi_amount',
      'emi_date',
      'principalCovered',
      'interestCalculate',
    ];
    const transInc = {
      model: TransactionEntity,
      attributes: traAttrs,
      where: { status: kCompleted },
      required: false,
    };
    const userInc = {
      model: registeredUsers,
      attributes: ['id', 'fullName', 'phone'],
    };
    const emiInc = { model: EmiEntity, attributes: emiAttr };
    const options: any = {
      where: { loanStatus: 'Complete' },
      include: [userInc, emiInc, transInc],
    };
    if (loanId) options.where.id = loanId;
    if (startDate && endDate)
      options.where.loan_disbursement_date = {
        [Op.gte]: startDate.toJSON(),
        [Op.lte]: endDate.toJSON(),
      };
    const loanData = await this.repository.getTableWhereData(loanAttr, options);
    if (loanData === k500Error) throw new Error();
    const length = loanData.length;
    if (length == 0) return k422ErrorMessage(kNoDataFound);
    const finalData = [];
    for (let i = 0; i < length; i++) {
      const loan = loanData[i];
      const user = loan.registeredUsers;
      const emiData = loan.emiData;
      emiData.sort((a, b) => b.id - a.id);
      const lastEmiDate = this.typeService.getGlobalDate(emiData[0].emi_date);
      const loanCompletionDate = this.typeService.getGlobalDate(
        loan?.loanCompletionDate,
      );
      if (lastEmiDate < loanCompletionDate) continue;
      const trans = loan?.transactionData ?? [];
      const originalTrans = await this.commonService.filterTransActionData(
        trans,
      );
      let expectedPrincipal = 0;
      let expectedInterest = 0;
      for (let e = 0; e < emiData.length; e++) {
        const emi = emiData[e];
        expectedPrincipal += emi?.principalCovered ?? 0;
        expectedInterest += emi?.interestCalculate ?? 0;
      }
      let paidPrincipal = 0;
      let paidInterest = 0;
      for (let t = 0; t < originalTrans.length; t++) {
        const tra = originalTrans[t];
        paidPrincipal += tra?.principalAmount ?? 0;
        paidInterest += tra?.interestAmount ?? 0;
      }
      const obj = {
        loanId: loan.id,
        userId: loan.userId,
        Name: user.fullName,
        Phone: this.cryptService.decryptPhone(user.phone),
        'Disbursement date': this.typeService.getDateFormatted(
          loan.loan_disbursement_date,
        ),
        'Loan completion date': this.typeService.getDateFormatted(
          loan.loanCompletionDate,
        ),
        'Expected principal': +expectedPrincipal,
        'Paid principal': +paidPrincipal.toFixed(2),
        'Expected interest': +expectedInterest,
        'Paid interest': +paidInterest.toFixed(2),
        'Interest difference': +(expectedInterest - paidInterest).toFixed(2),
      };
      finalData.push(obj);
    }
    if (download == true) {
      const rawExcelData = {
        sheets: ['foreClose-report'],
        data: [finalData],
        sheetName: 'foreClose-report.xlsx',
      };

      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      return { fileUrl: url };
    } else return finalData;
  }

  async syncLoanDetails(reqData) {
    // Dependency -> Number of EMIs ( In case in future emi numbers are high need to change below query )
    let rawQuery = `
    SELECT "loan"."id", "loan"."loanStatus", CAST("loan"."netApprovedAmount" AS DOUBLE PRECISION) AS "approved_amount",
    "loan"."loan_disbursement_date" AS "disbursed_date", "loan"."completedLoan" AS "completed_loans", "loan"."userId",
    "cibil"."cibilScore" AS "cibil_score", "cibil"."plScore" AS "pl_score", "cibil"."PLAccounts" AS "pl_accounts",
    "cibil"."inquiryPast30Days" AS "inquiry_last_30_days", "cibil"."PLOutstanding" AS "PLOutstanding",
    "banking"."salary", "banking"."consentMode", "banking"."otherDetails", "kyc"."aadhaarState",
    "kyc"."aadhaarAddress",
    "emi_1"."id" AS "emi_1_id", "emi_2"."id" AS "emi_2_id",
    "emi_3"."id" AS "emi_3_id", "emi_4"."id" AS "emi_4_id",
    "emi_5"."id" AS "emi_5_id", "emi_6"."id" AS "emi_6_id",
    "emi_1"."emi_date" AS "emi_1_date", "emi_2"."emi_date" AS "emi_2_date",
    "emi_3"."emi_date" AS "emi_3_date", "emi_4"."emi_date" AS "emi_4_date",
    "emi_5"."emi_date" AS "emi_5_date", "emi_6"."emi_date" AS "emi_6_date",
    "emi_1"."pay_type" AS "emi_1_pay_type", "emi_2"."pay_type" AS "emi_2_pay_type",
    "emi_3"."pay_type" AS "emi_3_pay_type", "emi_4"."pay_type" AS "emi_4_pay_type",
    "emi_5"."pay_type" AS "emi_5_pay_type", "emi_6"."pay_type" AS "emi_6_pay_type",

    "emi_1"."payment_done_date" AS "emi_1_pay_date", "emi_2"."payment_done_date" AS "emi_2_pay_date",
    "emi_3"."payment_done_date" AS "emi_3_pay_date", "emi_4"."payment_done_date" AS "emi_4_pay_date",
    "emi_5"."payment_done_date" AS "emi_5_pay_date", "emi_6"."payment_done_date" AS "emi_6_pay_date",

    "emi_1"."principalCovered" AS "emi_1_expected_principal", "emi_2"."principalCovered" AS "emi_2_expected_principal",
    "emi_3"."principalCovered" AS "emi_3_expected_principal", "emi_4"."principalCovered" AS "emi_4_expected_principal",
    "emi_5"."principalCovered" AS "emi_5_expected_principal", "emi_6"."principalCovered" AS "emi_6_expected_principal",
    "emi_1"."interestCalculate" AS "emi_1_standard_interest", "emi_2"."interestCalculate" AS "emi_2_standard_interest",
    "emi_3"."interestCalculate" AS "emi_3_standard_interest", "emi_4"."interestCalculate" AS "emi_4_standard_interest",
    "emi_5"."interestCalculate" AS "emi_5_standard_interest", "emi_6"."interestCalculate" AS "emi_6_standard_interest",

    "emi_1"."payment_due_status" AS "emi_1_payment_due_status", "emi_2"."payment_due_status" AS "emi_2_payment_due_status",
    "emi_3"."payment_due_status" AS "emi_3_payment_due_status", "emi_4"."payment_due_status" AS "emi_4_payment_due_status",
    "emi_5"."payment_due_status" AS "emi_5_payment_due_status", "emi_6"."payment_due_status" AS "emi_6_payment_due_status",
    COALESCE("emi_1"."fullPayPrincipal", 0) AS "emi_1_full_pay_principal", COALESCE("emi_2"."fullPayPrincipal", 0) AS "emi_2_full_pay_principal",
    COALESCE("emi_3"."fullPayPrincipal", 0) AS "emi_3_full_pay_principal", COALESCE("emi_4"."fullPayPrincipal", 0) AS "emi_4_full_pay_principal",
    COALESCE("emi_5"."fullPayPrincipal", 0) AS "emi_5_full_pay_principal", COALESCE("emi_6"."fullPayPrincipal", 0) AS "emi_6_full_pay_principal",
    COALESCE("emi_1"."fullPayInterest", 0) AS "emi_1_full_pay_interest", COALESCE("emi_2"."fullPayInterest", 0) AS "emi_2_full_pay_interest",
    COALESCE("emi_3"."fullPayInterest", 0) AS "emi_3_full_pay_interest", COALESCE("emi_4"."fullPayInterest", 0) AS "emi_4_full_pay_interest",
    COALESCE("emi_5"."fullPayInterest", 0) AS "emi_5_full_pay_interest", COALESCE("emi_6"."fullPayInterest", 0) AS "emi_6_full_pay_interest"
    FROM "loanTransactions" AS "loan"

    LEFT JOIN "EmiEntities" AS "emi_1" ON "emi_1"."loanId" = "loan"."id" AND "emi_1"."emiNumber" = 1
    LEFT JOIN "EmiEntities" AS "emi_2" ON "emi_2"."loanId" = "loan"."id" AND "emi_2"."emiNumber" = 2
    LEFT JOIN "EmiEntities" AS "emi_3" ON "emi_3"."loanId" = "loan"."id" AND "emi_3"."emiNumber" = 3
    LEFT JOIN "EmiEntities" AS "emi_4" ON "emi_4"."loanId" = "loan"."id" AND "emi_4"."emiNumber" = 4
    LEFT JOIN "EmiEntities" AS "emi_5" ON "emi_5"."loanId" = "loan"."id" AND "emi_5"."emiNumber" = 5
    LEFT JOIN "EmiEntities" AS "emi_6" ON "emi_6"."loanId" = "loan"."id" AND "emi_6"."emiNumber" = 6

    LEFT JOIN "CibilScoreEntities" AS "cibil" ON "cibil"."id" = "loan"."cibilId"
    INNER JOIN "BankingEntities" AS "banking" ON "banking"."id" = "loan"."bankingId"
    INNER JOIN "registeredUsers" AS "user" ON "user"."id" = "loan"."userId"
    INNER JOIN "KYCEntities" AS "kyc" ON "kyc"."id" = "user"."kycId"

    WHERE "loan"."loan_disbursement_date" >= '${reqData.start_date}'
    AND "loan"."loan_disbursement_date" <= '${reqData.end_date}'
    AND "loan"."loanStatus" IN ('Active', 'Complete')`;

    const outputList = await this.repo.injectRawQuery(
      loanTransaction,
      rawQuery,
    );
    if (outputList == k500Error) throw new Error();

    const loanIds = outputList.map((el) => el.id);
    rawQuery = `SELECT "completionDate", "principalAmount", "interestAmount", "emiId", "loanId"
    FROM "TransactionEntities" AS "trans"
    WHERE "status" = 'COMPLETED' AND "type" NOT IN ('REFUND', 'FULLPAY')
    AND "loanId" IN ('${loanIds.join("','")}')`;
    const transList = await this.repo.injectRawQuery(
      TransactionEntity,
      rawQuery,
    );
    if (transList == k500Error) throw new Error();

    const finalizedList = [];
    const today = this.typeService.getGlobalDate(new Date());
    const todayTime = today.getTime();
    for (let index = 0; index < outputList.length; index++) {
      const loanData = outputList[index];

      let expected_principal = 0;
      let paid_principal = 0;
      let expected_interest = 0;
      let paid_interest = 0;
      let max_dpd = 0;
      let repayment_status = -1;
      for (let i = 0; i < 6; i++) {
        // No EMI left
        let emiDate = loanData[`emi_${i + 1}_date`];
        if (!emiDate) break;

        // Upcoming EMI
        emiDate = this.typeService.getGlobalDate(new Date(emiDate));
        if (emiDate.getTime() >= todayTime) {
          if (repayment_status == -1) {
            repayment_status = 4;
          }
          continue;
        }

        const emiId = loanData[`emi_${i + 1}_id`];
        const paymentList = transList.filter((el) => el.emiId == emiId);
        let emiPrincipal = 0;
        let emiInterest = 0;
        paymentList.forEach((el) => {
          emiPrincipal += el.principalAmount;
          emiInterest += el.interestAmount;
        });
        const fullPayPrincipal = loanData[`emi_${i + 1}_full_pay_principal`];
        const fullPayInterest = loanData[`emi_${i + 1}_full_pay_interest`];
        const expPrincipal = loanData[`emi_${i + 1}_expected_principal`];
        let expInterest = loanData[`emi_${i + 1}_standard_interest`];
        const isFullPay = loanData[`emi_${i + 1}_pay_type`] == kFullPay;
        if (isFullPay) {
          const is_delayed = loanData[`emi_${i + 1}_payment_due_status`] == '1';
          if (!is_delayed) {
            expInterest = fullPayInterest ?? 0;
          }
        }
        let paidPrincipal = fullPayPrincipal + emiPrincipal;
        let paidInterest = fullPayInterest + emiInterest;
        if (paidPrincipal > expPrincipal) paidPrincipal = expPrincipal;
        if (paidInterest > expInterest) paidInterest = expInterest;

        expected_principal += expPrincipal;
        paid_principal += paidPrincipal;
        expected_interest += expInterest;
        paid_interest += paidInterest;

        // Calculation -> max_dpd
        let pay_date = loanData[`emi_${i + 1}_pay_date`];
        if (emiDate.getTime() <= todayTime) {
          if (pay_date) {
            pay_date = this.typeService.getGlobalDate(new Date(pay_date));
            // Delayed payment
            if (pay_date.getTime() > emiDate.getTime()) {
              const diffInDays = this.typeService.dateDifference(
                emiDate,
                pay_date,
              );
              if (diffInDays > max_dpd) max_dpd = diffInDays;
            }
          } else {
            const diffInDays = this.typeService.dateDifference(emiDate, today);
            if (diffInDays > max_dpd) max_dpd = diffInDays;
          }
        }

        if (!pay_date && max_dpd > 0) {
          repayment_status = 3; // Defaulter
        } else if (pay_date && max_dpd > 0 && repayment_status != 3) {
          repayment_status = 2; // Delay
        } else if (
          pay_date &&
          max_dpd == 0 &&
          repayment_status != 3 &&
          repayment_status != 2
        ) {
          // Pre paid
          if (pay_date.getTime() < emiDate.getTime() && repayment_status != 1) {
            repayment_status = 0;
          }
          // On time
          else if (pay_date.getTime() == emiDate.getTime()) {
            repayment_status = 1;
          }
        }
      }

      // Refund exclude
      if (paid_principal > expected_principal) {
        paid_principal = expected_principal;
      }
      if (paid_interest > expected_interest) {
        paid_interest = expected_interest;
      }

      const paymentList = transList.filter((el) => el.loanId == loanData.id);
      let closing_date = -1;
      if (paymentList?.length > 0) {
        paymentList.sort(
          (b, a) =>
            new Date(a.completionDate).getTime() -
            new Date(b.completionDate).getTime(),
        );
        // closing_date = paymentList[0].completionDate.substring(0, 10);
        // completionDate might come 'null' so handled substring error came while hitting syncLoanDetailsApiHitCron()
        closing_date =
          paymentList.length > 0 && paymentList[0].completionDate
            ? paymentList[0].completionDate.substring(0, 10)
            : -1;
      }

      let transactions_source = -1;
      if (loanData.consentMode == 'NETBANKING') transactions_source = 3;
      else if (!loanData.consentMode) transactions_source = 2;
      else if (loanData.consentMode == kfinvu) transactions_source = 1;
      else if (loanData.consentMode == kCAMS) transactions_source = 4;

      // Mapping -> Missing aadhaar state
      if (!loanData?.aadhaarState) {
        try {
          if (
            loanData?.aadhaarAddress &&
            typeof loanData?.aadhaarAddress == 'string'
          ) {
            const addressData = JSON.parse(loanData?.aadhaarAddress);
            if (addressData.state) {
              loanData.aadhaarState = addressData.state;
            }
          }
        } catch (error) {}
      }

      // Mapping -> Similar state into same
      if (loanData?.aadhaarState == 'Andaman and Nicobar Islands') {
        loanData.aadhaarState = 'Andaman & Nicobar Islands';
      } else if (loanData?.aadhaarState == 'Dadra and Nagar Haveli') {
        loanData.aadhaarState = 'Dadra & Nagar Haveli';
      } else if (
        loanData?.aadhaarState == 'Dadra and Nagar Haveli and Daman and Diu'
      ) {
        loanData.aadhaarState = 'Dadra & Nagar Haveli';
      } else if (loanData?.aadhaarState == 'Daman and Diu') {
        loanData.aadhaarState = 'Daman & Diu';
      } else if (loanData?.aadhaarState == 'Jammu and Kashmir') {
        loanData.aadhaarState = 'Jammu & Kashmir';
      } else if (loanData?.aadhaarState == 'Orissa') {
        loanData.aadhaarState = 'Odisha';
      } else if (loanData?.aadhaarState == 'Puducherry') {
        loanData.aadhaarState = 'Pondicherry';
      }

      const finalizedData = {
        loanId: loanData.id,
        userId: loanData.userId,
        completed_loans: loanData.completed_loans,
        loanStatus: loanData.loanStatus == 'Complete' ? 1 : 0,
        approved_amount: loanData.approved_amount,
        disbursed_date: loanData.disbursed_date?.substring(0, 10),
        closing_date,
        expected_principal,
        paid_principal,
        expected_interest,
        paid_interest,
        cibil_score: loanData.cibil_score ?? -2,
        pl_score: loanData.pl_score ?? -2,
        pl_accounts: loanData.pl_accounts ?? -2,
        pl_outstanding: loanData.PLOutstanding ?? 0,
        inquiry_last_30_days: loanData.inquiry_last_30_days ?? -2,
        verified_salary:
          (loanData?.salary ?? 0) > 0
            ? loanData?.salary
            : loanData?.otherDetails?.salary?.average ?? 0,
        transactions_source,
        aadhaarState: loanData?.aadhaarState ?? 'Unknown',
        max_dpd,
        repayment_status,
        lastIndex: false,
      };
      finalizedList.push(finalizedData);
    }

    await this.repo.bulkCreate(LoanDetails, finalizedList, {
      updateOnDuplicate: [
        'loanStatus',
        'expected_principal',
        'paid_principal',
        'expected_interest',
        'paid_interest',
        'closing_date',
        'lastIndex',
        'verified_salary',
        'pl_outstanding',
        'aadhaarState',
        'max_dpd',
        'repayment_status',
      ],
    });

    if (reqData?.indexRefresh == 'true') {
      rawQuery = `SELECT MAX("loanId") AS "loanId" FROM "LoanDetails"
      GROUP BY "userId"`;
      const userList = await this.repo.injectRawQuery(LoanDetails, rawQuery);
      if (userList == k500Error) throw new Error();

      const updatedLoanIds = userList.map((el) => el.loanId);
      await this.repo.updateRowWhereData(
        LoanDetails,
        { lastIndex: true },
        { where: { loanId: { [Op.in]: updatedLoanIds } } },
      );
    }

    return {};
  }

  //#region Sync Loan Details API Hit Cron Function
  async syncLoanDetailsApiHitCron() {
    const startYear = 2024;
    const currentYear = new Date().getFullYear();
    for (let year = startYear; year <= currentYear; year++) {
      const currentMonth = new Date().getMonth() + 1;
      for (let month = 1; month <= 12; month++) {
        let startDate = this.dateService.getGlobalDate(
          new Date(year, month - 1, 1),
        );
        let endDate = this.dateService.getGlobalDate(new Date(year, month, 0));
        if (year === currentYear && month === currentMonth)
          endDate = this.dateService.getGlobalDate(new Date());
        const indexRefresh = year === currentYear && month === currentMonth;
        await this.syncLoanDetails({
          start_date: startDate.toJSON(),
          end_date: endDate.toJSON(),
          indexRefresh,
        });
        if (year === currentYear && month === currentMonth) break;
      }
    }
    return {};
  }
  //#endregion

  // Commented as may require in future....
  // async rejectMultipleInprogressLoan() {
  //   const userAttributes = ['userId', [fn('COUNT', col('id')), 'count']];
  //   const userOptions = {
  //     where: { loanStatus: 'InProcess' },
  //     group: ['userId'],
  //     having: literal('COUNT(id) > 1'),
  //     order: [[literal('COUNT(id)'), 'desc']],
  //   };
  //   const users = await this.loanRepo.getTableWhereData(
  //     userAttributes,
  //     userOptions,
  //   );
  //   if (users == k500Error) return kInternalError;
  //   else if (users.length == 0) return k422ErrorMessage(kNoDataFound);
  //   const data = [];
  //   for (const user of users) {
  //     try {
  //       const dataUpdated = await this.updateRecords(user.userId);
  //       data.push(dataUpdated);
  //     } catch (error) {
  //       console.error(`Error while processing the user : ${error}`);
  //     }
  //   }
  //   return data;
  // }
  // async updateRecords(userId) {
  //   const dataUpdated = {
  //     userId,
  //     isUserUpdated: false,
  //     updatedLoans: [],
  //   };
  //   try {
  //     // Fetching the master records against the  userId....
  //     const masterRecord = await this.masterRepo.getTableWhereData(
  //       ['id', 'loanId', 'userId', 'status'],
  //       { where: { userId }, order: [['id', 'desc']] },
  //     );
  //     if (!masterRecord || masterRecord == k500Error) return dataUpdated;

  //     const firstRecord = masterRecord[0];

  //     // Updating user record of user....
  //     const userUpdate = await this.userRepo.updateRowData(
  //       { masterId: firstRecord.id, lastLoanId: firstRecord.loanId },
  //       userId,
  //     );
  //     if (userUpdate !== k500Error && userUpdate[0] != 0) {
  //       dataUpdated.isUserUpdated = true;
  //     }

  //     const loanIds = masterRecord.slice(1).map((record) => record.loanId);
  //     const loans = await this.loanRepo.getTableWhereData(
  //       ['id', 'loanStatus'],
  //       { where: { loanIds, loanStatus: 'InProcess' } },
  //     );
  //     if (!loans) return dataUpdated;

  //     for (const loan of loans) {
  //       const status = {
  //         loanId: loan.id,
  //         isLoanUpdated: false,
  //         isMasterUpdated: false,
  //       };
  //       const masterData = masterRecord.find(
  //         (record) => record.loanId == loan.id,
  //       );
  //       if (masterData) {
  //         // Updating loan record of user....
  //         const loanUpdate = await this.loanRepo.updateRowData(
  //           {
  //             masterId: firstRecord?.id,
  //             loanStatus: 'Rejected',
  //             manualVerification: 2,
  //             remark: 'Duplicate loan',
  //           },
  //           loan.id,
  //         );
  //         if (loanUpdate !== k500Error && loanUpdate?.[0] != 0) {
  //           status.isLoanUpdated = true;
  //         }

  //         // Updating master record of user....
  //         const masterUpdate = await this.masterRepo.updateRowData(
  //           { status: { loan: 2, eligibility: 2 } },
  //           masterData.id,
  //         );
  //         if (masterUpdate !== k500Error && masterUpdate[0] != 0) {
  //           status.isMasterUpdated = true;
  //         }
  //       }
  //       dataUpdated.updatedLoans.push(status);
  //     }
  //     return dataUpdated;
  //   } catch (error) {
  //     // Catching error if any inconvenience caused....
  //     console.error('Error resolving InProcess loan bug:', error);
  //     return dataUpdated;
  //   }
  // }

  //#region loanStatus CRON Alert API....
  async loanPaymentStatusAlert() {
    let startDate: any = new Date();
    startDate.setDate(startDate.getDate() - 5);
    startDate = this.dateService.getGlobalDate(startDate).toJSON();

    const transData = await this.transRepo.getTableWhereData(
      ['loanId', 'emiId', 'type', 'paidAmount'],
      {
        where: {
          status: kCompleted,
          type: ['EMIPAY', 'FULLPAY'],
          completionDate: { [Op.gte]: startDate },
        },
      },
    );
    if (transData === k500Error) throw new Error();
    if (transData.length == 0) return {};
    const loanIds = transData.map((trans) => trans.loanId);

    const emiInc = {
      model: EmiEntity,
      attributes: ['id', 'payment_status'],
    };
    const attr = ['id', 'loanStatus'];
    const options = {
      where: { loanStatus: 'Active', id: loanIds },
      include: [emiInc],
    };
    const loanData = await this.loanRepo.getTableWhereData(attr, options);
    if (loanData === k500Error) throw new Error();
    const length = loanData.length;
    if (!length || length == 0) return {};
    const loanToUpdate = new Set();
    const emiToUpdate = new Set();
    for (let i = 0; i < length; i++) {
      try {
        const loan = loanData[i];
        const emiData = loan.emiData;
        const checkStatus = emiData.find((emi) => emi.payment_status == 0);
        if (!checkStatus) {
          loanToUpdate.add(loan.id);
          continue;
        }
        const transactions = transData.filter(
          (record) => record.loanId === loan.id,
        );
        const transLength = transactions.length;
        for (let j = 0; j < transLength; j++) {
          const trans = transactions[j];
          if (trans.type == 'EMIPAY') {
            const emiRecord = emiData.find(
              (emi) => trans.emiId == emi.id && emi.payment_status == 0,
            );
            if (emiRecord) emiToUpdate.add(trans.loanId);
          } else if (trans.type == 'FULLPAY') {
            const fullRecord = emiData.find((emi) => emi.payment_status == 0);
            if (fullRecord) emiToUpdate.add(trans.loanId);
          }
        }
      } catch (error) {}
    }
    const emiNotUpdated = Array.from(emiToUpdate);
    const loanNotUpdated = Array.from(loanToUpdate);

    const slackPayload = {
      url: 'admin/loan/loanPaymentStatusAlert',
      fieldObj: {
        loanNotUpdated,
        emiNotUpdated,
      },
    };
    this.slackService.sendSlackCronAlert(slackPayload);
    return {};
  }
  //#endregion
}

// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class ComplianceEntity extends Model<ComplianceEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment: '0: Pending, 1: Accepted, 2: Rejected',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  sub_category_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  maker_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  checker_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  department_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  checked_date: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment:
      'The month-year of which task is prepared and uploaded to Compliance Tracker Dashboard. (ex. Task upload for May-2025)',
  })
  uploaded_for_month: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  maker_remark: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  checker_remark: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  file_url: any;
}

// Imports
import { Injectable } from '@nestjs/common/decorators';
import { Op } from 'sequelize';
import {
  GLOBAL_FLOW,
  OPTIONAL_DOCS_REQUIRED,
  PAGE_LIMIT,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { IFSC_VALIDATE_URL } from 'src/constants/network';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import {
  BAD_CIBIL_SCORE_MSG,
  kErrorMsgs,
  kNoDataFound,
  kNoTemplateFound,
  userCategoryTag,
} from 'src/constants/strings';
import { BankingEntity } from 'src/entities/banking.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { CrmRepository } from 'src/repositories/crm.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { TemplateRepository } from 'src/repositories/template.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { APIService } from 'src/utils/api.service';
import { CryptService } from 'src/utils/crypt.service';
import { DateService } from 'src/utils/date.service';
import { TypeService, crmTypeTitle } from 'src/utils/type.service';
import { EnvConfig } from 'src/configs/env.config';
import { MasterRepository } from 'src/repositories/master.repository';
import { ErrorContextService } from 'src/utils/error.context.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { HypothecationHistoryEntity } from 'src/entities/hypothecationHistory.entity';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import {
  EXCLUDE_CSE_LOAN_REJECT_REASON,
  LOAN_STATUS,
  PreApproveStatus,
} from 'src/constants/objects';
import { RedisService } from 'src/redis/redis.service';

@Injectable()
export class DashboardLoanService {
  constructor(
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    private readonly loanRepo: LoanRepository,
    private readonly apiService: APIService,
    private readonly templeteRepo: TemplateRepository,
    private readonly sharedNotification: SharedNotificationService,
    private readonly commonSharedService: CommonSharedService,
    private readonly dateService: DateService,
    private readonly crmRepo: CrmRepository,
    private readonly masterRepo: MasterRepository,
    private readonly errorContextService: ErrorContextService,
    private readonly repoManager: RepositoryManager,
    private readonly redisService: RedisService,
  ) {}

  async getAllLoanVerificationData(query) {
    try {
      const loanAttr = [
        'id',
        'manualVerification',
        'empId',
        'userId',
        'loanAmount',
        'netApprovedAmount',
        'manualVerificationAcceptName',
        'manualVerificationAcceptId',
        'prediction',
        'updatedAt',
        'bankingId',
        'remark',
        'loanRejectReason',
        'assignTo',
      ];
      const option: any = this.prepareOptionsForFB(
        query.status,
        query?.page ?? 1,
        query.searchText,
        query.adminId,
        query.startDate,
        query.endDate,
        query.newOrRepeated,
        query.download,
      );
      const loanData: any = await this.loanRepo.getTableWhereDataWithCounts(
        loanAttr,
        option,
      );
      if (!loanData || loanData == k500Error) return k500Error;

      const finalData: any = await this.preparDataForFinalVerification(
        loanData.rows,
      );
      if (finalData == k500Error) return k500Error;
      return {
        count: loanData.count,
        rows: finalData,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async checkIfscCode(ifsc = null) {
    try {
      if (!ifsc) return false;
      const url = IFSC_VALIDATE_URL + ifsc;
      const result: any = await this.apiService.requestGet(url);
      if (!result || result === k500Error) return false;
      if (result?.IFSC) return true;
      return false;
    } catch (error) {}
  }
  private prepareOptionsForFB(
    status: string,
    page: number,
    searchText: any,
    adminId,
    startDate = null,
    endDate = null,
    newOrRepeated = null,
    download = 'false',
  ) {
    try {
      /// user where condition
      const userWhere: any = {
        isBlacklist: '0',
        quantity_status: { [Op.or]: ['1', '3'] },
      };
      const toDay = this.typeService.getGlobalDate(new Date());
      if (searchText) {
        let encryptedData = '';
        if (!isNaN(searchText)) {
          encryptedData = this.cryptService.encryptPhone(searchText);
          encryptedData = encryptedData.split('===')[1];
        }
        userWhere[Op.or] = [
          { fullName: { [Op.iRegexp]: searchText } },
          {
            phone: {
              [Op.like]: encryptedData ? '%' + encryptedData + '%' : null,
            },
          },
        ];
      }
      if (newOrRepeated == '1') userWhere.completedLoans = 0;
      else if (newOrRepeated == '0') userWhere.completedLoans = { [Op.gt]: 0 };
      let where: any = {};
      if (adminId) where.assignTo = adminId;
      if (startDate && endDate) {
        startDate = this.typeService.getGlobalDate(startDate);
        endDate = this.typeService.getGlobalDate(endDate);
        where.verifiedDate = {
          [Op.gte]: startDate.toJSON(),
          [Op.lte]: endDate.toJSON(),
        };
      }
      let whereManual = {};
      if (status === '1') {
        where['manualVerification'] = { [Op.or]: ['1', '3'] };
      } else if (status == '2' || status == '4') {
        if (status == '2') where['loanStatus'] = 'Rejected';
        if (status == '2') where['manualVerification'] = '2';
        where = {
          ...where,
          [Op.or]: [
            {
              [Op.and]: [
                { remark: { [Op.ne]: 'Inactive User response' } },
                { remark: { [Op.ne]: 'Declined by user' } },
              ],
            },
            { remark: { [Op.eq]: null } },
          ],
        };
        where['declineId'] = { [Op.eq]: null };
        whereManual = { salaryVerification: { [Op.ne]: '2' } };
      } else if (status == '0') {
        userWhere.NextDateForApply = {
          [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
        };
        where['manualVerification'] = status;
      }
      const kycInclude: any = {
        attributes: ['id'],
        model: KYCEntity,
        where: {
          aadhaarStatus: { [Op.or]: ['1', '3'] },
          panStatus: { [Op.or]: ['1', '3'] },
        },
      };
      if (OPTIONAL_DOCS_REQUIRED)
        kycInclude.where.otherDocStatus = { [Op.or]: ['1', '3'] };

      const selfieInclude = {
        attributes: ['id', 'status'],
        model: UserSelfieEntity,
        where: {
          status: { [Op.or]: ['1', '3'] },
        },
      };
      const loanOptions = {
        distinct: true,
        where,
        include: [
          { model: PredictionEntity, attributes: ['reason'], required: false },
          {
            model: registeredUsers,
            attributes: ['id', 'fullName', 'phone', 'city', 'completedLoans'],
            where: userWhere,
            include: [kycInclude, selfieInclude],
          },
          {
            model: BankingEntity,
            attributes: [
              'id',
              'salary',
              'adminSalary',
              'disbursementIFSC',
              'salaryVerification',
              'assignedTo',
            ],
            where: whereManual,
          },
        ],
        order: [['updatedAt', 'DESC']],
      };

      if (status != '0' && download != 'true') {
        loanOptions['offset'] = ((page || 1) - 1) * PAGE_LIMIT;
        loanOptions['limit'] = PAGE_LIMIT;
      }
      return loanOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async preparDataForFinalVerification(data) {
    const finalData = [];
    try {
      for (let i = 0; i < data.length; i++) {
        try {
          const tmpData: any = {};
          const item = data[i];
          const user = item.registeredUsers ?? {};
          const bankingData = item.bankingData ?? {};
          bankingData.assignedAdminData =
            await this.commonSharedService.getAdminData(
              bankingData?.assignedTo,
            );
          const verification = this.typeService.getVerificationLastData(
            user?.verificationTrackerData,
          );
          const predictionData = item.predictionData ?? {};
          const predictionReason = predictionData.reason;
          if (predictionReason && (user?.completedLoans ?? 0) > 0) {
            const reasonData = JSON.parse(predictionReason);
            const firstElement = Object.keys(reasonData)[0];
            if (firstElement) {
              tmpData['predictionData'] = firstElement
                .replace(/_/g, ' ')
                .toUpperCase();
            }
          }
          tmpData['userId'] = user?.id;
          tmpData['loanId'] = item.id;
          tmpData['Waiting time'] = verification.waitingTime;
          tmpData['Difference in minutes'] = verification.minutes;
          tmpData['Assign'] =
            (await this.commonSharedService.getAdminData(item?.assignTo))
              .fullName ?? '-';
          tmpData['assignId'] =
            (await this.commonSharedService.getAdminData(item?.assignTo)).id ??
            '-';
          tmpData['Loan id'] = item.id;
          tmpData['Name'] = user?.fullName ?? '-';
          tmpData['Mobile number'] = this.cryptService.decryptPhone(user.phone);
          tmpData['Applied loan amount'] = item?.loanAmount ?? 0;
          tmpData['Approved loan amount'] = item?.netApprovedAmount ?? 0;
          tmpData['Salary'] = bankingData?.salary ?? 0;
          tmpData['City'] = user?.city ?? '-';
          tmpData['Completed loans'] = user?.completedLoans ?? 0;
          tmpData['Last action by'] =
            (
              await this.commonSharedService.getAdminData(
                item?.manualVerificationAcceptId,
              )
            )?.fullName ?? '-';
          tmpData['Last updated'] = this.typeService.dateToJsonStr(
            item?.verifiedDate ?? item?.updatedAt,
            'DD/MM/YYYY',
          );
          tmpData['Reject reason'] = item?.remark ?? '-';
          tmpData['Status'] = item?.manualVerification;
          const ifsc = bankingData?.disbursementIFSC;
          const isValidIfsc = await this.checkIfscCode(ifsc);
          if (!isValidIfsc) tmpData['ifscTagged'] = 'IFSC NOT VALID';
          finalData.push(tmpData);
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async sendNotificationBeforeLoanDecline() {
    try {
      const before7Day = new Date();
      before7Day.setDate(before7Day.getDate() - 7);
      const endDate = this.typeService.getUTCDate(before7Day.toString());
      const options = {
        where: {
          loanStatus: { [Op.or]: ['InProcess', 'Accepted'] },
          createdAt: { [Op.lte]: endDate },
        },
      };
      const attributes = ['id', 'userId', 'appType'];
      const loanData = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      //get templete id
      const tempOpt = { where: { subType: 'BEFORE_LOAN_DECLINE' } };
      const template = await this.templeteRepo.getRowWhereData(['id'], tempOpt);
      if (template === k500Error) return kInternalError;
      if (!template) return k422ErrorMessage(kNoTemplateFound);
      const data = { userData: loanData, id: template?.id, isMsgSent: true };
      return await this.sharedNotification.sendNotificationToUser(data);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region
  async getLoanHistory(query) {
    const userId = query.userId;
    if (!userId) return kParamMissing('userId');

    const data = await this.fetchAllData(userId);
    if (!Object.keys(data).length) return {};

    return await this.processLoanData(data, userId);
  }
  //#endregion

  //#region
  async fetchAllData(userId) {
    const loanData: any = await this.fetchLoanData(userId);
    if (!loanData?.length) return {};

    const loanIds = loanData.map((el) => el?.id);

    //get emi details
    const emiData = await this.fetchEmiData(loanIds);

    //get predictions details
    const predictionData = await this.commonSharedService.fetchPredictionData(
      loanIds,
    );

    //get hypothication history details
    const hypothecationData = await this.fetchHypothecationData(loanIds);

    //get master details
    const masterIds = loanData.map((el) => el?.masterId);
    const masterData = await this.fetchMasterData(masterIds);

    //get banking details
    const bankingIds = loanData.map((el) => el?.bankingId);
    const bankingData = await this.fetchBankingData(bankingIds);

    // Pre-fetch lender names for unique lender IDs
    const uniqueLenderIds = [
      ...new Set(hypothecationData.map((item) => item.lenderId)),
    ];

    //get emi details
    const lenderData = await this.fetchLenderData(uniqueLenderIds);

    return {
      loanData,
      emiData,
      predictionData,
      masterData,
      bankingData,
      lenderData,
      hypothecationData,
    };
  }
  //#endregion

  //#region Fetching and Preparing Data
  private async fetchLoanData(userId) {
    const attributes = [
      'followerId',
      'id',
      'createdAt',
      'emiSelection',
      'loan_disbursement_date',
      'qualityScore',
      'interestRate',
      'netApprovedAmount',
      'loanStatus',
      'purposeId',
      'approvedDuration',
      'loanAmount',
      'nocURL',
      'remark',
      'adminName',
      'esign_id',
      'userReasonDecline',
      'declineId',
      'loanRejectReason',
      'manualVerificationAcceptId',
      'netApprovalData',
      'manualVerification',
      'insuranceDetails',
      'isPartPayment',
      'verifiedSalaryDate',
      'cibilSystemFlag',
      'appType',
      'categoryTag',
      'isLoanClosure',
      'isLoanSettled',
      'cseRejectBy',
      'masterId',
      'bankingId',
      'penaltyCharges',
      'approvedLoanAmount',
      'eligibilityDetails',
    ];

    const options = {
      where: { userId },
      order: [['id', 'DESC']],
      useMaster: false,
    };
    const loanData = await this.loanRepo.getTableWhereData(attributes, options);
    if (loanData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return loanData;
  }
  //#endregion

  //#region Fetch EMI Data by Loan IDs
  private async fetchEmiData(loanIds) {
    const attributes = [
      'id',
      'emi_date',
      'waiver',
      'payment_due_status',
      'penalty_update_date',
      'paid_waiver',
      'unpaid_waiver',
      'penalty_days',
      'loanId',
    ];
    const options = {
      where: { loanId: loanIds },
      order: [['id', 'DESC']],
      useMaster: false,
    };

    const emiData = await this.repoManager.getTableWhereData(
      EmiEntity,
      attributes,
      options,
    );
    if (emiData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return emiData;
  }
  //#endregion

  //#region Fetch Hypothecation History by Loan IDs
  private async fetchHypothecationData(loanIds) {
    const attributes = [
      'assigned_date',
      'loanId',
      'unassigned_date',
      'lenderId',
    ];
    const options = {
      where: { loanId: loanIds },
      order: [['id', 'DESC']],
      useMaster: false,
    };

    const hypothecationData = await this.repoManager.getTableWhereData(
      HypothecationHistoryEntity,
      attributes,
      options,
    );
    if (hypothecationData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return hypothecationData;
  }
  //#endregion

  //#region Fetch Master Data by IDs
  private async fetchMasterData(masterIds) {
    const attributes = [
      'id',
      'loanId',
      'assignedCSE',
      'status',
      'dates',
      'pre_approve_status',
    ];
    const options = { where: { id: masterIds }, useMaster: false };

    const masterData = await this.masterRepo.getTableWhereData(
      attributes,
      options,
    );
    if (masterData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return masterData;
  }
  //#endregion

  //#region Fetch Banking Data by Loan IDs
  private async fetchBankingData(bankingIds) {
    const attributes = [
      'salaryVerification',
      'id',
      'nameMissMatchAdmin',
      'additionalNameApprovedBy',
      'salary',
      'loanId',
    ];
    const options = { where: { id: bankingIds }, useMaster: false };

    const bankingData = await this.repoManager.getTableWhereData(
      BankingEntity,
      attributes,
      options,
    );
    if (bankingData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return bankingData;
  }
  //#endregion

  //#region Fetch Hypothecation History by Loan IDs
  private async fetchLenderData(id) {
    const lenderData = await this.repoManager.getTableWhereData(
      HypothecationEntity,
      ['id', 'lenderName'],
      { where: { id }, useMaster: false },
    );
    if (lenderData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return lenderData;
  }
  //#endregion

  // Processing and Enriching Loan Data
  private async processLoanData(relatedData, userId) {
    const {
      emiData,
      predictionData,
      hypothecationData,
      masterData,
      bankingData,
      lenderData,
      loanData,
    } = relatedData;

    const response = [];
    for (let index = 0; index < loanData?.length; index++) {
      const element = loanData[index];

      const emi =
        emiData
          .filter((emi) => emi.loanId === element.id)
          .sort((a, b) => a.id - b.id) || [];

      const prediction =
        predictionData?.find((pred) => pred?.loanId === element.id) || {};

      const banking =
        bankingData?.find((bank) => bank?.id === element.bankingId) || {};

      const master = masterData.find(
        (masterRes) => masterRes.id == element?.masterId,
      );

      const hypoData = hypothecationData.filter(
        (hypo) => hypo.loanId === element.id,
      );

      if (banking?.nameMissMatchAdmin) {
        const adminData = await this.commonSharedService.getAdminData(
          banking?.nameMissMatchAdmin,
        );
        banking.nameMissMatchAdmin = adminData?.fullName ?? '-';
      }
      if (banking?.additionalNameApprovedBy) {
        const adminData = await this.commonSharedService.getAdminData(
          banking?.additionalNameApprovedBy,
        );
        banking.additionalNameApprovedBy = adminData?.fullName ?? '-';
      }

      ///check redis data of CSE
      let redisData = await this.redisService.get(
        'CSE_REJECT_LOAN' + element?.id,
      );
      if (redisData) redisData = JSON.parse(redisData);

      let remark = element?.remark;
      if (
        remark &&
        remark == BAD_CIBIL_SCORE_MSG &&
        element?.eligibilityDetails?.isEligible != true
      )
        remark = element?.eligibilityDetails?.messages?.checkEligible ?? remark;

      if (
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
        remark &&
        element?.cseRejectBy &&
        [
          LOAN_STATUS.Complete,
          LOAN_STATUS.Active,
          LOAN_STATUS.Rejected,
        ].includes(element?.loanStatus) &&
        EXCLUDE_CSE_LOAN_REJECT_REASON.includes(remark?.toLowerCase()?.trim())
      ) {
        remark = '-';
      }
      const creditAmount = element?.penaltyCharges?.creditUsed ?? 0;

      let riskCategory;
      if (element?.categoryTag || element?.categoryTag == 0)
        riskCategory = userCategoryTag[element?.categoryTag];
      else if (element?.predictionData?.categorizationTag)
        riskCategory = element?.predictionData?.categorizationTag?.slice(0, -5);
      else '-';

      const annumInterest =
        element?.eligibilityDetails?.annumInterest ??
        element?.eligibilityDetails?.anummIntrest;

      const loanAnnualRate = +annumInterest;
      const annualInterestRate = loanAnnualRate
        ? loanAnnualRate.toFixed(2)
        : (+element?.interestRate * 365).toFixed(2);

      const isPreApproveUser = master?.pre_approve_status;

      const obj: any = {
        followerId: element?.followerId,
        id: element?.id,
        createdAt: element?.createdAt
          ? this.dateService.readableDate(element?.createdAt, true)
          : '-',
        loan_disbursement_date: master?.dates?.disbursement
          ? this.dateService.readableDate(master?.dates?.disbursement, true)
          : '-',
        qualityScore: element?.qualityScore,
        interestRate: annualInterestRate ? annualInterestRate : '-',
        netApprovedAmount: +element?.netApprovedAmount || 0,
        adminApprovedAmount: element?.approvedLoanAmount
          ? +element?.approvedLoanAmount
          : '-',
        ///if cse reject loan due to user not responding at that time need to show loan as rejected to cse till disbursement is not initiated
        loanStatus:
          GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
          redisData &&
          element?.cseRejectBy &&
          master?.status?.disbursement < 0
            ? LOAN_STATUS.Rejected
            : element?.loanStatus,
        purposeId: element?.purposeId,
        approvedDuration: element?.approvedDuration,
        loanAmount: element?.loanAmount,
        nocURL: element?.nocURL,
        remark,
        adminName: element?.adminName,
        esign_id: element?.esign_id,
        userReasonDecline: element?.userReasonDecline,
        declineId: element?.declineId,
        loanRejectReason: element?.loanRejectReason,
        manualVerificationAcceptId: element?.manualVerificationAcceptId,
        netApprovalData: element?.netApprovalData,
        manualVerification: element?.manualVerification,
        penalty_days: this.typeService.getOverDueDay(emi),
        categoryTag: riskCategory,
        isLoanClosure: element?.isLoanClosure,
        isLoanSettled: element?.isLoanSettled,
        isPartPayment: element?.isPartPayment,
        verifiedSalaryDate: element?.verifiedSalaryDate,
        cibilSystemFlag: element?.cibilSystemFlag,
        app:
          element?.appType == 1
            ? EnvConfig.nbfc.nbfcShortName
            : EnvConfig.nbfc.appName,
        purpose:
          (await this.commonSharedService.fetchLoanPurpose(element?.purposeId))
            ?.purposeName ?? null,
        emiData: emi,
        ...(banking?.salary && { verifiedSalary: banking.salary }),
        bankingData: banking,
        lastActionBy:
          element.manualVerificationAcceptId === SYSTEM_ADMIN_ID
            ? 'System'
            : (
                await this.commonSharedService.getAdminData(
                  element.manualVerificationAcceptId,
                )
              )?.fullName ?? '-',
        assignedCSE: master?.assignedCSE
          ? (await this.commonSharedService.getAdminData(master.assignedCSE))
              ?.fullName ?? '-'
          : '-',
        creditAmount,
        preApproveUser:
          isPreApproveUser == PreApproveStatus.ELIGIBLE ? true : false,
      };
      if (obj?.verifiedSalary) {
        delete obj?.bankingData?.salary; // Remove salary from banking if it was used
        delete obj?.bankingData?.loanId;
      }
      // Add selectedEmiDate if emiSelection is available
      const emiSelection = element?.emiSelection ?? {};
      if (emiSelection?.selectedEmiDate)
        obj.selectedEmiDate = emiSelection?.selectedEmiDate;
      else obj.emiSelection = emiSelection;

      this.prepareHypothecationData(hypoData, lenderData, obj);

      // Prediction
      // ML Prediction
      if (prediction) this.processPredictionData(prediction, obj);

      // insurance details
      const insurance = element?.insuranceDetails;
      if (insurance) this.processInsuranceDetails(insurance, obj);

      // Follower details
      if (index == 0) {
        const followerData = await this.commonSharedService.getAdminData(
          element.followerId,
        );
        delete followerData?.email;
        obj.followerData = followerData;
        delete obj.followerId;
      }

      response.push(obj);
    }

    const lastCrm = await this.getLastCrm(userId);

    return { loanData: response, lastCrm };
  }

  //#region
  private async getLastCrm(userId) {
    ///get crm details
    const data = await this.getLastCrmData(userId);
    if (!data) return {};

    ///prepare crm data
    return await this.prepareLastCrmData(data);
  }
  //#endregion

  //#region
  private async getLastCrmData(userId) {
    const options: any = {
      where: {
        userId,
      },
      order: [['createdAt', 'DESC']],
    };
    // if (!loanData?.length) options.where.userId = userId;
    // else options.where.loanId = loanData[0].id;
    const attributes = [
      'id',
      'categoryId',
      'adminId',
      'remark',
      'createdAt',
      'reason',
      'loanId',
      'relationData',
    ];
    const lastCrmData = await this.crmRepo.getRowWhereData(attributes, options);
    if (lastCrmData == k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return lastCrmData;
  }
  //#endregion

  //#region
  private async prepareLastCrmData(lastCrmData) {
    const adminData = await this.commonSharedService.getAdminData(
      lastCrmData?.adminId,
    );

    const departmentData = await this.commonSharedService.getDepartment(
      adminData?.departmentId,
    );

    return {
      Title: lastCrmData?.relationData?.statusName ?? '-',
      Category: crmTypeTitle[lastCrmData?.categoryId] ?? '-',
      Description: lastCrmData?.remark ?? '-',
      'Loan Id': lastCrmData?.loanId ?? '-',
      'Action By': adminData?.fullName ?? '-',
      Department: departmentData?.department ?? '-',
      Reason: lastCrmData?.crmReasonData ?? '-',
      'Date & Time':
        lastCrmData?.createdAt || lastCrmData?.createdAt != ''
          ? this.dateService.readableDate(lastCrmData?.createdAt)
          : '-',
    };
  }
  //#endregion

  //#region Prepare Hypothecation data
  prepareHypothecationData(hypoData, lenderData, obj) {
    obj.lenderName = '-';
    obj.hypothecationMonths = '-';
    if (hypoData.length > 0) {
      const HypoDate = [];
      const latestLender = hypoData[0]?.lenderId;
      for (let index = 0; index < hypoData.length; index++) {
        const currentItem = hypoData[index];
        if (latestLender != currentItem?.lenderId) continue;
        const newHypoDate = this.typeService.getMonthAndYear(
          new Date(currentItem?.assigned_date),
        );
        HypoDate.push(newHypoDate);
      }
      const lender = lenderData.find((item) => item.id === latestLender);
      obj.lenderName = `${lender.lenderName}:${HypoDate[0]}`;
      HypoDate.shift();
      obj.hypothecationMonths = HypoDate;
    }
  }
  //#endregion

  //#region process prediction data
  processPredictionData(prediction, obj) {
    obj.CFLScore = prediction?.CFLScore ?? '-';
    if (prediction?.categorizationDetails) {
      const categorizationData = prediction?.categorizationDetails ?? {};
      obj.userCategoryTag =
        userCategoryTag[prediction?.categorizationDetails.category] ?? '';
      const userCategoryData: any = {};
      let totalScore = 0;

      for (const key in categorizationData) {
        try {
          const value = categorizationData[key];
          if (key !== 'totalValue') userCategoryData[key] = value?.value;
          else totalScore = value;
        } catch (error) {}
      }

      userCategoryData.totalScore = totalScore;
      obj.userCategoryData = userCategoryData;
    }

    const automationDetails = prediction?.automationDetails ?? {};
    if (automationDetails?.model_version) {
      delete automationDetails.feedData;
      obj.predictionDetails = automationDetails;
    }

    const ml_approval = prediction?.ml_approval ?? {};
    if (ml_approval?.model_version) {
      delete ml_approval.feedData;
      obj.ml_approval = ml_approval;
    }
  }
  //#endregion

  //#region process insurance details
  processInsuranceDetails(insurance, obj) {
    obj.insuranceDetails = {
      ...insurance,
      planAPremium: this.typeService.manageAmount(insurance?.planAPremium ?? 0),
      planBPremium: this.typeService.manageAmount(insurance?.planBPremium ?? 0),
      planCPremium: this.typeService.manageAmount(insurance?.planCPremium ?? 0),
      totalPremium: this.typeService.manageAmount(insurance?.totalPremium ?? 0),
    };
  }
  //#endregion
}

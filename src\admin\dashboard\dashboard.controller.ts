// Imports
import {
  kInternalError,
  kParamsMissing,
  kSuccessData,
} from 'src/constants/responses';
import { Key } from 'src/authentication/auth.guard';
import { ContactSharedService } from 'src/shared/contact.service';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { DashboardService } from 'src/admin/dashboard/dashboard.service';
import {
  Controller,
  Get,
  Query,
  Res,
  Req,
  Body,
  Post,
  UploadedFile,
  UseInterceptors,
  UploadedFiles,
  Headers,
} from '@nestjs/common';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { ErrorContextService } from 'src/utils/error.context.service';
@Controller('admin/dashboard')
export class DashboardController {
  constructor(
    private readonly service: DashboardService,
    private readonly errorContextService: ErrorContextService,
    private readonly sharedService: ContactSharedService,
    private readonly commonSharedService: CommonSharedService,
  ) {}

  //#region getting all stamp details
  @Get('getAllStampDetails')
  async funAllStampDetails(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.fetchAllStampDetails(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  //#endregion

  //#region getting user's location history
  @Get('getLocationHistory')
  async funGetLocationHistory(@Query() query, @Res() res) {
    try {
      if (!query.userId) return res.json(kParamsMissing);
      const result = await this.service.funGetLocationHistory(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //#region all Disbursed Loans
  @Get('allDisbursedLoans')
  async allDisbursedLoans(@Query() query, @Res() res, @Headers() headers) {
    try {
      query.hAdmin = headers['adminid'];
      const data: any = await this.service.allDisbursedLoans(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
  //#endregion

  //#region  getStampCount
  @Get('getStampCount')
  async funGetStampCount(@Res() res) {
    try {
      const data: any = await this.service.getStampCount();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getContactData')
  async getUniqueContactData(@Res() res, @Req() req, @Query() query) {
    try {
      query.token = req.headers['access-token'] ?? '';
      const result: any = await this.sharedService.getUniqueContacts(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //#region get active part pay details
  @Get('getActivePartPaymentDetails')
  async funGetActivePartPaymentDetails(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getActivePartPaymentDetails(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data: data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  //#endregion

  // #region get Repaid CardData
  @Get('getRepaidCardData')
  async funGetRepaidCardData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getRepaidCardData();
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getAllChatDocumentByUserId')
  async funAllChatDocumentByUserId(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.fetchAllChatDocumentByUserId(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getAllDocumentsLoanWise')
  async funAllDocumentsLoanWise(@Query() query, @Res() res) {
    try {
      const data = await this.service.fetchAllDocumentsLoanWise(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  //#endregion

  @Get('getAllUploadDocList')
  async funGetAllUploadList(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getAllUploadList(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('/getPaymentData')
  async funGetPaymentData(@Res() res) {
    try {
      const data: any = await this.service.funGetPaymentData();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data: data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
  //#endregion

  //#region get chat count
  @Get('getChatCount')
  async getChatCount(@Res() res) {
    try {
      const data: any = await this.service.getChatCount();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // get 15 Days Count And Amount Emi Data
  @Get('get15DaysCountAndAmountEmiData')
  async funGet15DaysCountAndAmountEmiData(@Res() res) {
    try {
      const data: any = await this.service.get15DaysCountAndAmountEmiData();
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data: data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
  //#endregion

  @Get('getAllDeviceByUserId')
  async funGetAllDeviceByUserId(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getAllDeviceByUserId(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kParamsMissing);
    }
  }
  // UI PATH : USERS-->customerDetails
  @Get('getNetBankingDetails')
  async funGetNetBankingDetails(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getLoanDataForNetBanking(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // Loan History Counts
  @Get('getLoanHistoryCounts')
  async funGetLoanHistoryCounts(@Key() userId, @Query() query, @Res() res) {
    try {
      userId = userId ?? query?.userId;
      if (!userId) return res.json(kParamsMissing);
      const data: any = await this.service.getLoanTransactionService(userId);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //  get dashboard static data api
  @Get('getDashboardData')
  async funGetDashboardData(@Res() res) {
    try {
      const data = await this.service.getDashboardData();
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // Caching API ( every 3 hours )
  // Dependency of Endpoint - getDashboardData
  @Get('aumDetails')
  async funAumDetails(@Res() res) {
    try {
      const data = await this.service.aumDetails();
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#region batch file upload
  @Post('uploadBatchFile')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async funUploadBatchFile(@UploadedFile() file, @Body() body, @Res() res) {
    try {
      body.file = file;
      const data: any = await this.service.funUploadBatchFile(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data: data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('bankBalance')
  async funBankBalance(@Query() query, @Res() res) {
    try {
      const data = await this.service.bankBalance(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('autoDebitScreenshots')
  async autoDebitScreenshots(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.autoDebitScreenshots(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Get('razorPayBalance')
  async razorPayBalance(@Res() res) {
    try {
      const data: any = await this.service.razorpayBalance();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('insuranceBalance')
  async insuranceBalance(@Res() res) {
    try {
      const data: any = await this.service.insuranceBalance();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getCollectionDocuments')
  async funCollectionDocuments(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getCollectionDocuments(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  @Get('dailyQualityParamCheck')
  async qualityCheck(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.dailyQualityParamCheck(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('dailyBalanceAlert')
  async funDailyBalanceAlert(@Res() res) {
    try {
      const data: any = await this.service.dailyBalanceAlert();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  @Get('fetchUPIPaymentAuditData')
  async getUPIPaymentAudit(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.fetchUPIPaymentAuditData(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  @Get('funLenderEmiReminder')
  async funLenderEmiReminder(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.funLenderEmiReminder(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('findMaskRole')
  async getMaskRole(@Query() query, @Res() res) {
    try {
      const result = await this.commonSharedService.findMaskRole(
        query?.adminId,
        query?.isRefresh,
        query?.needData,
      );
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
